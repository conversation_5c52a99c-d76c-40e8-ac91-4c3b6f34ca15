'use client';

import Flex from 'shared/uikit/Flex';
import dynamic from 'next/dynamic';
import Skeleton from 'shared/uikit/Skeleton';
import Loading from '../shared/components/molecules/VisualsSlider/loading';
import LandingLayout from '../shared/components/layouts/LandingLayout/LandingLayout.component';
import classes from './layout.module.scss';

const VisualsSlider = dynamic(
  () => import('../shared/components/molecules/VisualsSlider'),
  { ssr: false, loading: () => <Loading /> }
);

const AuthFooter = dynamic(
  () => import('shared/uikit/Footer/AuthFooter.component'),
  { ssr: false, loading: () => <Skeleton className={classes.loadingFooter} /> }
);

export interface LayoutProps {
  children?: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  return (
    <LandingLayout>
      <Flex className={classes.authLayoutRoot}>
        <Flex className={classes.left}>
          <VisualsSlider />
        </Flex>
        <Flex className={classes.right}>
          <Flex className={classes.childrenWrapper}>{children}</Flex>
          <Flex className={classes.footerWrapper}>
            <AuthFooter />
          </Flex>
        </Flex>
      </Flex>
    </LandingLayout>
  );
}
