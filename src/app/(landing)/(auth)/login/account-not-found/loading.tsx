'use client';

import Skeleton from 'shared/uikit/Skeleton';
import Flex from 'shared/uikit/Flex';
import Divider from 'shared/uikit/Divider';

const Loading = (): JSX.Element => (
  <Flex className="flex flex-col">
    <Skeleton className="h-32 w-full" />
    <Divider className="mt-12 sm:mt-16" />
    <Skeleton className="h-48 w-full mt-24 sm:mt-20" />
    <Skeleton className="h-48 w-full mt-8 sm:mt-12" />
  </Flex>
);

export default Loading;
