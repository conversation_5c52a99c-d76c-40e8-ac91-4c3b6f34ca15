'use client';

import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Typography from '@shared/uikit/Typography';
import Button from '@shared/uikit/Button';
import { landingRouteNames } from '@shared/utils/constants';
import AuthFormLayout from '../../../shared/components/layouts/AuthFormLayout';

const AccountNotFound = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <AuthFormLayout title={t('account_not_found')}>
      <Typography>{t('w_c_f_an_acc_t_email_p_c')}</Typography>
      <Button
        variant="xLarge"
        schema="primary-blue"
        className="mt-16_20"
        label={t('signup')}
        to={landingRouteNames.signup}
      />
    </AuthFormLayout>
  );
};

export default AccountNotFound;
