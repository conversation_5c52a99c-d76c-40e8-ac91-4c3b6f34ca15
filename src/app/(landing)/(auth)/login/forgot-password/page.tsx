'use client';

import React, { useState } from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import Form from 'shared/uikit/Form';
import AlertMessage from 'shared/uikit/AlertMessage';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { emailValidation2 } from 'shared/utils/toolkit/validationSchema';
import { Endpoints } from 'shared/utils/constants';
import AuthFormLayout from '../../../shared/components/layouts/AuthFormLayout';
import Success from '../partials/code';
import classes from './page.module.scss';

const ForgetPassword = (): JSX.Element => {
  const { t } = useTranslation();
  const [email, setEmail] = useState();
  const initialValues = { email: '' };
  const onSuccess = (_: any, value: any) => {
    setEmail(value?.email);
  };

  const onFailure = ({ error, form }: any) => {
    const errorCode = error?.response?.data?.error;
    if (errorCode === 'EmailNotFoundException') {
      form?.setErrors({ email: errorCode });
    }
    if (errorCode === 'RateLimiterExceedException') {
      form.setErrors({ rateLimit: true });
    }
  };

  if (email) {
    return <Success setEmail={setEmail} email={email} />;
  }

  return (
    <AuthFormLayout title={t('reset_password')}>
      <Form
        initialValues={initialValues}
        url={Endpoints.Auth.postForgetPassword}
        onSuccess={onSuccess}
        validationSchema={emailValidation2(t)}
        customErrorHandler={onFailure}
        submitWithEnter
      >
        {({ errors }) => (
          <>
            <DynamicFormBuilder
              groups={[
                {
                  name: 'email',
                  cp: 'input',
                  label: t('email'),
                  trim: true,
                  required: true,
                },
              ]}
            />
            {errors && errors.rateLimit && (
              <AlertMessage
                type="error"
                title={t('reset_pass_error')}
                subTitle={t('to_may_reset_pass_att_p_t_again')}
                className={classes.submitButton}
              />
            )}
            <SubmitButton
              variant="xLarge"
              schema="primary-blue"
              className={classes.submitButton}
              label={t('reset_password')}
            />
          </>
        )}
      </Form>
    </AuthFormLayout>
  );
};

export default ForgetPassword;
