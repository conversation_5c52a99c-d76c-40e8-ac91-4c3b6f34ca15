'use client';

import { useEffect } from 'react';
import {
  mainRoutes,
  landingRouteNames,
} from 'shared/utils/constants/routeNames';
import useHistory from 'shared/utils/hooks/useHistory';
import useOnSuccessLogin from 'shared/utils/hooks/useOnSuccessLogin';
import { useRouter } from 'next/navigation';
import clearCookies from '../../../shared/utils/clearCookies';

const SocialAuthentication = (): null => {
  const history = useHistory();
  const onSuccessLogin = useOnSuccessLogin();
  const router = useRouter();
  router.prefetch(`/${mainRoutes.home}`);

  useEffect(() => {
    const search = typeof window !== 'undefined' ? window.location.search : '';
    const params = new URLSearchParams(search);

    const accessToken = params.get('accessToken');
    const refreshToken = params.get('refreshToken');
    const signUpCompleted = params.get('signUpCompleted') === 'true';
    const userId = params.get('userId');
    const username = params.get('username');

    if (accessToken) {
      clearCookies();
      onSuccessLogin({
        accessToken,
        refreshToken,
        signUpCompleted,
        userId,
        username,
      });
    } else {
      clearCookies();
      history.push(landingRouteNames.signup);
    }
  }, []);

  return null;
};

export default SocialAuthentication;
