'use client';

import React, { useState } from 'react';
import Typography from 'shared/uikit/Typography';
import Form from 'shared/uikit/Form';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Flex from 'shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import IconButton from 'shared/uikit/Button/IconButton';
import useToast from 'shared/uikit/Toast/useToast';
import Endpoints from '@shared/utils/constants/endpoints';
import AuthFormLayout from '../../../shared/components/layouts/AuthFormLayout';
import classes from './page.module.scss';

const GetCode = ({ email, setEmail }): JSX.Element => {
  const { t } = useTranslation();
  const toast = useToast();
  const [isSent, setIsSent] = useState(false);

  const onSuccess = () => {
    toast({
      type: 'success',
      icon: 'check-circle',
      message: 'forget_pass_email_s_a',
    });
    setIsSent(true);
  };

  return (
    <AuthFormLayout title={t('forget_password')}>
      <Flex>
        <Flex flexDir="row">
          <IconButton name="check-circle" colorSchema="success" size="sm20" />
          <Typography
            ml={8}
            height={24}
            size={20}
            color="smoke_coal"
            font="700"
          >
            {t('email_sent')}
          </Typography>
        </Flex>
        <Typography className="responsive-margin-top">
          {t('y_c_r_y_pass')}
        </Typography>
      </Flex>
      <Flex className={classes.emailWrapper}>
        <Typography size={15} height={21} font="700">
          {email}
        </Typography>
        <BaseButton onClick={() => setEmail(null)}>
          <Typography size={15} height={21} font="700" color="brand">
            {t('wrong_email')}
          </Typography>
        </BaseButton>
      </Flex>
      <Flex className={classes.resendWrap}>
        <Typography>{t('mk_s_t_c_t_s_j_any_e')}</Typography>
        <Form
          initialValues={{ email }}
          onSuccess={onSuccess}
          url={Endpoints.Auth.postResendEmail}
        >
          {({ handleSubmit, isSubmitting }: any) => (
            <BaseButton
              onClick={handleSubmit}
              disabled={isSubmitting || isSent}
            >
              <Typography color="brand" className={classes.clickToResend}>
                {t('click_resend')}
              </Typography>
            </BaseButton>
          )}
        </Form>
      </Flex>
    </AuthFormLayout>
  );
};

export default GetCode;
