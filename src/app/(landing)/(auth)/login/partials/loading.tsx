'use client';

import React from 'react';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './loading.module.scss';

import AuthFormLayout from '../../../shared/components/layouts/AuthFormLayout';

export default function Loading() {
  const { t } = useTranslation();

  return (
    <AuthFormLayout title={t('login')}>
      <Skeleton className={classes.skeleton} />
      <Skeleton className={classes.skeleton} />
      <Skeleton className={classes.skeleton} />
    </AuthFormLayout>
  );
}
