'use client';

import React from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import Form from 'shared/uikit/Form';
import AlertMessage from 'shared/uikit/AlertMessage';
import Cookies from 'shared/utils/toolkit/cookies';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useHistory from 'shared/utils/hooks/useHistory';
import { resetPassWordValidation } from 'shared/utils/toolkit/validationSchema';
import useParams from 'shared/utils/hooks/useParams';
import useGetDeviceId from 'shared/utils/hooks/useGetDeviceId';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import { Endpoints } from 'shared/utils/constants';
import classes from './page.module.scss';
import AuthFormLayout from '../../../../shared/components/layouts/AuthFormLayout';

interface InitialValues {
  password: string;
  cPassword: string;
  resetPasswordCode: string;
  email: string;
}

const ResetPassword = (): JSX.Element => {
  const history = useHistory();
  const { deviceId } = useGetDeviceId();
  const { slug = [] } = useParams();
  const email = decodeURIComponent(slug?.[0]);
  const resetPasswordCode = slug?.[1];
  const { t } = useTranslation();

  const initialValues: InitialValues = {
    password: '',
    cPassword: '',
    resetPasswordCode,
    email,
  };

  const { mutate: resetPasswordHandler } = useReactMutation({
    url: Endpoints.Auth.postForgetPasswordConfirm,
  });

  const onSuccessHandler = async (variables: any, formRef: any) => {
    resetPasswordHandler(
      {
        ...variables,
        deviceId,
      },
      {
        onSuccess: () => {
          const DEVICE_ID = getCookieKey('deviceId');
          Cookies.set(DEVICE_ID, deviceId as string);
          history.push(landingRouteNames.login);
        },
        onError: (error: any) => {
          const errorCode = error?.response?.data?.error;
          if (errorCode === 'RepetitivePasswordException') {
            formRef.setErrors({
              api: true,
              repetitive: true,
            });
          } else {
            formRef.setErrors({
              api: true,
              repetitive: false,
            });
          }
        },
      }
    );
  };

  return (
    <AuthFormLayout title={t('set_new_pass')}>
      <Form
        initialValues={initialValues}
        validationSchema={resetPassWordValidation(t)}
        onSuccess={onSuccessHandler}
        local
        submitWithEnter
      >
        {({ errors }: any) => (
          <>
            <DynamicFormBuilder
              groups={[
                {
                  name: 'password',
                  cp: 'input',
                  label: t('new_password'),
                  showStrength: true,
                  type: 'password',
                  keyboardType: 'visible-password',
                  required: true,
                },
                {
                  name: 'cPassword',
                  cp: 'input',
                  label: t('confirm_password'),
                  type: 'password',
                  wrapStyle: 'responsive-margin-top',
                  keyboardType: 'visible-password',
                  required: true,
                },
              ]}
            />
            {errors && errors.api && (
              <AlertMessage
                type="error"
                title={t('reset_pass_err_title')}
                subTitle={
                  errors.repetitive
                    ? t('old_new_pass')
                    : t('reset_pass_err_sub')
                }
                className={classes.submitButton}
              />
            )}
            <SubmitButton
              variant="xLarge"
              schema="primary-blue"
              className={classes.submitButton}
              label={t('reset_password')}
              style={classes.submitButton}
            />
          </>
        )}
      </Form>
    </AuthFormLayout>
  );
};

export default ResetPassword;
