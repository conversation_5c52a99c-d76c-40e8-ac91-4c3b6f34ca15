'use client';

import React from 'react';
import TwoFactorAuthGetCodeForm from 'shared/uikit/TwoFactorAuthGetCodeForm';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import useHistory from 'shared/utils/hooks/useHistory';
import useOnSuccessLogin from '@shared/utils/hooks/useOnSuccessLogin';
import AuthFormLayout from '../../../shared/components/layouts/AuthFormLayout';

const TwoFactorAuthGetCode = (): JSX.Element => {
  const { t } = useTranslation();
  const history = useHistory();
  const onSuccessLogin = useOnSuccessLogin();

  const onUseBackupCodeClick = () =>
    history.push(landingRouteNames.twoFactorAuthBackupCode);

  const onCancelClick = () => history.goBack();

  return (
    <AuthFormLayout title={t('two_fac_auth')}>
      <TwoFactorAuthGetCodeForm
        onSuccess={onSuccessLogin}
        onUseBackupCodeClick={onUseBackupCodeClick}
        onCancelClick={onCancelClick}
      />
    </AuthFormLayout>
  );
};

export default TwoFactorAuthGetCode;
