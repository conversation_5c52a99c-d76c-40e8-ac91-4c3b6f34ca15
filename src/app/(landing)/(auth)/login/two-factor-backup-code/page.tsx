'use client';

import React from 'react';
import TwoFactorAuthBackupCodeForm from 'shared/uikit/TwoFactorAuthBackupCodeForm';
import { mainRoutes } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useHistory from 'shared/utils/hooks/useHistory';
import { useRouter } from 'next/navigation';
import useOnSuccessLogin from '../../../shared/hooks/useOnSuccessLogin';
import AuthFormLayout from '../../../shared/components/layouts/AuthFormLayout';

const TwoFactorAuthBackupCode = (): JSX.Element => {
  const { t } = useTranslation();
  const onSuccessLogin = useOnSuccessLogin();
  const history = useHistory();
  const onCancelClick = () => history.goBack();
  const router = useRouter();
  router.prefetch(`/${mainRoutes.home}`);

  return (
    <AuthFormLayout title={t('use_bkup_code')}>
      <TwoFactorAuthBackupCodeForm
        onCancelClick={onCancelClick}
        onSuccess={onSuccessLogin}
      />
    </AuthFormLayout>
  );
};

export default TwoFactorAuthBackupCode;
