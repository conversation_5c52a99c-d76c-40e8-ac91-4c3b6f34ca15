'use client';

import React, { useState } from 'react';
import Typography from 'shared/uikit/Typography';
import Form from 'shared/uikit/Form';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Flex from 'shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import landingEndpoints from 'shared/utils/constants/servicesEndpoints/services/landing';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import classes from './page.module.scss';
import IconButton from 'shared/uikit/Button/IconButton';
import AuthFormLayout from '../../../shared/components/layouts/AuthFormLayout';
import DividerTitled from 'shared/uikit/Divider/DividerTitled';
import cnj from 'shared/uikit/utils/cnj';
import Button from 'shared/uikit/Button';
import useToast from 'shared/uikit/Toast/useToast';

const GetCode = ({ email, setEmail }): JSX.Element => {
  const { t } = useTranslation();
  const toast = useToast();
  const [isSent, setIsSent] = useState(false);

  const onSuccess = () => {
    toast({
      type: 'success',
      icon: 'check-circle',
      message: 'reset_pass_email_s_a',
    });
    setIsSent(true);
  };

  return (
    <AuthFormLayout title={t('email_verification')}>
      <Flex>
        <Flex flexDir="row">
          <IconButton name="check-circle" colorSchema="success" size="sm20" />
          <Typography
            ml={8}
            height={24}
            size={20}
            color="smoke_coal"
            font="700"
          >
            {t('verification_sent')}
          </Typography>
        </Flex>
        <Typography className="responsive-margin-top">
          {t('confirm_t_e_y_u')}
        </Typography>
      </Flex>
      <Flex className={classes.emailWrapper}>
        <Typography size={15} height={21} font="700">
          {email}
        </Typography>
        <BaseButton onClick={() => setEmail(null)}>
          <Typography size={15} height={21} font="700" color="brand">
            {t('wrong_email')}
          </Typography>
        </BaseButton>
      </Flex>
      <Flex className={classes.resendWrap}>
        <Typography color="secondaryDisabledText" size={15} height={21}>
          {t('mk_s_t_c_t_s_j_any_e')}
        </Typography>
        <Form
          initialValues={{ email }}
          onSuccess={onSuccess}
          url={landingEndpoints.Auth.postResendEmail}
          enableReinitialize
        >
          {({ handleSubmit, isSubmitting }: any) => (
            <BaseButton
              onClick={handleSubmit}
              disabled={isSubmitting || isSent}
            >
              <Typography color="brand" className={classes.clickToResend}>
                {t('click_resend')}
              </Typography>
            </BaseButton>
          )}
        </Form>
      </Flex>
      <DividerTitled
        label={t('h_an_acc')}
        className={cnj(classes.haveAccount)}
      />
      <Button
        variant="xLarge"
        fullWidth
        schema="semi-transparent"
        className={classes.borderRadius}
        label={t('login_cap')}
        to={landingRouteNames.login}
      />
    </AuthFormLayout>
  );
};

export default GetCode;
