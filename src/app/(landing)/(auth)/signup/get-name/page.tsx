'use client';

import React from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Form from 'shared/uikit/Form';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import useTranslation from 'shared/utils/hooks/useTranslation';
import landingEndpoints from 'shared/utils/constants/servicesEndpoints/services/landing';
import geoApi from 'shared/utils/api/geo';
import useSignUpRedirections from 'shared/utils/hooks/useSignUpRedirections';
import formValidator from 'shared/utils/form/formValidator';
import { signupCompletion } from 'shared/utils/api/auth';
import Cookies from 'shared/utils/toolkit/cookies';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import classes from './GetName.module.scss';
import clearCookies from '../../../shared/utils/clearCookies';
import AuthFormLayout from '../../../shared/components/layouts/AuthFormLayout';

interface InitialValues {
  name: string;
  surname: string;
  occupation: string;
}

const GetName = (): JSX.Element => {
  const { t } = useTranslation();
  const { getRedirectUrl, redirect } = useSignUpRedirections();
  const NOT_COMPLETED_SIGNUP_DATA = getCookieKey('notSignupCompletedData');
  const OBJ_TOKEN = getCookieKey('userObjToken');
  const tokens = Cookies.get(NOT_COMPLETED_SIGNUP_DATA);
  const authTokens = tokens ? JSON.parse(tokens) : {};

  const initialValues: InitialValues = {
    name: '',
    surname: '',
    occupation: '',
  };

  const onSuccess = (data: any) => {
    const { username } = data;
    Cookies.set(OBJ_TOKEN, { ...authTokens, username });
    clearCookies();
    redirect(getRedirectUrl() || landingRouteNames.welcome);
  };

  const normalizer = (response: any) =>
    response &&
    response
      .map(({ id, title }: any) => ({
        value: id,
        label: title,
      }))
      ?.slice(0, 10);

  return (
    <AuthFormLayout title={t('personal_ino')}>
      <Form
        submitWithEnter
        initialValues={initialValues}
        transform={({ location, occupation, name, surname }: any) => ({
          accessToken: authTokens?.accessToken,
          name: name?.trim(),
          surname: surname?.trim(),
          location,
          occupationId: occupation?.value,
          occupationName: occupation?.label,
        })}
        apiFunc={signupCompletion}
        onSuccess={onSuccess}
        validationSchema={formValidator.object().shape({
          location: formValidator
            .object()
            .test('value', 'select_one_of_sug_cites', (val) => val?.value),
        })}
      >
        {() => (
          <>
            <DynamicFormBuilder
              groups={[
                {
                  name: 'name',
                  cp: 'input',
                  maxLength: 30,
                  label: t('name'),
                  required: true,
                },
                {
                  name: 'surname',
                  cp: 'input',
                  maxLength: 30,
                  label: t('last_name'),
                  wrapStyle: 'responsive-margin-top',
                  required: true,
                },
                {
                  name: 'occupation',
                  cp: 'asyncAutoComplete',
                  label: t('occupation'),
                  wrapStyle: 'responsive-margin-top',
                  url: landingEndpoints.Common.getOccupations,
                  required: true,
                  visibleRightIcon: true,
                  rightIconProps: { name: 'search' },
                  normalizer,
                },
                {
                  name: 'location',
                  apiFunc: geoApi.suggestCity,
                  cp: 'asyncAutoCompleteWithExtraParams',
                  label: t('location'),
                  wrapStyle: 'responsive-margin-top',
                  required: true,
                  visibleRightIcon: true,
                  rightIconProps: { name: 'search' },
                  accessToken: authTokens?.accessToken,
                },
              ]}
            />
            <SubmitButton
              className={classes.submitButton}
              variant="large"
              label={t('signup')}
              schema="primary-blue"
              fullWidth
            />
          </>
        )}
      </Form>
    </AuthFormLayout>
  );
};

export default GetName;
