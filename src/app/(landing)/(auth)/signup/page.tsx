'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Cookies from 'shared/utils/toolkit/cookies';
import { getXEmailValidation } from 'shared/utils/toolkit/validationSchema';
import { postSignup } from 'shared/utils/api/auth';
import useLocation from 'shared/utils/hooks/useLocation';
import useGetDeviceId from 'shared/utils/hooks/useGetDeviceId';
import useOnSuccessLogin from 'shared/utils/hooks/useOnSuccessLogin';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import {
  landingRouteNames,
  mainRoutes,
} from 'shared/utils/constants/routeNames';
import useSignUpRedirections from 'shared/utils/hooks/useSignUpRedirections';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import DividerTitled from 'shared/uikit/Divider/DividerTitled';
import cnj from 'shared/uikit/utils/cnj';
import Button from 'shared/uikit/Button';
import Form from 'shared/uikit/Form';
import { usePrefetchRoutes } from 'shared/hooks/usePrefetchRoutes';
import { useReferralCode } from 'shared/hooks/useReferralCode';
import Endpoints from 'shared/utils/constants/endpoints';
import EmailVerification from './code/page';
import AuthFormLayout from '../../shared/components/layouts/AuthFormLayout';
import { INVITED_BY_KEY } from '../../shared/constants/enums';
import classes from './page.module.scss';

const SignUp = (): JSX.Element => {
  const { t } = useTranslation();
  const { preserveSearchParams } = useLocation();
  const { query }: any = useRouter();
  const searchClass = new URLSearchParams(query);
  const invitedBy = searchClass.get('invitedBy');

  if (invitedBy) {
    Cookies.set(INVITED_BY_KEY, invitedBy);
  }
  const router = useRouter();
  const { deviceId } = useGetDeviceId();
  const onSuccessLogin = useOnSuccessLogin();
  const [email, setEmail] = useState();

  usePrefetchRoutes([`/${mainRoutes.home}`]);

  const initialValues = {
    xemail: '',
    xpassword: '',
  };
  const { mutate } = useReactMutation({ url: Endpoints.Auth.postLogin });
  const { saveRedirectUrl } = useSignUpRedirections();
  const location = useLocation();
  useReferralCode();

  useEffect(() => {
    router.prefetch(landingRouteNames.code);
  }, [router]);

  const onSuccess = ({ email }: any) => {
    setEmail(email);
  };

  const onFailure = async ({ variables }: any) =>
    new Promise((resolve) => {
      const DEVICE_ID = getCookieKey('deviceId');
      mutate(
        {
          email: variables.xemail,
          password: variables.xpassword,
          deviceId,
        },
        {
          onSuccess: (response: { data: any }) => {
            Cookies.set(DEVICE_ID, deviceId);
            onSuccessLogin(response?.data);
          },
          onError: resolve,
        }
      );
    });

  const withSaveRedirectUrl: Function =
    (callback: any) => async (args: any) => {
      if (!location?.pathname?.includes(landingRouteNames.signup)) {
        saveRedirectUrl(window?.location?.href);
      }
      return await callback(args);
    };
  if (email) {
    return <EmailVerification setEmail={setEmail} email={email} />;
  }

  return (
    <AuthFormLayout title={t('signup')}>
      <Form
        validationSchema={getXEmailValidation(t)}
        initialValues={initialValues}
        onSuccess={onSuccess}
        apiFunc={withSaveRedirectUrl(postSignup)}
        customErrorHandler={onFailure}
        customXNames={['email', 'password']}
        submitWithEnter
      >
        {() => (
          <>
            <DynamicFormBuilder
              groups={[
                {
                  name: 'xemail',
                  cp: 'input',
                  label: t('email'),
                  trim: true,
                  autoComplete: 'new-password',
                  required: true,
                },
                {
                  name: 'xpassword',
                  cp: 'input',
                  label: t('password'),
                  type: 'password',
                  keyboardType: 'visible-password',
                  showStrength: true,
                  autoComplete: 'new-password',
                  required: true,
                  wrapStyle: 'responsive-margin-top',
                },
              ]}
            />

            <SubmitButton
              variant="xLarge"
              schema="primary-blue"
              className={classes.submitButton}
              label={t('signup_cap')}
            />
            <DividerTitled
              label={t('h_an_acc')}
              className={cnj(classes.haveAccount)}
            />

            <Button
              variant="xLarge"
              fullWidth
              schema="semi-transparent"
              className={classes.borderRadius}
              label={t('login_cap')}
              to={preserveSearchParams(landingRouteNames.login)}
            />
          </>
        )}
      </Form>
    </AuthFormLayout>
  );
};

export default SignUp;
