'use client';

import React, { useEffect, useState } from 'react';
import Button from 'shared/uikit/Button';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Form from 'shared/uikit/Form';
import Icon from 'shared/uikit/Icon';
import Skeleton from 'shared/uikit/Skeleton';
import Typography from 'shared/uikit/Typography';
import Cookies from 'shared/utils/toolkit/cookies';
import useTranslation from 'shared/utils/hooks/useTranslation';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import { verifyEmail } from 'shared/utils/api/auth';
import useGetDeviceId from 'shared/utils/hooks/useGetDeviceId';
import removeUserCookies from 'shared/utils/toolkit/removeUserCookies';
import removePageCookies from 'shared/utils/toolkit/removePageCookies';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import useHistory from 'shared/utils/hooks/useHistory';
import useToast from 'shared/uikit/Toast/useToast';
import { Endpoints } from 'shared/utils/constants';
import classes from './page.module.scss';
import AuthFormLayout from '../../../../../shared/components/layouts/AuthFormLayout';

type Props = {
  params: {
    email: string;
    token: string;
  };
};

const CheckVerifyEmailPage = ({ params }: Props): JSX.Element => {
  const { t } = useTranslation();
  const [verifyEmailIsLoading, setIsLoading] = useState(true);
  const [isVerifySuccess, setIsVerifySuccess] = useState(false);
  const history = useHistory();
  const verificationCode = decodeURIComponent(params?.token);
  const email = decodeURIComponent(params?.email);
  const { deviceId, isLoading: getDeviceIdLoading } = useGetDeviceId();
  const toast = useToast();
  const [isSent, setIsSent] = useState(false);

  const isLoading = getDeviceIdLoading || verifyEmailIsLoading;

  const { mutate: verifyEmailHandler } = useReactMutation({
    apiFunc: verifyEmail,
  });

  useEffect(() => {
    removeUserCookies();
    removePageCookies();

    if (!getDeviceIdLoading) {
      setIsLoading(true);
      if (deviceId) {
        const DEVICE_ID = getCookieKey('deviceId');
        Cookies.set(DEVICE_ID, deviceId as string);
        verifyEmailHandler(
          {
            verificationCode,
            email,
            deviceId,
          },
          {
            onSuccess: () => {
              history.push(landingRouteNames.login);
            },
            onError: () => {
              setIsLoading(false);
              setIsVerifySuccess(false);
            },
          }
        );
      } else {
        setIsLoading(false);
        setIsVerifySuccess(false);
      }
    }
  }, [getDeviceIdLoading, deviceId]);

  const title = isLoading
    ? 'checking_verif_code'
    : isVerifySuccess
      ? 'welcome_back_to'
      : 'something_went_wrong';
  const subTitle = isVerifySuccess
    ? 'verification_successful'
    : 'verify_time_h_ex';
  const message = isVerifySuccess
    ? 'welcome_back_to_message'
    : 'an_er_oc_w_verify_y_a';
  const icon = isVerifySuccess ? 'check-circle' : 'info-circle';

  const onSuccess = () => {
    toast({
      type: 'success',
      icon: 'check-circle',
      message: 'verification_code_s_a',
    });
    setIsSent(true);
  };

  return (
    <AuthFormLayout title={t('email_verification')}>
      <Flex className={cnj(classes.hintWrap, isLoading && classes.skeletonBg)}>
        {!isLoading && (
          <>
            <Icon
              type="far"
              name={icon}
              size={20}
              color="graphene"
              className={classes.icon}
            />
            <Flex>
              <Typography font="700" size={16} height={28}>
                {t(subTitle)}
              </Typography>
              <Typography mt={10}>{t(message)}</Typography>
            </Flex>
          </>
        )}
      </Flex>
      {isLoading ? (
        <Skeleton className={cnj(classes.btnWrap, classes.skeleton)} />
      ) : (
        <Flex className={classes.btnWrap}>
          {isVerifySuccess ? (
            <Button
              to={landingRouteNames.login}
              className={classes.loginBtn}
              label={t('login_cap')}
              schema="primary-blue"
              variant="large"
            />
          ) : (
            <Form
              initialValues={{ email }}
              onSuccess={onSuccess}
              url={Endpoints.Auth.postResendEmail}
            >
              {({ handleSubmit, isSubmitting }: any) => (
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting || isSent}
                  label={t('re_verify_code')}
                  schema="primary-blue"
                  variant="large"
                />
              )}
            </Form>
          )}
        </Flex>
      )}
    </AuthFormLayout>
  );
};

export default CheckVerifyEmailPage;
