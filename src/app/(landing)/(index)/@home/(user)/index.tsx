'use client';

import React, { useEffect } from 'react';
import useSignUpRedirections from 'shared/utils/hooks/useSignUpRedirections';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import MainContent from './partials/sections/MainContent';

const HomePage: React.FC = () => {
  const { getRedirectUrl, redirect } = useSignUpRedirections();

  useEffect(() => {
    if (getRedirectUrl()) redirect();
  }, [getRedirectUrl, redirect]);

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeHomeScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <MainContent />
    </PermissionsGate>
  );
};

export default HomePage;
