'use client';

import React from 'react';
import Flex from 'shared/uikit/Flex/index';
import NoticeBoxSkeleton from 'shared/components/molecules/NoticeBox/NoticeBox.skeleton';
import FeedSkeleton from 'shared/components/Organism/FeedCard/FeedCard.skeleton';
import CreatePostBoxSkeleton from 'shared/components/molecules/CreatePostBoxSkeleton';
import cnj from '@shared/uikit/utils/cnj';
import classes from './loading.module.scss';

type Props = {
  className?: string;
};
export const SideSkeleton = ({ className }: Props) => (
  <Flex className={cnj(classes.skeletonSide, className)}>
    <NoticeBoxSkeleton widthButton />
    <NoticeBoxSkeleton widthButton />
  </Flex>
);

export const MainSkeleton = ({ className }: Props) => (
  <Flex className={cnj(classes.skeletonMain, className)}>
    <CreatePostBoxSkeleton />
    <FeedSkeleton />
  </Flex>
);

const Loading: React.FC = () => (
  <Flex className={classes.skeletonRoot}>
    <MainSkeleton />
    <SideSkeleton />
  </Flex>
);

export default Loading;
