import React, { useState } from 'react';
import Button from 'shared/uikit/Button';
import Birthdays from 'shared/components/molecules/NoticeBox/NoticeBoxWithItemAction';
import { getUpcomingBirthDays } from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type IUpcomingBirthDay from 'shared/utils/IUpcomingBirthDay';
import classes from './Sidebar.birthdays.module.scss';
import UpcomingBirthdaysModal from '../../modals/Birthdays';

const MAX_ITEMS = 3;

const SidebarBirthdayBox: React.FC = () => {
  const { t } = useTranslation();
  const [isOpen, toggleModal] = useState(false);
  const [isOpenCelebrateModal, toggleCelebrateModal] = useState(false);
  const [selectedUser, setUser] = useState<IUpcomingBirthDay | null>(null);
  const { data, isLoading, totalElements } = useInfiniteQuery<any>(
    [QueryKeys.upcomingBirthDays],
    {
      func: getUpcomingBirthDays,
      extraProps: {
        size: MAX_ITEMS,
      },
    }
  );
  const isEmpty = !isLoading && totalElements === 0;

  const handleMoreClick = () => {
    toggleModal(true);
  };

  if (isEmpty) {
    return null;
  }

  return (
    <>
      <Birthdays
        isLoading={isLoading}
        layoutProps={{
          title: t('upcoming_birthdays'),
          onMoreClicked: handleMoreClick,
          isVisibleShowMore: totalElements > MAX_ITEMS,
        }}
        data={data.slice(0, MAX_ITEMS)}
        dataKeys={{
          imageKeyName: 'croppedImageUrl',
          titleKeyName: 'fullName',
          subTitleKeyName: 'subTitle',
        }}
        listItemProps={{
          wrapperClassName: classes.listItemRequestWrapper,
          className: classes.listItem,
        }}
        actionRender={(item: IUpcomingBirthDay) =>
          item.isToday ? (
            <Button
              leftIcon="comment-alt-lines"
              label={t('celebrate')}
              onClick={() => {
                setUser(item);
                toggleCelebrateModal(true);
              }}
            />
          ) : (
            true
          )
        }
      />
      {isOpen && (
        <UpcomingBirthdaysModal hideBack onClose={() => toggleModal(false)} />
      )}
      {/* {isOpenCelebrateModal && ( */}
      {/*  <CelebrateModal */}
      {/*    hideBack */}
      {/*    selectedUser={{ */}
      {/*      image: selectedUser?.croppedImageUrl, */}
      {/*      title: selectedUser?.fullName, */}
      {/*      subTitle: selectedUser?.subTitle, */}
      {/*    }} */}
      {/*    onClose={() => toggleCelebrateModal(false)} */}
      {/*    onBack={() => toggleCelebrateModal(false)} */}
      {/*  /> */}
      {/* )} */}
    </>
  );
};

export default SidebarBirthdayBox;
