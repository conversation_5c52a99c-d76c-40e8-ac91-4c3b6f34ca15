import React, { useState } from 'react';
import HashtagIcon from 'shared/components/molecules/HashtagIcon/HashtagIcon';
import PopularHashtags from 'shared/components/molecules/NoticeBox/NoticeBoxWithoutAction';
import { getMostPopularHashtags } from 'shared/utils/api/postSearch';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { routeNames } from 'shared/utils/constants/routeNames';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { ISearchHashtagApi } from '@shared/types/search';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import usePluralize from '@shared/utils/toolkit/usePluralize';
import classes from './Sidebar.hashtags.module.scss';

const MIN_ITEMS = 6;

const SidebarBoxHashtags: React.FC = () => {
  const [showItemsCounts, setItemCounts] = useState(MIN_ITEMS);
  const { t } = useTranslation();
  const pluralize = usePluralize();
  const { data = [], isLoading } = useInfiniteQuery<ISearchHashtagApi>(
    [QueryKeys.popularHashtags],
    {
      func: getMostPopularHashtags,
      extraProps: {
        size: 10,
      },
    }
  );
  const isEmpty = !isLoading && data?.length === 0;
  const handleMoreClicked = () => {
    setItemCounts(data.length);
  };

  const navigateToSearchPage = useNavigateSearchPage();
  const handleHashtagClicked = (item: any) => {
    navigateToSearchPage({
      pathname: routeNames.searchPosts,
      hashtags: [item?.name],
    });
  };

  const displayData = data.slice(0, showItemsCounts).map((x) => ({
    ...x,
    name: `${x.id}`,
    subTitle: pluralize('post', x.usageCount),
  }));

  if (isEmpty) {
    return null;
  }

  return (
    <PopularHashtags
      isLoading={isLoading}
      layoutProps={{
        title: t('popular_hashtags'),
        onMoreClicked: handleMoreClicked,
        isVisibleShowMore: data?.length !== showItemsCounts,
      }}
      data={displayData}
      dataKeys={{
        titleKeyName: 'name',
        subTitleKeyName: 'subTitle',
      }}
      listItemProps={{
        hoverBgColor: 'hoverPrimary',
        hoverColor: 'thirdText',
        titleContainerClassName: classes.titleContainer,
        labelsContainerClassName: classes.labelsContainerClassName,
        onItemClicked: handleHashtagClicked,
        lefSvg: HashtagIcon,
        labelClassName: classes.labelClassName,
        secondaryLabelClassName: classes.secondaryLabelClassName,
        className: classes.listItemClassName,
      }}
    />
  );
};

export default SidebarBoxHashtags;
