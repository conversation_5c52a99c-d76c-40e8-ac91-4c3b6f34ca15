import React from 'react';
import FollowButton from 'shared/components/molecules/FollowButton/FollowButton';
import PopularPages from 'shared/components/molecules/NoticeBox/NoticeBoxWithItemAction';
import type { QueryKeyType } from 'shared/types/general';
import useDisclosure from 'shared/utils/hooks/useDisclosure';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import useFollowPage from 'shared/hooks/api-hook/useFollowPage';
import dynamic from 'next/dynamic';
import type { IPage } from 'shared/types/page';
import type { ISearchPage } from 'shared/types/search';
import type { BlockGen } from 'shared/types';
import classes from './Sidebar.pages.list.module.scss';

const PeoplePopularPages = dynamic(() => import('../../modals/SidebarModal'));
const MAX_ITEMS = 3;

interface SidebarPagesListProps {
  title: string;
  queryKey: QueryKeyType;
  apiFunc: Function;
}

const SidebarPagesList = ({
  title,
  queryKey,
  apiFunc,
}: SidebarPagesListProps) => {
  const { followCall, isLoading: isLoadingFollow } = useFollowPage();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { t } = useTranslation();

  const {
    data = [],
    isLoading,
    refetch,
    hasNextPage,
    totalElements,
  } = useInfiniteQuery<BlockGen<IPage>>(queryKey, {
    func: apiFunc,
    extraProps: {
      size: 20,
    },
  });
  const isEmpty = !isLoading && totalElements === 0;

  const { remove: removeFollowRequest } = useUpdateInfinityData(queryKey);

  const onSuccess = (item: ISearchPage) => {
    removeFollowRequest(item.id);
    if (data?.length < MAX_ITEMS + 1 && hasNextPage) {
      refetch();
    }
  };

  const handleMoreClick = () => {
    onOpen();
  };

  if (isEmpty) {
    return null;
  }

  return (
    <>
      <PopularPages
        isLoading={isLoading}
        layoutProps={{
          title,
          onMoreClicked: handleMoreClick,
          isVisibleShowMore: totalElements > MAX_ITEMS,
        }}
        data={data?.slice(0, MAX_ITEMS)}
        dataKeys={{
          imageKeyName: 'croppedImageUrl',
          titleKeyName: 'title',
          subTitleKeyName: 'subTitle',
        }}
        listItemProps={{
          wrapperClassName: classes.listItemRequestWrapper,
          className: classes.listItemRequest,
          avatarProps: {
            isCompany: true,
          },
        }}
        actionRender={(item) => (
          <FollowButton
            object={{ id: item.id, isPage: true, username: item.username }}
            onSuccess={() => onSuccess(item)}
          />
        )}
      />
      {isOpen && (
        <PeoplePopularPages
          onClose={onClose}
          title={title}
          queryKey={queryKey}
          apiFunc={apiFunc}
          actionFunc={followCall}
          actionButtonProps={{ isLoading: isLoadingFollow }}
          isCompany
        />
      )}
    </>
  );
};

export default SidebarPagesList;
