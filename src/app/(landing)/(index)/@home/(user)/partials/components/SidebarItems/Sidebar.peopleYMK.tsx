import React from 'react';
import FollowButton from 'shared/components/molecules/FollowButton/FollowButton';
import TopSuggestions from 'shared/components/molecules/NoticeBox/NoticeBoxWithItemAction';
import { getSuggestionPeople } from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useDisclosure from 'shared/utils/hooks/useDisclosure';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import dynamic from 'next/dynamic';
import type IPeople from 'shared/utils/IPeople';
import type { BlockGen } from 'shared/types';
import classes from './Sidebar.peopleYMK.module.scss';

const PeopleYMKModal = dynamic(() => import('../../modals/SidebarModal'));
const MAX_ITEMS = 3;

const SidebarPeopleKnowBox = (): JSX.Element => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { t } = useTranslation();
  const KEY = [QueryKeys.getSuggestionPeople, 'sidebar'];
  const {
    data = [],
    isLoading,
    refetch,
    hasNextPage,
    totalElements,
  } = useInfiniteQuery<BlockGen<IPeople>>(KEY, {
    func: getSuggestionPeople,
    extraProps: {
      size: 30,
    },
  });
  const isEmpty = !isLoading && totalElements === 0;
  const { remove: removeFollowRequest } = useUpdateInfinityData(KEY);
  const onSuccess = (item: IPeople) => {
    removeFollowRequest(item.id);
    if (data?.length < MAX_ITEMS + 1 && hasNextPage) {
      refetch();
    }
  };
  const handleMoreClick = () => {
    onOpen();
  };

  if (isEmpty) {
    return null;
  }

  return (
    <>
      <TopSuggestions
        isLoading={isLoading}
        layoutProps={{
          title: t('people_y_mk'),
          onMoreClicked: handleMoreClick,
          isVisibleShowMore: totalElements > MAX_ITEMS,
        }}
        data={data?.slice(0, MAX_ITEMS)}
        dataKeys={{
          imageKeyName: 'croppedImageUrl',
          titleKeyName: 'fullName',
          subTitleKeyName: 'subTitle',
        }}
        listItemProps={{
          wrapperClassName: classes.listItemRequestWrapper,
          className: classes.listItemRequest,
        }}
        actionRender={(item) => (
          <FollowButton
            back={item?.back}
            object={{ id: item.id, isPage: false, username: item.username }}
            onSuccess={() => onSuccess(item)}
          />
        )}
      />
      {isOpen && (
        <PeopleYMKModal
          onClose={onClose}
          title={t('people_y_mk')}
          queryKey={KEY}
          apiFunc={getSuggestionPeople}
        />
      )}
    </>
  );
};

export default SidebarPeopleKnowBox;
