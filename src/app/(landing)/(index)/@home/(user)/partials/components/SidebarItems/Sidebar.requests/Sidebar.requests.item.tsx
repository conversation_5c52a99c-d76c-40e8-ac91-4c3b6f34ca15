import React from 'react';
import Flex from 'shared/uikit/Flex/index';
import Button from 'shared/uikit/Button';
import useTheme from 'shared/uikit/utils/useTheme';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useAcceptFollow from 'shared/hooks/api-hook/useAcceptFollow';
import useDeclineFollow from 'shared/hooks/api-hook/useDeclineFollow';
import classes from './Sidebar.requests.item.module.scss';

export interface SidebarRequestsItemProps {
  item?: any;
  onSuccess?: any;
}

const SidebarRequestsItem = ({
  item,
  onSuccess,
}: SidebarRequestsItemProps): JSX.Element => {
  const { isDark } = useTheme();
  const { t } = useTranslation();
  const { acceptCall, isLoading: isLoadingAccept } = useAcceptFollow();
  const { declineCall, isLoading: isLoadingDecline } = useDeclineFollow();

  return (
    <Flex className={classes.listItemActionsContainer}>
      <Button
        isLoading={isLoadingAccept}
        fullWidth
        labelFont="700"
        onClick={(e: any) => {
          preventClickHandler(e);
          acceptCall(item.id, {
            onSuccess,
          });
        }}
        className={classes.followBtn}
        label={t('accept')}
      />
      <Button
        isLoading={isLoadingDecline}
        onClick={(e: any) => {
          preventClickHandler(e);
          declineCall(item.id, {
            onSuccess,
          });
        }}
        schema={isDark ? 'dark-gray' : 'semi-transparent'}
        className={classes.followBtn}
        label={t('decline')}
      />
    </Flex>
  );
};

export default SidebarRequestsItem;
