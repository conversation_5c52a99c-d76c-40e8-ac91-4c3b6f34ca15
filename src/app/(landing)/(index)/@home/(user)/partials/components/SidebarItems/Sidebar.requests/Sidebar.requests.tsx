import React, { useState } from 'react';
import dynamic from 'next/dynamic';
import Icon from 'shared/uikit/Icon';
import useMedia from 'shared/uikit/utils/useMedia';
import { getFollowRequestPaginated } from 'shared/utils/api/network/common';
import useDisclosure from 'shared/utils/hooks/useDisclosure';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import NewRequests from 'shared/components/molecules/NoticeBox/NoticeBoxWithItemAction';
import useAcceptFollow from 'shared/hooks/api-hook/useAcceptFollow';
import useDeclineFollow from 'shared/hooks/api-hook/useDeclineFollow';
import type { PeopleType } from 'shared/types/people';
import classes from './Sidebar.requests.module.scss';
import SidebarRequestsItem from './Sidebar.requests.item';
import useGetNewRequests from './useGetNewRequests';

const NewRequestsModal = dynamic(() => import('../../../modals/SidebarModal'));

const MAX_ITEMS = 3;

const SidebarNewRequestsBox = (): JSX.Element => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { t } = useTranslation();
  const [isLocalEmpty, toggleIsLocalEmpty] = useState(false);
  const { isMoreThanTablet } = useMedia();

  const { acceptCall, isLoading: isLoadingAccept } = useAcceptFollow();
  const { declineCall, isLoading: isLoadingDecline } = useDeclineFollow();

  const { key, isLoading, totalElements, data, hasNextPage, refetch } =
    useGetNewRequests();

  const { remove: removeFollowRequest } = useUpdateInfinityData(key);
  const isEmpty = !isLoading && totalElements === 0;

  const handleMoreClick = () => {
    onOpen();
  };

  const onSuccess = (item: PeopleType) => () => {
    removeFollowRequest(item.id);
    if (data?.length - 1 === 0) {
      toggleIsLocalEmpty(true);
    }
    if (data?.length < MAX_ITEMS + 1 && hasNextPage) {
      refetch();
    }
  };

  if (isEmpty && !isLocalEmpty) {
    return null;
  }

  return (
    <>
      <NewRequests
        isEmpty={isEmpty}
        isLoading={isLoading}
        layoutProps={{
          title: t('new_follow_req'),
          onMoreClicked: handleMoreClick,
          isVisibleShowMore: 1 || totalElements > MAX_ITEMS,
        }}
        data={data?.slice(0, MAX_ITEMS)}
        dataKeys={{
          imageKeyName: 'croppedImageUrl',
          titleKeyName: 'fullName',
          subTitleKeyName: 'usernameAtSign',
        }}
        listItemProps={{
          wrapperClassName: classes.listItemRequestWrapper,
          className: classes.listItemRequest,
        }}
        actionRender={(item) => (
          <SidebarRequestsItem item={item} onSuccess={onSuccess(item)} />
        )}
      />
      {isOpen && (
        <NewRequestsModal
          onClose={onClose}
          title={t('new_follow_req')}
          queryKey={key}
          apiFunc={getFollowRequestPaginated}
          actionFunc={acceptCall}
          secondActionFunc={declineCall}
          actionButtonProps={{
            leftIcon: null,
            schema: 'primary-blue',
            isLoading: isLoadingAccept,
            label: isMoreThanTablet ? t('accept') : undefined,
            leftSvg: isMoreThanTablet ? undefined : (
              <Icon name="check" color="white" size={18} />
            ),
          }}
          secondActionProps={{
            className: classes.declineBtn,
            isLoading: isLoadingDecline,
            label: isMoreThanTablet ? t('decline') : undefined,
            leftSvg: isMoreThanTablet ? undefined : (
              <Icon name="times" color="graphene" size={18} />
            ),
          }}
        />
      )}
    </>
  );
};

export default SidebarNewRequestsBox;
