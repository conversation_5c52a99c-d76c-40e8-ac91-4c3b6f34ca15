import React, { useState } from 'react';
import useTheme from 'shared/uikit/utils/useTheme';
import Button from 'shared/uikit/Button';
import ModalDialog from 'shared/uikit/Modal/ModalDialog';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import UserItem from 'shared/components/molecules/UserItemWithAction';
import { getUpcomingBirthDays } from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import CelebrateModal from 'shared/components/Organism/CelebrateMessage';
import InfiniteLoading from 'shared/components/molecules/InfiniteLoading';
import InfiniteScroll from 'shared/components/Organism/InfiniteScroll';
import type IUpcomingBirthDay from 'shared/utils/IUpcomingBirthDay';
import classes from './Birthdays.component.module.scss';

export interface BirthdaysModalProps {
  onClose?: () => void;
  hideBack?: boolean;
}

const BirthdaysModal = ({
  onClose,
  hideBack,
}: BirthdaysModalProps): JSX.Element => {
  const { isDark } = useTheme();
  const [isOpen, toggleModal] = useState(false);
  const [selectedUser, setUser] = useState<IUpcomingBirthDay | null>(null);

  const { data, hasNextPage, fetchNextPage, isFetchingNextPage } =
    useInfiniteQuery<any>([QueryKeys.upcomingBirthDays], {
      func: getUpcomingBirthDays,
      size: 10,
    });

  function handleOnCelebrateClick(item: any) {
    setUser(item);
    toggleModal(true);
  }

  return (
    <>
      {isOpen ? null : (
        <ModalDialog isOpen onBack={onClose} onClose={onClose}>
          <ModalHeaderSimple title="Upcoming birthdays" hideBack={hideBack} />
          <ModalBody className={classes.contentClassName}>
            <InfiniteScroll
              data={data}
              onEndReached={fetchNextPage}
              hasMore={!isFetchingNextPage && hasNextPage}
              loadingComponent={<InfiniteLoading />}
              renderItem={(person: IUpcomingBirthDay) => (
                <UserItem
                  key={person.id}
                  data={{
                    image: person.croppedImageUrl,
                    title: person.fullName,
                    subTitle: person.subTitle,
                  }}
                  subTitleProps={{
                    color: 'fifthText',
                  }}
                  noHover
                  action={
                    person.isToday && (
                      <Button
                        schema={isDark ? 'secondary-dark' : 'semi-transparent'}
                        rightIcon="comment-alt-lines"
                        rightType="fas"
                        label="Celebrate"
                        onClick={() => handleOnCelebrateClick(person)}
                      />
                    )
                  }
                />
              )}
            />
          </ModalBody>
        </ModalDialog>
      )}
      {isOpen && (
        <CelebrateModal
          selectedUser={{
            image: selectedUser?.croppedImageUrl,
            title: selectedUser?.fullName,
            subTitle: selectedUser?.subTitle,
          }}
          hideBack={false}
          onClose={() => toggleModal(false)}
          onBack={() => toggleModal(false)}
        />
      )}
    </>
  );
};

export default BirthdaysModal;
