import React from 'react';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import InfiniteScroll from 'shared/components/Organism/InfiniteScroll';
import type IGetFollowers from 'shared/utils/IGetFollowers';
import type { PaginateResponse } from 'shared/types/response';
import NetworkItemWithAction from 'shared/components/Organism/SocialConnectionsModal/NetworkItemWithAction';
import FixedRightSideModalDialog from '@shared/uikit/Modal/FixedRightSideModalDialog';
import SocialConnectionsModalSkeleton from '../../../../../../../../shared/components/Organism/SocialConnectionsModal/SocialConnectionsModal.skeleton';

export interface SidebarModalProps {
  onClose: () => void;
  title: string;
  queryKey: string | Array<string>;
  apiFunc: Function;
}

const SidebarModal = ({
  onClose,
  queryKey,
  apiFunc,
  title,
}: SidebarModalProps): JSX.Element => {
  const {
    data = [],
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery<PaginateResponse<IGetFollowers>>(queryKey, {
    func: apiFunc,
  });
  const { remove } = useUpdateInfinityData(queryKey);

  return (
    <FixedRightSideModalDialog wide isOpen onBack={onClose} onClose={onClose}>
      <ModalHeaderSimple title={title} />
      <ModalBody className="!m-16_12">
        <InfiniteScroll
          data={data}
          onEndReached={fetchNextPage}
          hasMore={!isFetchingNextPage && hasNextPage}
          loadingComponent={
            <SocialConnectionsModalSkeleton
              // className={classes.skeletonWithSearch}
              visibleSearchInput={false}
              placeHolderLength={1}
            />
          }
          renderItem={(item: any) => (
            <NetworkItemWithAction
              item={item}
              onSuccess={() => remove(item.id)}
            />
          )}
        />
        {isFetchingNextPage && (
          <SocialConnectionsModalSkeleton
            // className={classes.skeletonWithSearch}
            visibleSearchInput={false}
            placeHolderLength={1}
          />
        )}
      </ModalBody>
    </FixedRightSideModalDialog>
  );
};

export default SidebarModal;
