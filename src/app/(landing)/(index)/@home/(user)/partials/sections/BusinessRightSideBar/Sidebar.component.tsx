import React from 'react';
import Flex from 'shared/uikit/Flex/index';
import classes from './Sidebar.component.module.scss';
// import UpComingBirthdays from '../../components/SidebarItems/Sidebar.birthdays';
import WhichPagesToFollow from '../../components/SidebarItems/Sidebar.pageWhich';
import PopularPages from '../../components/SidebarItems/Sidebar.popularPages';
import PopularHashtags from '../../components/SidebarItems/Sidebar.hashtags';

const Sidebar: React.FC = () => (
  <Flex className={classes.postSidebarContainer}>
    {/* <UpComingBirthdays /> */}
    <WhichPagesToFollow />
    <PopularPages />
    <PopularHashtags />
  </Flex>
);

export default Sidebar;
