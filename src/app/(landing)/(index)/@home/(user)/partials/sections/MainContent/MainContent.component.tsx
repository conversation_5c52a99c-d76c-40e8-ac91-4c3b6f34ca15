import React, { memo, useRef } from 'react';
import dynamic from 'next/dynamic';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import Flex from 'shared/uikit/Flex';
import CreatePostBox from '@shared/components/Organism/CreatePostBox';
import CreatePostProgressBar from '@shared/components/Organism/CreatePostProgressBar';
import { getFeedList } from '@shared/utils/api/post';
import { QueryKeys, CREATE_POST_BOX } from '@shared/utils/constants';
import PostsList from 'shared/components/Organism/PostsList';
import classes from './MainContent.component.module.scss';
import { MainSkeleton, SideSkeleton } from '../../../loading';

const UserRightSideBar = dynamic(() => import('../UserRightSideBar'), {
  loading: () => <SideSkeleton className="mt-20 !ml-0" />,
});

const BusinessRightSideBar = dynamic(() => import('../BusinessRightSideBar'), {
  loading: () => <SideSkeleton className="mt-20 !ml-0" />,
});

const HomePage: React.FC = () => {
  const listContainerRef = useRef<HTMLDivElement>(null);
  const queryKey = [QueryKeys.homeFeedList];

  return (
    <Flex className={classes.homeContainer} ref={listContainerRef}>
      <PostsList
        queryKey={queryKey}
        scrollRef={listContainerRef}
        apiFunc={getFeedList}
        topListItem={
          <Flex id={CREATE_POST_BOX} className={classes.createPostWrapper}>
            <CreatePostBox />
            <CreatePostProgressBar />
          </Flex>
        }
        startMargin={278}
        initialLoadingSkeleton={<MainSkeleton className="!mt-16_20 !px-0_20" />}
      />

      <Flex className={classes.postSidebar}>
        {isBusinessApp ? <BusinessRightSideBar /> : <UserRightSideBar />}
      </Flex>
    </Flex>
  );
};

export default memo(HomePage);
