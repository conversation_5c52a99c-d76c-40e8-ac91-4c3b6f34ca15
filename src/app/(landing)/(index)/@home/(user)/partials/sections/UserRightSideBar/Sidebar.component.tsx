import React from 'react';
import Flex from 'shared/uikit/Flex/index';
import classes from './Sidebar.component.module.scss';
// import UpComingBirthdays from '../../components/SidebarItems/Sidebar.birthdays';
import NewRequests from '../../components/SidebarItems/Sidebar.requests';
import PeopleYouKnow from '../../components/SidebarItems/Sidebar.peopleYMK';
import PopularPages from '../../components/SidebarItems/Sidebar.popularPages';
import PopularHashtags from '../../components/SidebarItems/Sidebar.hashtags';

const Sidebar: React.FC = () => (
  <Flex className={classes.postSidebarContainer}>
    {/* <UpComingBirthdays /> */}
    <NewRequests />
    <PeopleYouKnow />
    <PopularPages />
    <PopularHashtags />
  </Flex>
);

export default Sidebar;
