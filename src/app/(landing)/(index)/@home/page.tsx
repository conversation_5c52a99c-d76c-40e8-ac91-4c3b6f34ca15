'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { appPortalsObject, getPortal } from 'shared/utils/getAppEnv';
import { useSearchParams, redirect } from 'next/navigation';
import getPortalBaseUrl from 'shared/utils/getPortalBaseUrl';
import FeedLoading from './(user)/loading';

const Module = dynamic(
  () => {
    if (getPortal() === appPortalsObject.recruiter)
      return import('./(recruiter)/index');
    if (getPortal() === appPortalsObject.service)
      return import('./(recruiter)/index');
    if (getPortal() === appPortalsObject.campaign)
      return import('./(recruiter)/index');
    if (getPortal() === appPortalsObject.sales)
      return import('./(sales)/index');

    return import('./(user)/index');
  },
  {
    ssr: false,
    loading: () => <FeedLoading />,
  }
);

export default function Page() {
  const searchParams = useSearchParams();
  let redirectUrl = searchParams.get('redirect');
  if (
    !redirectUrl &&
    ![appPortalsObject.editor, appPortalsObject.user].includes(getPortal())
  ) {
    redirectUrl = getPortalBaseUrl(getPortal());
  }

  if (redirectUrl) {
    return redirect(redirectUrl);
  }
  return <Module />;
}
