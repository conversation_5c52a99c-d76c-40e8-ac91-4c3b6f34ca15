'use client';

import React from 'react';
import useLocation from '@shared/utils/hooks/useLocation';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import useGetDeviceId from 'shared/utils/hooks/useGetDeviceId';
import useTranslation from 'shared/utils/hooks/useTranslation';
import AuthSocialIcons from 'shared/uikit/AuthSocialIcons';
import cnj from 'shared/uikit/utils/cnj';
import DividerTitled from 'shared/uikit/Divider/DividerTitled';
import Button from 'shared/uikit/Button';
import LogoIcon from 'shared/svg/LogoIcon';
import ParseTextStringCP from 'shared/components/molecules/TranslateReplacer';
import TextLink from 'shared/uikit/Link/TextLink';
import classes from './page.module.scss';

const PrimePage = (): JSX.Element => {
  const { t } = useTranslation();
  const { deviceId } = useGetDeviceId();
  const { preserveSearchParams } = useLocation();

  return (
    <Flex className={classes.primeRoot}>
      <Flex className={classes.logoWrapper}>
        <LogoIcon />
      </Flex>
      <Typography
        className={classes.text}
        font="700"
        size={24}
        height={28}
        color="smoke_coal"
      >
        {t('one_p_one_e')}
      </Typography>
      <AuthSocialIcons
        className={classes.socialIconsWrap}
        column
        deviceId={deviceId}
        isSignup
      />
      <DividerTitled
        label={t('or_lower')}
        className={cnj(classes.dividerTitled)}
      />
      <Button
        fullWidth
        variant="xLarge"
        schema="primary-blue"
        label={t('create_account')}
        to={preserveSearchParams(landingRouteNames.signup)}
      />

      <ParseTextStringCP
        textProps={{
          className: classes.bySingIn,
          height: 19,
          size: 14,
          color: 'secondaryDisabledText',
        }}
        textString={translateReplacer(t('by_sign_y_a_t'), [
          t('terms_of_serv'),
          t('privacy_policy'),
          t('cookie'),
        ])}
        tagComponentMap={{
          0: (text) => (
            <TextLink
              typographyProps={{ size: 14, color: 'brand' }}
              to={landingRouteNames.userAgreement}
            >
              {text}
            </TextLink>
          ),
          1: (text) => (
            <TextLink
              typographyProps={{ size: 14, color: 'brand' }}
              to={landingRouteNames.privacy}
            >
              {text}
            </TextLink>
          ),
          2: (text) => (
            <TextLink
              typographyProps={{ size: 14, color: 'brand' }}
              to={landingRouteNames.cookie_policy}
            >
              {text}
            </TextLink>
          ),
        }}
      />

      <DividerTitled
        label={t('h_an_acc')}
        className={cnj(classes.haveAccount)}
      />
      <Button
        variant="xLarge"
        fullWidth
        schema="semi-transparent"
        label={t('login_cap')}
        to={preserveSearchParams(landingRouteNames.login)}
      />
    </Flex>
  );
};

export default PrimePage;
