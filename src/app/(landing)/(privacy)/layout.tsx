'use client';

import React, { useEffect } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex/index';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { MAIN_CENTER_WRAPPER_ID } from 'shared/constants/enums';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import Header from 'shared/components/layouts/AppLayout/AppLayout.Header';
import useScrollToTopEventListener from 'shared/utils/useScrollToTopEventListener';
import Tabs from 'shared/uikit/Tabs';
import { usePathname, useRouter } from 'next/navigation';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import classes from './layout.module.scss';

const AppLayout: React.FC = ({ children }: React.PropsWithChildren) => {
  const { reFetchAppObject, authUser } = useGetAppObject();
  const { t } = useTranslation();
  useScrollToTopEventListener();
  const pathname = usePathname();
  const router = useRouter();

  const onBackHandler = () => {
    router.back();
  };
  useEffect(() => {
    if (!authUser?.publicSetting) {
      reFetchAppObject();
    }
  }, [authUser]);

  const tabs = [
    {
      path: landingRouteNames.legal,
      title: t('legal'),
    },
    {
      path: landingRouteNames.helpCenter,
      title: t('help'),
    },
    {
      path: landingRouteNames.about,
      title: t('about'),
    },
    {
      path: landingRouteNames.contactUs,
      title: t('get_business'),
    },
  ];
  const title = tabs.find((i) => i.path === pathname)?.title;

  return (
    <Flex className={classes.appRoot}>
      <Header isLanding className={classes.desktopHeader} />
      <ModalHeaderSimple
        visibleHeaderDivider
        className={classes.mobileHeader}
        title={title}
        backButtonProps={{ onClick: onBackHandler }}
      />
      <Flex className={cnj(classes.appMain)}>
        <Flex id={MAIN_CENTER_WRAPPER_ID} className={classes.mainCenter}>
          <Tabs
            contentRootClassName={classes.contentRootClassName}
            linksRootClassName={classes.linksRootShrink}
            tabs={tabs}
            linkVariant="badge"
            isFullWidth={false}
            navLinkClassName={classes.navLinkClassName}
            navLinkTitleClassName={classes.navLinkTitleClassName}
            linkAndActionWrapperClassName={classes.linkAndActionWrapper}
          >
            {children}
          </Tabs>
        </Flex>
      </Flex>
    </Flex>
  );
};
export default AppLayout;
