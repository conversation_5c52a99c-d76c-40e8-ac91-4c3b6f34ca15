'use client';

import React from 'react';

import { landingRouteNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import FeedbackModal from 'shared/components/Organism/FeedbackModal';
import BusinessContact from 'shared/components/Organism/BusinessContact';
import useMedia from 'shared/uikit/utils/useMedia';
import Flex from 'shared/uikit/Flex';
import { contactUsLinks } from '../../partials/links';
import findPrevNext from '../../partials/findPrevNext';
import ListDetailLayout from '../../partials/ListDetailLayout';

const Page = ({ params }) => {
  const { t } = useTranslation();
  const form = params?.form;
  const { current, links } = findPrevNext(form, contactUsLinks);
  const { isMoreThanTablet } = useMedia();

  return (
    <ListDetailLayout
      baseRouteName={
        isMoreThanTablet
          ? landingRouteNames.contactUs
          : landingRouteNames.feedback
      }
      base={t('get_business')}
      links={links}
      current={current}
    >
      <Flex>
        {form === 'business-contact' ? (
          <BusinessContact />
        ) : (
          <FeedbackModal isLanding />
        )}
      </Flex>
    </ListDetailLayout>
  );
};

export default Page;
