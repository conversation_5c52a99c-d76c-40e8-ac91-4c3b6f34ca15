'use client';

import React from 'react';

import { landingRouteNames } from 'shared/utils/constants/routeNames';
import useMedia from 'shared/uikit/utils/useMedia';
import { redirect } from 'next/navigation';
import findPrevNext from '../partials/findPrevNext';
import ListDetailLayout from '../partials/ListDetailLayout';
import { contactUsLinks } from '../partials/links';

const Page = () => {
  const { links } = findPrevNext(null, contactUsLinks);
  const { isMoreThanTablet } = useMedia();

  if (isMoreThanTablet) {
    return redirect(landingRouteNames.feedback);
  }
  return <ListDetailLayout links={links} />;
};

export default Page;
