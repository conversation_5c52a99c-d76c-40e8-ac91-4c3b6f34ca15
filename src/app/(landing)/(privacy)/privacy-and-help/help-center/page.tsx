'use client';

import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ComingSoon from 'shared/svg/ComingSoon';
import classes from './page.module.scss';

const Page = () => {
  const { t } = useTranslation();

  return (
    <EmptySectionInModules
      title={t('coming_3dot')}
      text={t('w_r_w_o_it')}
      image={<ComingSoon />}
      classNames={{ container: classes.container }}
    />
  );
};

export default Page;
