'use client';

import React from 'react';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useMedia from 'shared/uikit/utils/useMedia';
import classes from './page.module.scss';

import findPrevNext from '../../partials/findPrevNext';
import Ifram from '../../partials/Ifram';
import ListDetailLayout from '../../partials/ListDetailLayout';
import BottomNavigation from '../../partials/BottomNavigation';
import { privacyLinks } from '../../partials/links';

const Page = ({ params }) => {
  const file = params?.file;
  const { isMoreThanTablet } = useMedia();

  const { currentLanguage, t } = useTranslation();
  const { current, links, prev, next } = findPrevNext(file, privacyLinks);

  return (
    <ListDetailLayout
      base={t('legal')}
      baseRouteName={
        isMoreThanTablet
          ? landingRouteNames.userAgreement
          : landingRouteNames.legal
      }
      links={links}
      current={current}
      bottomComponent={<BottomNavigation next={next} prev={prev} />}
    >
      <Ifram
        src={`/privacy-and-help/${currentLanguage}/${current?.name}.html`}
        title={`${currentLanguage}-${current?.name ?? ''}`}
      />
    </ListDetailLayout>
  );
};

export default Page;
