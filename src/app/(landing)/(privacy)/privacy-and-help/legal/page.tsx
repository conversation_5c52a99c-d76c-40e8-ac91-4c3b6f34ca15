'use client';

import React from 'react';
import useMedia from 'shared/uikit/utils/useMedia';
import { redirect } from 'next/navigation';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import findPrevNext from '../partials/findPrevNext';
import ListDetailLayout from '../partials/ListDetailLayout';
import { privacyLinks } from '../partials/links';

const Page = () => {
  const { links } = findPrevNext(null, privacyLinks);
  const { isMoreThanTablet } = useMedia();

  if (isMoreThanTablet) {
    return redirect(landingRouteNames.userAgreement);
  }

  return <ListDetailLayout links={links} />;
};

export default Page;
