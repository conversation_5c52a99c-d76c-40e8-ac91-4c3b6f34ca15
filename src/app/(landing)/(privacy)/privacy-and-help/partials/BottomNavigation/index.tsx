import React from 'react';
import Typography from 'shared/uikit/Typography';
import Flex from 'shared/uikit/Flex/index';
import IconButton from 'shared/uikit/Button/IconButton';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import cnj from 'shared/uikit/utils/cnj';
import Link from 'shared/uikit/Link';
import classes from './BottomNavigation.module.scss';

interface Props {
  prev: any;
  next: any;
}

const Navigation: React.FC<Props> = ({ prev, next }) => {
  const { t } = useTranslation();

  return (
    <Flex className={classes.container}>
      <Link
        to={`${landingRouteNames.privacySecurity}/legal/${prev?.hash}`}
        className={cnj(classes.link, !prev?.hash && classes.disabled)}
        disabled={!prev?.hash}
      >
        <IconButton type="far" size="sm18" noHover name="chevron-left" />
        <Flex>
          <Typography height={18} size={15} font="bold">
            {t('previous')}
          </Typography>
          <Typography
            isTruncated
            isWordWrap
            lineNumber={1}
            size={12}
            height={17}
            color="secondaryDisabledText"
          >
            {t(prev?.name) || t('there_is_n_p_s')}
          </Typography>
        </Flex>
      </Link>
      <Link
        to={`${landingRouteNames.privacySecurity}/legal/${next?.hash}`}
        className={cnj(classes.link, !next?.hash && classes.disabled)}
        disabled={!next?.hash}
      >
        <IconButton type="far" size="sm18" noHover name="chevron-right" />
        <Flex>
          <Typography height={18} size={15} font="bold">
            {t('next')}
          </Typography>
          <Typography
            isTruncated
            isWordWrap
            lineNumber={1}
            size={12}
            height={17}
            color="secondaryDisabledText"
            className={classes.breakAll}
          >
            {t(next?.name) || t('there_is_n_n_s')}
          </Typography>
        </Flex>
      </Link>
    </Flex>
  );
};

export default Navigation;
