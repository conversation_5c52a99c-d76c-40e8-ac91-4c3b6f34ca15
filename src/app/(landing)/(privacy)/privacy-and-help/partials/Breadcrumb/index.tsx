import React from 'react';
import Flex from 'shared/uikit/Flex/index';
import Typography from 'shared/uikit/Typography';
import IconButton from 'shared/uikit/Button/IconButton';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Link from 'shared/uikit/Link';
import classes from './Breadcrumb.module.scss';

interface Props {
  base: string;
  current: string;
  baseRouteName: string;
}
const Breadcrumb: React.FC<Props> = ({ baseRouteName, base, current }) => {
  const { t } = useTranslation();

  return (
    <Flex>
      <Flex className={classes.divider} />
      <Flex className={classes.breadcrumbContainer}>
        <Link to={baseRouteName}>
          <Typography color="secondaryDisabledText" height={18} size={14}>
            {base}
          </Typography>
        </Link>
        <IconButton
          iconProps={{ size: 8 }}
          type="far"
          noHover
          size="tiny"
          name="chevron-right"
        />
        <Typography size={14} font="700" height={18} color="smoke_coal">
          {t(current)}
        </Typography>
      </Flex>
    </Flex>
  );
};

export default Breadcrumb;
