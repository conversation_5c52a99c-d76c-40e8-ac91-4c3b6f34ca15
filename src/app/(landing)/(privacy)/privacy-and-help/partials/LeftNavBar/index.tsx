import React, { useEffect, useState } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex/index';
import Typography from 'shared/uikit/Typography';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import IconButton from 'shared/uikit/Button/IconButton';
import Link from 'shared/uikit/Link';
import classes from './leftNavBar.module.scss';

const AccordionMenu = ({ activeLink, links }) => {
  const { t } = useTranslation();

  return (
    <Flex className={classes.leftNavBarRoot}>
      {links.map((item, index) => (
        <Link
          key={index}
          className={cnj(
            classes.leftNavBarItem,
            item.hash === activeLink && classes.active
          )}
          to={`${landingRouteNames.privacySecurity}/${item.base}/${item.hash}`}
        >
          <Typography className={classes.name} font="700" color="smoke_coal">
            {t(item.name)}
          </Typography>
          <IconButton
            className={classes.icon}
            name="chevron-right"
            size="sm16"
            type="far"
            noHover
          />
        </Link>
      ))}
    </Flex>
  );
};

export default AccordionMenu;
