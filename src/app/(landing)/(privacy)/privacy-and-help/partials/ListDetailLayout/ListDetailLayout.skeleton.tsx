'use client';

import React, { type FC } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex/index';
import useMedia from 'shared/uikit/utils/useMedia';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalDialog from 'shared/uikit/Modal/ModalDialog';
import Skeleton from '@shared/uikit/Skeleton';
import classes from './ListDetailLayout.module.scss';

const ListDetailSkeleton: FC<{ isMainRoute?: boolean }> = ({ isMainRoute }) => {
  const { isTabletAndLess } = useMedia();

  if (isTabletAndLess) {
    return (
      <ModalDialog contentClassName={classes.modalContent} isOpen>
        <ModalBody className={classes.modalBody}>
          <Skeleton className={classes.modalHeaderSkeleton} />
          {isMainRoute ? (
            <Flex
              className={cnj(classes.leftWrapper, classes.leftSkeletonWrapper)}
            >
              {Array(9)
                .fill(null)
                .map((_, idx) => (
                  <Skeleton
                    className={classes.leftItemSkeleton}
                    key={`link-skeleton-${idx}`}
                  />
                ))}
            </Flex>
          ) : (
            <Skeleton className={classes.mainContentSkeleton} />
          )}

          <Skeleton />
        </ModalBody>
      </ModalDialog>
    );
  }

  return (
    <Flex className={cnj(classes.contentRoot)}>
      <Flex className={cnj(classes.legalLayoutWrapper)}>
        <Flex className={cnj(classes.leftWrapper, classes.leftSkeletonWrapper)}>
          {Array(9)
            .fill(null)
            .map((_, idx) => (
              <Skeleton
                className={classes.leftItemSkeleton}
                key={`link-skeleton-${idx}`}
              />
            ))}
        </Flex>
        <Flex className={classes.childrenRoot}>
          <Skeleton className={classes.mainContentSkeleton} />
        </Flex>
      </Flex>
    </Flex>
  );
};

export default ListDetailSkeleton;
