'use client';

import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex/index';
import useMedia from 'shared/uikit/utils/useMedia';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalDialog from 'shared/uikit/Modal/ModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Divider from 'shared/uikit/Divider';
import { usePathname, useRouter } from 'next/navigation';
import Breadcrumb from '../Breadcrumb';
import LeftNavBar from '../LeftNavBar';
import classes from './ListDetailLayout.module.scss';

type Props = React.PropsWithChildren<{
  base: string;
  links: Array<string>;
  bottomComponent?: React.ReactElement;
  current: any;
  baseRouteName: string;
}>;

const ListDetailLayout: React.FC<Props> = ({
  base,
  children,
  links,
  bottomComponent,
  current,
  baseRouteName,
}) => {
  const { t } = useTranslation();
  const { isTabletAndLess } = useMedia();
  const router = useRouter();
  const pathname = usePathname();

  const isLegal = pathname.includes(landingRouteNames.legal);

  const onBackHandler = () => router.replace(landingRouteNames.legal);

  if (current?.hash && isTabletAndLess) {
    return (
      <ModalDialog contentClassName={classes.modalContent} isOpen>
        <ModalHeaderSimple
          backButtonProps={{ onClick: onBackHandler }}
          title={t(current.name)}
          titleProps={{ lineNumber: 1, isWordWrap: true, isTruncated: true }}
        />
        <ModalBody className={classes.modalBody}>
          <Flex className={classes.iframeWrapper} id="iframeWrapper">
            <Breadcrumb
              baseRouteName={baseRouteName}
              base={base}
              current={current?.name}
            />
            {children}
            {bottomComponent}
          </Flex>
        </ModalBody>
      </ModalDialog>
    );
  }

  return (
    <Flex className={cnj(classes.contentRoot)}>
      <Flex
        className={cnj(
          classes.legalLayoutWrapper,
          !current?.hash && classes.displayFlex
        )}
      >
        <Flex className={cnj(classes.leftWrapper)}>
          <LeftNavBar activeLink={current?.hash} links={links} />
        </Flex>
        <Flex className={classes.childrenRoot}>
          <Flex
            className={cnj(
              classes.childrenWrapper,
              !isLegal && classes.borderRadius
            )}
          >
            <Breadcrumb
              baseRouteName={baseRouteName}
              base={base}
              current={current?.name}
            />
            <Divider className={classes.divider} />
            {children}
            {bottomComponent}
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default ListDetailLayout;
