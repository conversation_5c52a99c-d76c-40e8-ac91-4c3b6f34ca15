import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import type { PropsWithChildren } from 'react';
import React from 'react';
import { useRouter } from 'next/navigation';
import classes from './header.module.scss';

interface Props extends PropsWithChildren {
  title?: string;
}

const AuthFormLayout: React.FC<Props> = ({ title, children }) => {
  const router = useRouter();

  const onClick = () => router.back();

  return (
    <>
      <ModalHeaderSimple
        backButtonProps={{
          className: classes.backButton,
          onClick,
        }}
        closeButtonProps={{ onClick, className: classes.closeButton }}
        className={classes.header}
        title={title}
      />
      <ModalBody className={classes.modalBody}>{children}</ModalBody>
    </>
  );
};

export default AuthFormLayout;
