'use client';

import React from 'react';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import { useSwiper } from 'swiper/react';
import cnj from 'shared/uikit/utils/cnj';
import classes from './index.module.scss';

const SwiperNavigation = ({ activeIndex, length }): JSX.Element => {
  const swiper = useSwiper();
  const skeletons = Array.from({ length }, (_, i) => i);

  return (
    <Flex className={classes.bottomSection}>
      <IconButton
        size="tiny"
        onClick={() => swiper.slidePrev()}
        name="chevron-left"
        colorSchema="transparentSmokeCoal"
      />
      <Flex className={classes.dotsRoot}>
        {skeletons.map((item) => (
          <Flex
            key={item}
            className={cnj(
              classes.dot,
              item === activeIndex ? classes.activeDot : classes.inActiveDot
            )}
          />
        ))}
      </Flex>
      <IconButton
        onClick={() => swiper.slideNext()}
        size="tiny"
        name="chevron-right"
        colorSchema="transparentSmokeCoal"
      />
    </Flex>
  );
};

export default SwiperNavigation;
