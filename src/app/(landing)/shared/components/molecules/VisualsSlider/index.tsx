'use client';

import 'swiper/css';

import React, { useState } from 'react';
import appEnvironment from 'shared/utils/constants/env';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { Swiper, SwiperSlide } from 'swiper/react';
import cnj from 'shared/uikit/utils/cnj';
import Typography from 'shared/uikit/Typography';
import Image from 'shared/uikit/Image';
import Flex from 'shared/uikit/Flex/index';
import useTheme from 'shared/uikit/utils/useTheme';
import SwiperNavigation from './SwiperNavigation';
import classes from './index.module.scss';

const VisualsSlider = (): JSX.Element => {
  const [activeIndex, setActive] = useState(0);
  const { t } = useTranslation();
  const onSlideChange = (swiper) => setActive(swiper.activeIndex);
  const { isDark } = useTheme();

  const slides = [
    {
      key: '1',
      title: t('usr_post_parent'),
      subTitle: t('usr_post_chld_3'),
    },
    {
      key: '2',
      title: t('usr_page_parent'),
      subTitle: t('elevate_y_m_b_prop'),
    },
    {
      key: '3',
      title: t('schedule_sync_succ'),
      subTitle: t('usr_post_chld_3'),
    },
    {
      key: '4',
      title: t('usr_jobs_parent'),
      subTitle: t('navigate_y_c_p_w_opp'),
    },
  ];

  return (
    <Flex className={classes.swapperRoot}>
      <Swiper
        onSlideChange={onSlideChange}
        // navigation={{}}
        className={classes.swapper}
        slidesPerView={1}
      >
        {slides.map(({ key, title, subTitle }) => (
          <SwiperSlide key={key}>
            <Flex className={cnj(classes.slideRoot)}>
              <Typography
                color="smoke_coal"
                font="700"
                size={32}
                height={38}
                textAlign="center"
              >
                {title}
              </Typography>
              <Typography
                color="smoke_coal"
                font="400"
                size={16}
                height={19}
                textAlign="center"
                mt={12}
              >
                {subTitle}
              </Typography>
              <Image
                defaultTag
                className={classes.image}
                src={`${appEnvironment.storageBaseUrl}/assets/images/slider/${isDark ? 'd' : 'l'}${key}.png`}
                alt={title}
              />
            </Flex>
          </SwiperSlide>
        ))}
        <span className={classes.navigationRoot} slot="container-end">
          <SwiperNavigation activeIndex={activeIndex} length={slides.length} />
        </span>
      </Swiper>
    </Flex>
  );
};

export default VisualsSlider;
