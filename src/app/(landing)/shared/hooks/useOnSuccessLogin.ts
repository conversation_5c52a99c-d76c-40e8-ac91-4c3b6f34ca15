import useHistory from 'shared/utils/hooks/useHistory';
import { landingRouteNames } from 'shared/utils/constants/routeNames';
import appEnvironment from 'shared/utils/constants/env';
import { routeNames } from 'shared/utils/constants/routeNames';
import getQueryParams from '@/app/(landing)/shared/utils/getQueryParams';
import Cookies from 'shared/utils/toolkit/cookies';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';

type OnSuccessType = (a: any, b?: any, c?: any) => any;

const useOnSuccessLogin = (): OnSuccessType => {
  const history = useHistory();
  const TWO_FACTOR_LOGIN_DATA = getCookieKey('twoFactorLoginData');

  return (data: any) => {
    const signUpCompleted = data?.signUpCompleted;
    if (data?.totpToken && !data?.accessToken) {
      Cookies.set(TWO_FACTOR_LOGIN_DATA, data);
      return history.push(landingRouteNames.twoFactorAuthGetCode);
    }
    if (!signUpCompleted) {
      const NOT_COMPLETED_SIGNUP_DATA = getCookieKey('notSignupCompletedData');
      Cookies.set(NOT_COMPLETED_SIGNUP_DATA, data);
      history.push(landingRouteNames.getName);
    } else {
      Cookies.remove(TWO_FACTOR_LOGIN_DATA);
      const OBJ_TOKEN = getCookieKey('userObjToken');
      Cookies.set(OBJ_TOKEN, data);
      const qs = getQueryParams(window.location.search); // @ts-ignore
      const redirect = qs?.redirect;
      const next = redirect?.includes('business.lobox')
        ? redirect
        : `${appEnvironment.baseUrl}${redirect}`;
      window.location.replace(redirect ? next : routeNames.home);
    }
  };
};

export default useOnSuccessLogin;
