'use client';

import Skeleton from 'shared/uikit/Skeleton';
import Flex from 'shared/uikit/Flex';
import Divider from 'shared/uikit/Divider';

const Loading = (): JSX.Element => (
  <Flex className="flex flex-col p-16 md:p-0">
    <Skeleton className="h-[43px] mb-24_32 mx-auto mt-32 md:mt-0" />
    <Skeleton className="h-[28px] mb-24_32" />
    <Skeleton className="h-[48px] mb-8_12" />
    <Skeleton className="h-[48px]" />
    <Divider className="mt-[28px] mb-[28px]" />
    <Skeleton className="h-[48px] mb-20" />
    <Skeleton className="h-[44px]" />
    <Divider className="mb-[28px] mt-[40px]" />
    <Skeleton className="h-[48px]" />
  </Flex>
);

export default Loading;