'use client';

import React, { useEffect } from 'react';
import Box from 'shared/uikit/Layout/Box';
import Button from 'shared/uikit/Button';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import { subscribe } from 'shared/utils/api/invite';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useSearchParams } from 'next/navigation';
import UnsubscribeSvg from 'shared/svg/landing/Unsubscribe';
import AuthFooter from 'shared/uikit/Footer/AuthFooter.component';
import Header from 'shared/components/layouts/AppLayout/AppLayout.Header';
import FooterSmall from 'shared/uikit/Footer/FooterSmall';
import useMedia from 'shared/uikit/utils/useMedia';
import { LANDING } from '../shared/constants/enums';
import classes from './page.module.scss';

export interface UnsubscribeProps {
  className?: string;
}

const SubscribedAgain = ({ className }: UnsubscribeProps): JSX.Element => {
  const { t } = useTranslation();
  const searchClass = useSearchParams();
  const { isTabletAndLess } = useMedia();
  const email = searchClass.get('email');
  const token = searchClass.get('subscriptionToken');
  const type = searchClass.get('type');

  const { mutate } = useReactMutation({
    apiFunc: subscribe,
  });

  useEffect(() => {
    mutate(
      {
        type,
        token,
        email,
      },
      {
        onSuccess: () => {},
      }
    );
  }, []);

  return (
    <>
      <Header isLanding />
      <Flex className={cnj(classes.subscribedAgainRoot, LANDING, className)}>
        <Box className={classes.mainWrapper} id="container">
          <UnsubscribeSvg />
          <Typography font="bold" size={20} color="smoke_coal" mt={20}>
            {t('understood_welcome_back')}
          </Typography>
          <Typography
            font="400"
            size={15}
            color="graphene"
            textAlign="center"
            mt={8}
          >
            {t('subscribed_again_message')}
          </Typography>
          <Button
            className={classes.button}
            label={t('manage_email_preferences')}
            to="/settings/preferences"
          />
        </Box>
        <Flex
          className={
            isTabletAndLess
              ? classes.mobileFooterWrapper
              : classes.desktopFooterWrapper
          }
        >
          {isTabletAndLess ? <FooterSmall /> : <AuthFooter />}
        </Flex>
      </Flex>
    </>
  );
};

export default SubscribedAgain;
