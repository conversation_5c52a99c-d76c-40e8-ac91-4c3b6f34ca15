import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import classes from './Welcome.dots.module.scss';

interface DotsProps {
  length: number;
  activeSlide: number;
}

const Dots: React.FC<DotsProps> = ({ length, activeSlide }) => (
  <Flex className={classes.dotsRoot}>
    {/* @ts-ignore */}
    {[...Array(length).keys()].map((item) => (
      <Flex
        key={item}
        className={cnj(
          classes.dot,
          item === activeSlide ? classes.activeDot : classes.inActiveDot
        )}
      />
    ))}
  </Flex>
);

export default Dots;
