'use client';

import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Typography from 'shared/uikit/Typography';
import Flex from 'shared/uikit/Flex';
import Image from 'shared/uikit/Image';
import Logo from 'shared/svg/landing/LogoIcon';
import useCssVariables from 'shared/hooks/useCssVariables';
import classes from './Welcome.slide.module.scss';

interface SlideProps {
  title: string;
  subTitle: string;
  image: any;
  width: number;
}

const Slide: React.FC<SlideProps> = ({ title, subTitle, image, width }) => {
  const cssVars = useCssVariables({
    scope: classes.slideRoot,
    variables: { rootWidth: `${width}px` },
  });

  return (
    <Flex className={cnj(classes.slideRoot)}>
      {cssVars}
      <Logo visiblelobox className={classes.logo} />
      <Image defaultTag className={classes.image} src={image} alt={title} />
      <Typography
        color="coal"
        font="300"
        size={28}
        height={33}
        mt={20}
        textAlign="center"
      >
        {title}
      </Typography>
      <Typography
        color="coal"
        font="400"
        size={15}
        height={21}
        mt={28}
        className={classes.subTitle}
        textAlign="center"
      >
        {subTitle}
      </Typography>
    </Flex>
  );
};

export default Slide;
