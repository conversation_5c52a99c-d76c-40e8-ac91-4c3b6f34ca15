'use client';

import React, { useState } from 'react';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import Cookies from 'shared/utils/toolkit/cookies';
import isClient from 'shared/utils/toolkit/isClient';
import { replaceUrl } from 'shared/utils/toolkit/redirection';
import { routeNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useCssVariables from 'shared/hooks/useCssVariables';
import cnj from 'shared/uikit/utils/cnj';
import { INVITED_BY_KEY, LANDING } from '../shared/constants/enums';
import langSvg from './images/lang.svg';
import welcomeSvg from './images/welcome.svg';
import amazingSvg from './images/amazing.svg';
import Slide from './Welcome.slide';
import Dots from './Welcome.dots';
import classes from './page.module.scss';

const Welcome = (): JSX.Element => {
  const { t } = useTranslation();
  const slides = [
    {
      key: 'one',
      title: t('welcome_title1'),
      subTitle: t('welcome_hint1'),
      image: welcomeSvg,
    },
    {
      key: 'two',
      title: t('welcome_title2'),
      subTitle: t('welcome_hint2'),
      image: langSvg,
    },
    {
      key: 'three',
      title: t('welcome_title3'),
      subTitle: t('welcome_hint3'),
      image: amazingSvg,
    },
  ];
  const getWidth = () =>
    isClient() && window.innerWidth < 540 ? window.innerWidth : 440;
  const [state, setState] = useState({
    translate: 0,
    transition: 0.45,
    activeSlide: 0,
  });
  const { translate, transition, activeSlide } = state;
  const isLastSlide = activeSlide === slides.length - 1;
  const cssVars = useCssVariables({
    scope: classes.welcomeRoot,
    variables: {
      slidesWidth: `${getWidth()}px`,
      wrapWidth: `${getWidth() * slides.length}px`,
      translate: `${translate}px`,
      transition,
    },
  });
  const nextSlide = () => {
    setState({
      ...state,
      translate: translate + getWidth(),
      activeSlide: activeSlide + 1,
    });
  };
  const doneHandler = () => {
    const invitedBy = Cookies.get(INVITED_BY_KEY);
    replaceUrl(invitedBy ? `/${invitedBy}` : routeNames.home);
  };

  return (
    <Flex className={cnj(classes.welcomeRoot, LANDING)}>
      {cssVars}
      <Flex className={classes.wrap}>
        <Flex className={classes.slides}>
          <Flex className={classes.slideWrap}>
            {slides.map(({ key, title, subTitle, image }) => (
              <Slide
                key={key}
                title={title}
                subTitle={subTitle}
                image={image}
                width={getWidth()}
              />
            ))}
          </Flex>
        </Flex>
        <Flex className={classes.bottomSection}>
          <Dots {...{ activeSlide, length: slides.length }} />
          <Flex className={classes.skipWrap}>
            {!isLastSlide && (
              <Button
                fullWidth
                schema="ghost"
                onClick={doneHandler}
                labelFont="bold"
                labelColor="brand"
                label={t('skip')}
              />
            )}
            {!isLastSlide && <Flex className={classes.divider} />}
            <Button
              fullWidth
              schema="semi-transparent"
              className={classes.nextBtn}
              onClick={isLastSlide ? doneHandler : nextSlide}
              labelFont="bold"
              leftIcon="arrow-right"
              leftType="far"
              leftIconClassName={classes.icon}
              label={isLastSlide ? 'Lets get started' : 'Next'}
            />
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default Welcome;
