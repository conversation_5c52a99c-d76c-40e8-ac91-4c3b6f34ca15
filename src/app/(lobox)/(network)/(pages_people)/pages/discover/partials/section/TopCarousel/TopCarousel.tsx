import React from 'react';
import Carousel from 'shared/uikit/Carousel';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { routeNames } from 'shared/utils/constants/routeNames';
import useGetPopularPageCategories from 'shared/hooks/api-hook/useGetPopularPageCategories';
import { carouselPlaceholderData } from 'shared/constants/enums';
import SectionTitle from 'shared/components/Organism/Objects/Common/SectionTitle';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import PageCategoryItem from 'shared/components/Organism/PageCategoryItem/PageCategoryItem';
import classes from './TopCarousel.module.scss';

const TopCarousel: React.FC = () => {
  const { t } = useTranslation();
  const { data = [], isLoading } = useGetPopularPageCategories();
  const navigateSearchPage = useNavigateSearchPage();

  const onClick = (id: string) => () => {
    navigateSearchPage({
      industryIds: id,
      pathname: routeNames.searchPages,
    });
  };

  return (
    <>
      <SectionTitle title={t('pp_category')} />

      <Carousel
        className={classes.carousel}
        visibleHead={false}
        moveWalkDistance={208}
      >
        {(isLoading ? carouselPlaceholderData : data)?.map(
          ({ id, title, imageUrl }) => (
            <PageCategoryItem
              onClick={onClick(id)}
              key={id}
              isLoading={isLoading}
              title={title}
              cover={imageUrl}
              className={classes.item}
            />
          )
        )}
      </Carousel>
    </>
  );
};

export default TopCarousel;
