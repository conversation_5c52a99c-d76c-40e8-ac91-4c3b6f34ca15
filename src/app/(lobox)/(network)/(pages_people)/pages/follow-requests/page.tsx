'use client';

import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { getMyFollowRequests } from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import useScrollReachEnd from 'shared/utils/hooks/useScrollReachEnd';
import FollowRequestItem from 'shared/components/molecules/FollowRequestItem/FollowRequestItem';
import { INIT_API_SIZE, PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import useCardMenuBuilder from 'shared/hooks/useCardMenuBuilder';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import InfiniteScrollUnitSection from 'shared/components/Organism/UnitSection/InfiniteScrollUnitSection';
import type { PageType } from 'shared/types/page';
import type { BlockGen } from 'shared/types';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';

export interface PeopleFollowRequestsProps {
  noHead?: boolean;
}

const PagesFollowRequests = ({
  noHead,
}: PeopleFollowRequestsProps): JSX.Element => {
  const { t } = useTranslation();
  const {
    data = [],
    totalElements,
    fetchNextPage,
    isLoading,
    hasNextPage,
  } = useInfiniteQuery<BlockGen<PageType>>([QueryKeys.getMyFollowPageRequest], {
    func: getMyFollowRequests,
    size: noHead ? undefined : INIT_API_SIZE,
    extraProps: {
      userType: 'PAGE',
      includeMutualCounter: true,
    },
  });
  const { reFetchAppObject } = useGetAppObject();

  const { remove, replace } = useUpdateInfinityData([
    QueryKeys.getMyFollowPageRequest,
  ]);

  const success = (id: string) => () => {
    // remove(id);
    reFetchAppObject();
  };

  const handleBlock = (item: PageType, isBlocked: boolean) => () => {
    const newItem = { ...item, isBlocked };
    replace(newItem);
  };
  const handleRemove = (id: string) => () => {
    remove(id);
  };

  useScrollReachEnd({
    scrollEl: PROFILE_SCROLL_WRAPPER,
    callback: noHead ? fetchNextPage : undefined,
  });

  const menuBuilder = useCardMenuBuilder({
    isPage: true,
    queryKeyToUpdate: [QueryKeys.getMyFollowPageRequest],
  });

  return (
    <InfiniteScrollUnitSection
      isPage
      hasNextPage={hasNextPage}
      showDefaultCard
      avatarProps={{
        isCompany: true,
      }}
      sectionProps={{
        noHead,
        resultsNumber: totalElements,
        title: t('incoming_requests'),
        resultsCaption: 'page',
      }}
      isLoading={isLoading}
      list={data?.map((page) => {
        const menu = menuBuilder({ ...page, block: handleBlock(page, true) });

        return {
          id: page?.id,
          image: page?.croppedImageUrl,
          title: page?.title,
          subtitle: `@${page?.username}`,
          to: `/${page?.username}`,
          postsCounter: page.postsCounter,
          croppedHeaderImageLink: page.croppedHeaderImageLink,
          locationTitle: page.location?.title,
          description: page?.category,
          followers: page?.followersCounter,
          following: page?.followingsCounter,
          actions: (
            <FollowRequestItem
              id={page?.id}
              username={page?.username}
              onSuccessAccept={success(page?.id)}
              onSuccessDecline={success(page?.id)}
            />
          ),
          moreList: menu,
          isBlocked: page.isBlocked,
          undoBlock: handleBlock(page, false),
          remove: handleRemove(page.id),
          username: page.username as string,
        };
      })}
    />
  );
};

export default PagesFollowRequests;
