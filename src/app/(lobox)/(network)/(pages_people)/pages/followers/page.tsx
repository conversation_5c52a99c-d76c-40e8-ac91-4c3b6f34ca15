'use client';

import React from 'react';
import { getMyFollowerPages } from 'shared/utils/api/network/page';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useTranslation from 'shared/utils/hooks/useTranslation';
import InfiniteScrollObjectListWithApi from 'shared/components/Organism/InfiniteScrollObjectListWithApi';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';

const Followers = (): JSX.Element => {
  const { t } = useTranslation();
  const PagesFollowersDataKey = [QueryKeys.getMyFollowerPages];
  const { getAppObjectPropValue } = useGetAppObject();
  const userId = getAppObjectPropValue({
    pageKey: 'id',
    userKey: 'id',
  });

  return (
    <InfiniteScrollObjectListWithApi
      entity="page"
      apiFunc={getMyFollowerPages}
      queryKey={PagesFollowersDataKey}
      extraProps={{
        userId,
        includeMutualCounter: true,
      }}
      sectionProps={{
        resultsCaption: 'follower',
        title: t('followers_cap'),
      }}
      cardClickProps={{
        [searchFilterQueryParams.searchGroupType]: searchGroupTypes.FOLLOWERS,
      }}
      emptyMessage={t('y_d_h_a_follower_d_s_pages')}
    />
  );
};

export default Followers;
