'use client';

import React from 'react';
import { getMyFollowingPages } from 'shared/utils/api/network/page';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import InfiniteScrollObjectListWithApi from 'shared/components/Organism/InfiniteScrollObjectListWithApi';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';

const PagesDiscover = (): JSX.Element => {
  const { t } = useTranslation();
  const { getAppObjectPropValue } = useGetAppObject();
  const FollowingDataKey = [QueryKeys.getMyFollowingPages];

  const userId = getAppObjectPropValue({
    pageKey: 'id',
    userKey: 'id',
  });

  return (
    <InfiniteScrollObjectListWithApi
      entity="page"
      apiFunc={getMyFollowingPages}
      queryKey={FollowingDataKey}
      extraProps={{
        userId,
        includeMutualCounter: true,
      }}
      sectionProps={{
        resultsCaption: t('following'),
        title: t('following_cap'),
        noPluralization: true,
      }}
      cardClickProps={{
        [searchFilterQueryParams.searchGroupType]: searchGroupTypes.FOLLOWINGS,
      }}
      emptyMessage={t('y_d_h_a_following_d_s_pages')}
    />
  );
};

export default PagesDiscover;
