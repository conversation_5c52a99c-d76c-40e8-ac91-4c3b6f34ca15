import PeoplePagesTabLayout from 'shared/components/layouts/PeoplePagesTabLayout';
import React from 'react';
import { appPortalsObject, getPortal } from 'shared/utils/getAppEnv';
import Coming from '../../../coming/page';

interface LayoutProps {
  children?: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  if (getPortal() === appPortalsObject.recruiter) return <Coming />;
  return <PeoplePagesTabLayout type="pages">{children}</PeoplePagesTabLayout>;
}
