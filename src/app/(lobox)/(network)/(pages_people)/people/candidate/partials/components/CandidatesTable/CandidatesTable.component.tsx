import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import ResponsiveTable from 'shared/uikit/Table/ResponsiveTable';
import Spinner from 'shared/uikit/Spinner';
import Typography from 'shared/uikit/Typography';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useUpdateQueryData from 'shared/utils/hooks/useUpdateQueryData';
import usePaginateQuery from 'shared/utils/hooks/usePaginateQuery';
import adminApi from 'shared/utils/api/admin';
import type { IJob } from 'shared/types/job';
import type { JobStatusType } from 'shared/types/job';
import TablePagination from 'shared/components/Organism/TablePagination';
import useCandidatesListData from './CandidatesTable.useData';
import classes from './CandidatesTable.component.module.scss';

interface CandidatesTableProps {
  status: JobStatusType;
  title: string;
}

const size = 10;

const CandidatesTable: React.FC<CandidatesTableProps> = ({ status, title }) => {
  const {
    totalElements,
    totalPages,
    content = [],
    isEmpty,
    isFetching,
    page,
    setPage,
    queryKey,
  } = usePaginateQuery<IJob>({
    action: {
      apiFunc: adminApi.getCandidatesList, // adminApi.getCandidatesList,
      key: [QueryKeys.getCandidatesList],
      // params: {
      //   isBusinessApp,
      //
      // },
    },
  });

  const { replace } = useUpdateQueryData(queryKey);
  const onStatusChange = (job: IJob) => {
    replace(job);
  };

  const toggleJobOwnersModal = () => {};
  const setSelectedJob = () => {};

  const { columns, renderMobileRow } = useCandidatesListData({
    status,
    onStatusChange,
    toggleJobOwnersModal,
    setSelectedJob,
  });

  if (isEmpty) {
    return <Typography>no candidates</Typography>;
  }

  return (
    <>
      {isFetching ? (
        <Spinner />
      ) : (
        <ResponsiveTable
          columns={columns}
          dataList={content}
          // rowLinkTo={(item: IJob) => routeNames.jobsComing.makeRoute(item.id)}
          rowLinkTo={() => {}}
          renderMobileRow={renderMobileRow}
          classesNames={{
            tRow: classes.tRow,
            tHead: classes.tHead,
            tableCell: classes.tableCell,
          }}
        />
      )}

      {totalPages > 1 && (
        <TablePagination
          onPageChange={setPage}
          pageNumber={page}
          size={size}
          totalElements={Number(totalElements)}
          totalPages={Number(totalPages)}
          className={cnj(classes.pagination, isFetching && classes.loading)}
          footerClassName={classes.footer}
          hasFilter={false}
        />
      )}
    </>
  );
};

export default CandidatesTable;
