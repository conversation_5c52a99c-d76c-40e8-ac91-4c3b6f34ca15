import React from 'react';
import AvatarCard from 'shared/uikit/AvatarCard';
import Button from 'shared/uikit/Button';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import Typography from 'shared/uikit/Typography';
import { routeNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { IJob } from 'shared/types/job';
import type { JobStatusType } from 'shared/types/job';
import classes from './CandidatesTable.useData.module.scss';

interface useCandidatesListDataProps {
  onStatusChange: (args: any) => void;
  toggleJobOwnersModal: (arg: boolean) => void;
  setSelectedJob: (arg: IJob) => void;
  status: JobStatusType;
}

const useCandidatesListData = ({
  status,
  onStatusChange,
  toggleJobOwnersModal,
  setSelectedJob,
}: useCandidatesListDataProps) => {
  const { t } = useTranslation();

  const CandidateAvatar = ({ user }) => (
    <AvatarCard
      noHover
      withPadding={false}
      data={{
        username: user.username,
        image: user.croppedImageUrl,
        title: user.fullName,
        subTitle: user.occupation?.label,
      }}
    />
  );
  const Meetings = () => (
    <IconButton
      size="md"
      className={classes.icon}
      name="meetings"
      type="far"
      iconProps={{ color: 'cornflowerBlue' }}
    />
  );
  const Tasks = () => (
    <IconButton
      size="md"
      className={classes.icon}
      name="tasks"
      type="far"
      iconProps={{ color: 'darkError' }}
    />
  );
  const Notes = () => (
    <IconButton
      size="md"
      className={classes.icon}
      name="notes"
      type="far"
      iconProps={{ color: 'darkTangerine' }}
    />
  );
  const Reminders = () => (
    <IconButton
      size="md"
      className={classes.icon}
      name="reminders"
      type="far"
      iconProps={{ color: 'heliotrope' }}
    />
  );
  const Location = ({ isTabletAndLess, address }: any) => (
    <Typography
      mt={isTabletAndLess ? 2 : 0}
      size={isTabletAndLess ? 12 : 15}
      height={isTabletAndLess ? 18 : 21}
      color={isTabletAndLess ? 'secondaryDisabledText' : 'thirdText'}
      isTruncated
      isWordWrap
      lineNumber={1}
    >
      {address?.label}
    </Typography>
  );

  const Track = ({ className, job }: any) => (
    <Button
      label={t('track')}
      schema="semi-transparent"
      rightIcon="chevron-right"
      to={routeNames.coming}
      rightType="far"
      className={cnj(classes.chevronIcon, className)}
    />
  );
  const Tags = ({ className, job }: any) => <Typography>tags</Typography>;

  const columns = [
    {
      key: 'title',
      title: t('user_cap'),
      render: ({ dataRow: user }: any) => <CandidateAvatar user={user} />,
    },
    {
      key: 'location',
      title: t('location'),
      render: ({ value: address }: any) => <Location address={address} />,
    },
    {
      key: 'tags',
      title: t('tags'),
      render: ({ value: status, dataRow: job }: any) => (
        <Tags job={job} status={status} />
      ),
    },

    {
      key: 'meetings',
      title: t('meetings'),
      render: ({ dataRow: job }: { dataRow: IJob }) => <Meetings job={job} />,
    },
    {
      key: 'tasks',
      title: t('tasks'),
      render: ({ dataRow: job }: { dataRow: IJob }) => <Tasks job={job} />,
    },
    {
      key: 'notes',
      title: t('notes'),
      render: ({ dataRow: job }: { dataRow: IJob }) => <Notes job={job} />,
    },
    {
      key: 'reminders',
      title: t('reminders'),
      render: ({ dataRow: job }: { dataRow: IJob }) => <Reminders job={job} />,
    },
    {
      key: 'rightIcon',
      title: '',
      render: ({ dataRow: job }) => (
        <Button
          label={t('view_details')}
          schema="semi-transparent"
          leftIcon="user"
        />
      ),
    },
  ];

  const renderMobileRow = ({ dataRow: user }: { dataRow: IJob }) => (
    <Flex className={classes.mobileRow}>
      <CandidateAvatar user={user} />
      <Location isTabletAndLess address={user.location?.label} />
      {/* <Flex flexDir="row" className={classes.applicationsWrapper}> */}
      {/*   <Flex className={classes.mobileItem}> */}
      {/*     <Typography className={classes.mobileTitle}> */}
      {/*       {t('applicants')} */}
      {/*     </Typography> */}
      {/*      */}
      {/*   </Flex> */}
      {/*   <Flex className={classes.divider} /> */}
      {/*   <Flex className={classes.mobileItem}> */}
      {/*     <Typography className={classes.mobileTitle}>{t('owners')}</Typography> */}
      {/*     <Owners job={job} /> */}
      {/*   </Flex> */}
      {/* </Flex> */}
      {/* <Flex flexDir="row" className={classes.statusWrapper}> */}
      {/*   <Flex className={classes.mobileItem}> */}
      {/*     <Status job={job} status={job?.status} /> */}
      {/*   </Flex> */}
      {/*   <Flex className={classes.divider} /> */}
      {/*   <Flex className={classes.mobileItem}> */}
      {/*     <Track className={cnj(classes.trackButton)} job={job} /> */}
      {/*   </Flex> */}
      {/* </Flex> */}
    </Flex>
  );

  return {
    columns,
    renderMobileRow,
  };
};

export default useCandidatesListData;
