'use client';

import React from 'react';
import Flex from 'shared/uikit/Flex/Flex.component';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Carousel from 'shared/uikit/Carousel';
import { carouselPlaceholderData } from 'shared/constants/enums';
import Section from 'shared/components/molecules/Section';
import UnitSectionSkeleton from 'shared/components/molecules/UnitSectionSkeleton/UnitSectionSkeleton';
import classes from './loading.module.scss';

interface LoadingProps {
  type: 'pages' | 'people';
}

const Loading = ({ type }: LoadingProps) => {
  const isPage = type === 'pages';
  const { t } = useTranslation();

  return (
    <Flex className={classes.rootSkeletonWrapper}>
      <Carousel
        showCenterButtons={false}
        title={t('pp_category')}
        className={classes.carouselSkeleton}
      >
        {carouselPlaceholderData.map(({ id }) => (
          <Flex
            key={`${id}_${isPage ? 'pages' : 'people'}`}
            className={classes.skeletonCarouselItemRoot}
          >
            <Flex className={classes.imageWrapper}>
              <Skeleton className={classes.coverImage} />
            </Flex>
            <Flex className={classes.details}>
              <Skeleton className={classes.title} />
            </Flex>
          </Flex>
        ))}
      </Carousel>
      <Section title={t('top_suggestions')}>
        <UnitSectionSkeleton totalElements={3} isPage={isPage} />
      </Section>
    </Flex>
  );
};

export default Loading;
