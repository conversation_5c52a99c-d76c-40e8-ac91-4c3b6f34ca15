'use client';

import React from 'react';
import {
  getPopularPeople,
  getSuggestionPeople,
} from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { routeNames } from 'shared/utils/constants/routeNames';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { searchPerson } from 'shared/utils/api/search';
import PeopleCard from 'shared/components/Organism/PeopleCard';
import SearchAllSection from 'shared/components/Organism/SearchAllSection';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useHistory from 'shared/utils/hooks/useHistory';
import { searchGroupTypes } from 'shared/constants/search';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import type { BlockGen } from 'shared/types';
import type { PeopleType } from 'shared/types/people';
import { useObjectClicks } from 'shared/hooks/useObjectClicks';
import { TopCarousel } from './partials/section';

const PeopleDiscover = (): JSX.Element => {
  const { t } = useTranslation();
  const suggestionPeopleQueryKey = [QueryKeys.getSuggestionPeople];
  const searchPeopleQueryKey = [QueryKeys.searchUsers, 'discover'];
  const popularPeopleQueryKey = [QueryKeys.getPopularPeople];
  const history = useHistory();
  const navigateSearchPage = useNavigateSearchPage();

  const {
    data: suggestPeopleData,
    isLoading: suggestPeopleIsLoading,
    totalElements: suggestPeopleTotal,
  } = useInfiniteQuery<BlockGen<PeopleType>>(suggestionPeopleQueryKey, {
    func: getSuggestionPeople,
    size: 6,
    extraProps: {
      includeMutualCounter: true,
    },
  });
  const {
    data: searchPeopleData,
    isLoading: searchPeopleIsLoading,
    totalElements: searchPeopleTotal,
  } = useInfiniteQuery<BlockGen<PeopleType>>(searchPeopleQueryKey, {
    func: searchPerson,
    size: 51,
    extraProps: {
      includeMutualCounter: true,
    },
  });
  const {
    data: popularPeoplePeopleData,
    isLoading: popularPeoplePeopleIsLoading,
    totalElements: popularPeopleTotal,
  } = useInfiniteQuery<BlockGen<PeopleType>>(popularPeopleQueryKey, {
    func: getPopularPeople,
    size: 6,
    extraProps: {
      includeMutualCounter: true,
    },
  });

  const isLoading =
    suggestPeopleIsLoading ||
    searchPeopleIsLoading ||
    popularPeoplePeopleIsLoading;

  const isEmpty =
    !isLoading &&
    suggestPeopleData?.length === 0 &&
    searchPeopleData?.length === 0 &&
    popularPeoplePeopleData?.length === 0;

  const onClickHandler =
    (searchGroupType: keyof typeof searchGroupTypes, id: string) => () => {
      navigateSearchPage({
        pathname: routeNames.searchPeople,
        searchGroupType,
        currentEntityId: id,
      });
    };

  const { handleTagClick } = useObjectClicks();

  return (
    <>
      <TopCarousel />
      {isEmpty ? (
        <EmptySectionInModules
          title={t('no_people')}
          text={t('no_items_to_display')}
          buttonProps={{
            title: t('all_people'),
            onClick: () => history.push(routeNames.searchPeople),
          }}
          isFullParent
        />
      ) : (
        <>
          <SearchAllSection
            scopes={[SCOPES.canSeePeopleScreen]}
            renderItem={(item) => (
              <PeopleCard
                people={item}
                queryKey={suggestionPeopleQueryKey}
                onSelectHandler={() =>
                  handleTagClick(item?.username, item?.id, 'people', {
                    searchGroupType: searchGroupTypes.TOP_SUGGESTION,
                  })
                }
              />
            )}
            title={t('top_suggestions')}
            showAllRouteName={`${routeNames.searchPeople}?searchGroupType=${searchGroupTypes.TOP_SUGGESTION}`}
            routeName={(item) => `/${item.username}`}
            visibleShowAll
            isLoading={suggestPeopleIsLoading}
            data={suggestPeopleData}
            useVirtual
          />

          <SearchAllSection
            showAllRouteName={`${routeNames.searchPeople}?searchGroupType=${searchGroupTypes.POPULAR}`}
            routeName={(item) => `/${item.username}`}
            scopes={[SCOPES.canSeePeopleScreen]}
            renderItem={(item) => (
              <PeopleCard
                people={item}
                queryKey={popularPeopleQueryKey}
                onSelectHandler={() =>
                  handleTagClick(item?.username, item?.id, 'people', {
                    searchGroupType: searchGroupTypes.POPULAR,
                  })
                }
              />
            )}
            title={t('popular_people')}
            visibleShowAll
            isLoading={popularPeoplePeopleIsLoading}
            data={popularPeoplePeopleData}
            useVirtual
          />
          <SearchAllSection
            showAllRouteName={`${routeNames.searchPeople}?searchGroupType=${searchGroupTypes.ALL}`}
            routeName={(item) => `/${item.username}`}
            scopes={[SCOPES.canSeePeopleScreen]}
            renderItem={(item) => (
              <PeopleCard
                people={item}
                queryKey={searchPeopleQueryKey}
                onSelectHandler={() =>
                  handleTagClick(item?.username, item?.id, 'people', {
                    searchGroupType: searchGroupTypes.ALL,
                  })
                }
              />
            )}
            title={t('all_people')}
            visibleShowAll
            visibleShowAllBeneathButton
            isLoading={searchPeopleIsLoading}
            data={searchPeopleData}
            useVirtual
          />
        </>
      )}
    </>
  );
};

export default PeopleDiscover;
