'use client';

import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { routeNames } from 'shared/utils/constants/routeNames';
import { getMyFollowRequests } from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import FollowRequestItem from 'shared/components/molecules/FollowRequestItem/FollowRequestItem';
import useCardMenuBuilder from 'shared/hooks/useCardMenuBuilder';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import InfiniteScrollUnitSection from 'shared/components/Organism/UnitSection/InfiniteScrollUnitSection';
import useHistory from 'shared/utils/hooks/useHistory';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import type { BlockGen } from 'shared/types';
import type { PeopleType } from 'shared/types/people';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';

const PeopleFollowRequests = (): JSX.Element => {
  const { t } = useTranslation();
  const menuBuilder = useCardMenuBuilder({
    queryKeyToUpdate: [QueryKeys.getMyFollowPeopleRequest],
  });
  const { reFetchAppObject } = useGetAppObject();
  const history = useHistory();

  const {
    data = [],
    totalElements,
    fetchNextPage,
    isLoading,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery<BlockGen<PeopleType>>(
    [QueryKeys.getMyFollowPeopleRequest],
    {
      func: getMyFollowRequests,
      size: 21,
      extraProps: {
        userType: 'PERSON',
        includeMutualCounter: true,
      },
    }
  );
  const { replace, remove } = useUpdateInfinityData([
    QueryKeys.getMyFollowPeopleRequest,
  ]);
  const { refetch } = useUpdateInfinityData([
    QueryKeys.getMyFollowerPeople,
    '',
  ]);

  const success = (id: string) => () => {
    // remove(id);
    reFetchAppObject();
    refetch();
  };
  const handleBlock = (item: PeopleType, isBlocked: boolean) => () => {
    const newItem = { ...item, isBlocked };
    replace(newItem);
  };
  const handleRemove = (id: string) => () => {
    remove(id);
  };

  const routeToDiscoverPeople = () => {
    history.push(routeNames.peopleDiscover);
  };

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeePeopleFollowingTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <InfiniteScrollUnitSection
        hasNextPage={hasNextPage}
        showDefaultCard
        sectionProps={{
          resultsNumber: totalElements,
          resultsCaption: 'person',
          title: t('requests'),
          noHead: false,
        }}
        isFetchingNextPage={isFetchingNextPage}
        fetchNextPage={fetchNextPage}
        isLoading={isLoading}
        emptyState={
          <EmptySectionInModules
            // caption={fullName as string}
            text={t('empty_following_people')}
            buttonProps={{
              title: t('discover_people'),
              onClick: routeToDiscoverPeople,
            }}
            isFullHeight
          />
        }
        list={data?.map((person) => {
          const menu = menuBuilder({
            ...person,
            block: handleBlock(person, true),
          });

          return {
            id: person?.id,
            image: person?.croppedImageUrl,
            title: `${person?.name} ${person.surname}`,
            postsCounter: person.postsCounter,
            croppedHeaderImageLink: person.croppedHeaderImageLink,
            locationTitle: person.locationTitle,
            subtitle: `@${person?.username}`,
            to: `/${person?.username}`,
            description: person?.occupationName,
            followers: person?.followersCounter,
            following: person?.followingsCounter,
            actions: (
              <FollowRequestItem
                id={person?.id}
                username={person?.username}
                onSuccessAccept={success(person?.id)}
                onSuccessDecline={success(person?.id)}
              />
            ),
            moreList: menu,
            isBlocked: person.isBlocked,
            undoBlock: handleBlock(person, false),
            remove: handleRemove(person.id),
            username: person.username as string,
            isPrivateAccount:
              person.privateProfile &&
              (!person.follow ||
                (person.follow && person.followStatus !== 'ACCEPTED')),
          };
        })}
        cardClickProps={{
          [searchFilterQueryParams.searchGroupType]:
            searchGroupTypes.INCOMING_REQUESTS,
        }}
      />
    </PermissionsGate>
  );
};

export default PeopleFollowRequests;
