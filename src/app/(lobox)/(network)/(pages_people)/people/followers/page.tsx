'use client';

import React from 'react';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import { getMyFollowerPeople } from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useTranslation from 'shared/utils/hooks/useTranslation';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import InfiniteScrollObjectListWithApi from 'shared/components/Organism/InfiniteScrollObjectListWithApi';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';

const Followers = (): JSX.Element => {
  const { t } = useTranslation();
  const peopleFollowersKey = [QueryKeys.getMyFollowerPeople];

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeePeopleFollowerTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <InfiniteScrollObjectListWithApi
        entity="person"
        sectionProps={{
          resultsCaption: 'follower',
          title: t('followers_cap'),
        }}
        apiFunc={getMyFollowerPeople}
        queryKey={peopleFollowersKey}
        cardClickProps={{
          [searchFilterQueryParams.searchGroupType]: searchGroupTypes.FOLLOWERS,
        }}
        extraProps={{
          includeMutualCounter: true,
        }}
        emptyMessage={t('y_d_h_a_follower_d_s_people')}
      />
    </PermissionsGate>
  );
};

export default Followers;
