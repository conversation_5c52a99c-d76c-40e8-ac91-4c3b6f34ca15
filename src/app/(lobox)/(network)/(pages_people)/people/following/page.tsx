'use client';

import React from 'react';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import { getMyFollowingPeople } from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useTranslation from 'shared/utils/hooks/useTranslation';
import InfiniteScrollObjectListWithApi from 'shared/components/Organism/InfiniteScrollObjectListWithApi';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';

const Following = (): JSX.Element => {
  const { t } = useTranslation();
  const peopleFollowingKey = QueryKeys.getMyFollowingPeople;

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeePeopleFollowingTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <InfiniteScrollObjectListWithApi
        entity="person"
        sectionProps={{
          resultsCaption: t('following'),
          title: t('following_cap'),
          noPluralization: true,
        }}
        apiFunc={getMyFollowingPeople}
        queryKey={peopleFollowingKey}
        cardClickProps={{
          [searchFilterQueryParams.searchGroupType]:
            searchGroupTypes.FOLLOWINGS,
        }}
        extraProps={{
          includeMutualCounter: true,
        }}
        emptyMessage={t('y_d_h_a_following_d_s_people')}
      />
    </PermissionsGate>
  );
};

export default Following;
