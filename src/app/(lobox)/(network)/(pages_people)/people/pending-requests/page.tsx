'use client';

import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { getPendingRequests } from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PendingRequestItem from 'shared/components/molecules/PendingRequestItem/PendingRequestItem';
import useCardMenuBuilder from 'shared/hooks/useCardMenuBuilder';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import InfiniteScrollUnitSection from 'shared/components/Organism/UnitSection/InfiniteScrollUnitSection';
import type { BlockGen } from 'shared/types';
import type { PeopleType } from 'shared/types/people';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import { routeNames } from '@shared/utils/constants';
import useHistory from '@shared/utils/hooks/useHistory';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';

const PeoplePendingRequests = (): JSX.Element => {
  const { t } = useTranslation();
  const history = useHistory();

  const { reFetchAppObject } = useGetAppObject();
  const menuBuilder = useCardMenuBuilder({
    queryKeyToUpdate: [QueryKeys.getPendingRequests],
  });
  const {
    data = [],
    fetchNextPage,
    isLoading,
    totalElements,
    isFetchingNextPage,
    hasNextPage,
  } = useInfiniteQuery<BlockGen<PeopleType>>([QueryKeys.getPendingRequests], {
    func: getPendingRequests,
    size: 21,
    extraProps: {
      includeMutualCounter: true,
    },
  });
  const { remove, replace } = useUpdateInfinityData([
    QueryKeys.getPendingRequests,
  ]);

  const success = (item: PeopleType) => {
    reFetchAppObject();
    remove(item.id);
  };

  const handleBlock = (item: PeopleType, isBlocked: boolean) => () => {
    const newItem = { ...item, isBlocked };
    replace(newItem);
  };

  const handleRemove = (id: string) => () => {
    remove(id);
  };
  const routeToDiscoverPeople = () => {
    history.push(routeNames.peopleDiscover);
  };

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeePeopleFollowingTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <InfiniteScrollUnitSection
        queryKey={[QueryKeys.getPendingRequests]}
        hasNextPage={hasNextPage}
        showDefaultCard
        sectionProps={{
          noHead: false,
          resultsNumber: totalElements,
          resultsCaption: 'person',
          title: t('pending'),
        }}
        emptyState={
          <EmptySectionInModules
            title={t('empty_pending_recommendation')}
            buttonProps={{
              title: t('discover_people'),
              onClick: routeToDiscoverPeople,
            }}
            isFullHeight
            isInsideTab
            isFullWidth
          />
        }
        fetchNextPage={fetchNextPage}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        list={data?.map((item: BlockGen<PeopleType>) => {
          const menu = menuBuilder({
            ...item,
            block: handleBlock(item, true),
          });

          return {
            id: item.id,
            image: item.croppedImageUrl,
            title: `${item.name} ${item.surname}`,
            postsCounter: item.postsCounter,
            croppedHeaderImageLink: item.croppedHeaderImageLink,
            locationTitle: item.locationTitle,
            subtitle: `@${item.username}`,
            to: `/${item.username}`,
            description: item.occupationName,
            followers: item.followersCounter,
            following: item.followingsCounter,
            actions: (
              <PendingRequestItem
                object={item}
                onSuccess={() => {
                  success(item);
                }}
              />
            ),
            moreList: menu,
            isBlocked: item.isBlocked,
            undoBlock: handleBlock(item, false),
            remove: handleRemove(item.id),
            username: item.surname,
            isPending: true,
            isPrivateAccount:
              item.privateProfile &&
              (!item.follow ||
                (item.follow && item.followStatus !== 'ACCEPTED')),
          };
        })}
        cardClickProps={{
          [searchFilterQueryParams.searchGroupType]:
            searchGroupTypes.PENDING_REQUESTS,
        }}
      />
    </PermissionsGate>
  );
};

export default PeoplePendingRequests;
