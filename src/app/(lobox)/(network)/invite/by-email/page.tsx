'use client';

import React, { useState } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Box from 'shared/uikit/Layout/Box';
import Typography from 'shared/uikit/Typography';
import Button from 'shared/uikit/Button';
import InputTags from 'shared/uikit/InputTags';
import Section from 'shared/components/molecules/Section';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useStateCallback from 'shared/utils/hooks/useStateCallback';
import useRouteToInvite from 'shared/hooks/useRouteToInvite';
import useSendInvitation from 'shared/hooks/api-hook/useSendInvitation';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import type FailedInvitedType from 'shared/utils/FailedInvitedType';
import ByEmailResults from './partials/ByEmail.results';
import classes from './page.module.scss';

export interface ByEmailProps {
  className?: string;
}

const MAX_TAGS_NUMBER = 10;

const ByEmail = ({ className }: ByEmailProps): JSX.Element => {
  const [showResult, setShowResult] = useState(false);
  const [failures, setFailures] = useStateCallback<Array<FailedInvitedType>>(
    []
  );
  const { t } = useTranslation();
  const [tags, seTags] = useStateCallback<Array<string>>([]);
  const { invite } = useSendInvitation();

  const handleSend = () => {
    invite(tags, {
      onSuccess: ({ failureList }) => {
        setFailures(failureList, () => {
          setShowResult(true);
        });
      },
    });
  };
  const handleChange = (tgs: Array<string>): void => {
    seTags(tgs);
  };

  const routeToInvite = useRouteToInvite();

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeInviteScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Flex className={cnj(classes.byEmailRoot, className)}>
        <Section
          backAction={routeToInvite}
          resultsNumberThreshold={-1}
          resultsNumber={MAX_TAGS_NUMBER - tags?.length}
          resultsCaption={t('remains')}
          title={t('invite_p_t_l')}
        >
          {showResult ? (
            <ByEmailResults
              failures={failures}
              number={(tags?.length || 0) - (failures?.length || 0)}
            />
          ) : (
            <Box className={classes.box}>
              <Typography
                color="thirdText"
                size={20}
                font="700"
                height={24}
                textAlign="center"
                mb={20}
              >
                {t('invite_via_email')}
              </Typography>
              <Flex className={classes.inputWrapper}>
                <Typography
                  font="400"
                  size={15}
                  height={21}
                  color="primaryText"
                  mb={20}
                >
                  {t('enter_email_msg')}
                </Typography>
                <InputTags
                  maxTags={MAX_TAGS_NUMBER}
                  helperText={t('invite_email_help')}
                  errorText={t('enter_valid_email')}
                  label={t('invite_email_ph')}
                  className={classes.inputTags}
                  onChange={handleChange}
                  validateDelay={750}
                />
              </Flex>
              <Flex className={classes.buttonWrapper}>
                <Button
                  onClick={handleSend}
                  label={t('send_invite')}
                  disabled={!tags?.length}
                />
              </Flex>
            </Box>
          )}
        </Section>
      </Flex>
    </PermissionsGate>
  );
};

export default ByEmail;
