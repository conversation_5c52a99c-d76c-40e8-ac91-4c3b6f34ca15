import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Box from 'shared/uikit/Layout/Box';
import Typography from 'shared/uikit/Typography';
import Button from 'shared/uikit/Button';
import Tooltip from 'shared/uikit/Tooltip';
import Avatar from 'shared/uikit/Avatar';
import Link from 'shared/uikit/Link';
import useTheme from 'shared/uikit/utils/useTheme';
import { APP_ENTITIES } from 'shared/utils/constants/app-entities';
import { routeNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Svg from 'shared/svg/ByEmailResult';
import type FailedInvitedType from 'shared/utils/FailedInvitedType';
import classes from './ByEmail.results.module.scss';

export interface ByEmailResultsProps {
  className?: string;
  number?: number;
  failures?: Array<FailedInvitedType>;
}

const ByEmailResults = ({
  className,
  number = 0,
  failures = [],
}: ByEmailResultsProps): JSX.Element => {
  const { t } = useTranslation();
  const { isDark } = useTheme();

  return (
    <Box className={cnj(classes.ByEmailResultsRoot, className)}>
      <Flex className={classes.svgWrapper}>
        <Svg className={classes.svg} />
        <Typography
          className={classes.number}
          color="brand"
          font="900"
          size={72}
          height={85}
        >
          {number}
        </Typography>
      </Flex>
      <Flex className={classes.bottomWrapper}>
        <Typography
          color="primaryText"
          font="400"
          size={15}
          height={21}
          mt={40}
          mb={20}
        >
          {failures?.length
            ? `${failures.length} ${t(
                failures?.length > 1 ? 'existed_fails_msg' : 'existed_fail_msg'
              )}`
            : t('u_invite_more')}
        </Typography>
        {!!failures?.length && (
          <Flex className={classes.existedPane}>
            {failures.map(
              (
                {
                  croppedImageUrl,
                  name,
                  surname,
                  title,
                  username,
                  email,
                  userType,
                },
                index
              ) => (
                <Tooltip
                  key={email}
                  triggerWrapperClassName={cnj(
                    index !== 0 && classes.marginLeft,
                    classes.link
                  )}
                  trigger={
                    <Link to={`/${username}`}>
                      <Avatar
                        imgSrc={croppedImageUrl}
                        size="sm"
                        isCompany={userType === APP_ENTITIES.page}
                      />
                    </Link>
                  }
                >
                  <Flex className={classes.tooltipContentWrapper}>
                    <Typography
                      color="coal_disabledGray"
                      font="500"
                      size={14}
                      height={16}
                    >
                      {title || `${name} ${surname}`}
                    </Typography>
                    <Typography
                      color="gray"
                      font="500"
                      size={12}
                      height={16}
                    >{`@${username}`}</Typography>
                    <Typography
                      color="graphene_hover"
                      font="500"
                      size={14}
                      height={16}
                    >
                      {email}
                    </Typography>
                  </Flex>
                </Tooltip>
              )
            )}
          </Flex>
        )}
        <Flex className={classes.actionButtons}>
          <Button
            to={routeNames.invite}
            label={t('s_m_inv')}
            schema={isDark ? 'ghost-black' : 'ghost'}
            className={classes.inviteMoreButton}
          />
          <Button
            to={routeNames.home}
            label={t('done_now')}
            schema="primary-blue"
          />
        </Flex>
      </Flex>
    </Box>
  );
};

export default ByEmailResults;
