import React from 'react';
import Section from 'shared/components/molecules/Section';
import Box from 'shared/uikit/Layout/Box';
import Skeleton from 'shared/uikit/Skeleton';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import noop from 'lodash/noop';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './ByEmail.skeleton.module.scss';

const ByEmailSkeleton = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Flex className={classes.byEmailSkeletonRoot}>
      <Section backAction={noop} title={t('invite_p_t_l')}>
        <Box className={classes.box}>
          <Typography
            color="thirdText"
            size={20}
            font="700"
            height={24}
            textAlign="center"
            mb={20}
          >
            {t('invite_via_email')}
          </Typography>
          <Flex className={classes.inputWrapper}>
            <Typography
              font="400"
              size={15}
              height={21}
              color="primaryText"
              mb={20}
            >
              {t('enter_email_msg')}
            </Typography>
            <Skeleton className={classes.skeleton__email} />
          </Flex>
          <Flex className={classes.buttonWrapper}>
            <Skeleton className={classes.skeleton__button} />
          </Flex>
        </Box>
      </Section>
    </Flex>
  );
};

export default ByEmailSkeleton;
