import React from 'react';
import Section from 'shared/components/molecules/Section';
import useTranslation from 'shared/utils/hooks/useTranslation';
import noop from 'lodash/noop';
import classes from './Connect.skeleton.module.scss';
import GoogleLogo from '../partials/components/GoogleLogo';
import FollowListSkeleton from '../partials/components/FollowList.skeleton';
import InvitationList from '../partials/components/InvitationList';

const ConnectSkeleton = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Section backAction={noop} hasSearchInput title={t('connect_w_p_l')}>
      <InvitationList
        className={classes.connectSkeletonRoot}
        social={{
          title: 'Google',
          logo: <GoogleLogo />,
        }}
        skeleton={<FollowListSkeleton />}
        isLoading
      />
    </Section>
  );
};

export default ConnectSkeleton;
