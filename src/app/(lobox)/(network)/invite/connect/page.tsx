'use client';

import React from 'react';
import { getFollowList } from 'shared/utils/api/invite';
import {
  MAIN_CENTER_WRAPPER_ID,
  INVITE_STATUS,
} from 'shared/utils/constants/enums';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useScrollReachEnd from 'shared/utils/hooks/useScrollReachEnd';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import Media from 'shared/uikit/Media';
import useTheme from 'shared/uikit/utils/useTheme';
import AdvanceCard from 'shared/components/molecules/AdvanceCard/AdvanceCard.component';
import Section from 'shared/components/molecules/Section';
import useRouteToInvite from 'shared/hooks/useRouteToInvite';
import useFollowUser from 'shared/hooks/api-hook/useFollowUser';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import type ContactFollowItemType from 'shared/utils/ContactFollowItemType';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import classes from './page.module.scss';
import InvitationList from '../partials/components/InvitationList';
import GoogleLogo from '../partials/components/GoogleLogo';
import FollowListSkeleton from '../partials/components/FollowList.skeleton';

const statusListToShow = [INVITE_STATUS.follow];

const InviteConnect = (): JSX.Element => {
  const { setValue, debounceValue } = useDebounceState('', 500);
  const { t } = useTranslation();
  const {
    theme: {
      variables: { gutter },
    },
  } = useTheme();

  const routeToInvite = useRouteToInvite();
  const dataKey = [QueryKeys.getContactFollowList, debounceValue];
  const { data, totalElements, fetchNextPage, isLoading } =
    useInfiniteQuery<ContactFollowItemType>(dataKey, {
      func: getFollowList,
      extraProps: {
        text: debounceValue,
      },
    });
  const { remove } = useUpdateInfinityData(dataKey);

  const dataToShow = data.filter(({ status }) =>
    statusListToShow.some((s) => s === status)
  );
  const { followCall, isLoading: isLoadingFollow } = useFollowUser();

  const handleFollow = (item: any) => (e: any) => {
    preventClickHandler(e);

    followCall(item.ownerProfileInfoId, {
      onSuccess: () => {
        remove(item.id);
      },
    });
  };

  useScrollReachEnd({
    scrollEl: MAIN_CENTER_WRAPPER_ID,
    callback: fetchNextPage,
  });

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeInviteScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Flex className={classes.container}>
        <Section
          backAction={routeToInvite}
          hasSearchInput
          title={t('connect_w_p_l')}
          onSearchChanged={setValue}
        >
          <InvitationList
            emptyState={
              <EmptySectionInModules
                title={t('connect')}
                text={t('no_contact_follow')}
              />
            }
            skeleton={<FollowListSkeleton />}
            isLoading={isLoading}
            data={dataToShow}
            social={{
              title: 'Google',
              message: `${totalElements} ${t(
                totalElements > 1 ? 'contacts' : 'contact'
              )}`,
              logo: <GoogleLogo />,
            }}
            renderItem={(item) => (
              <AdvanceCard
                key={item.id}
                classNames={{
                  root: classes.itemWrapper,
                }}
                action={
                  <Media greaterThan="tablet">
                    <Button
                      isLoading={isLoadingFollow}
                      className={classes.bottomAction}
                      label={t('follow_cap')}
                      leftIcon="plus"
                      onClick={handleFollow(item)}
                    />
                  </Media>
                }
                bottomAction={
                  <Media at="tablet">
                    <Button
                      isLoading={isLoadingFollow}
                      className={classes.bottomAction}
                      label={t('follow')}
                      leftIcon="plus"
                      onClick={handleFollow(item)}
                    />
                  </Media>
                }
                data={{
                  objectId: item.ownerProfileInfoId,
                  firstText: `${item.name} ${item.surname}`,
                  firstTextAdditionalProps: {
                    objectId: item.ownerProfileInfoId,
                  },
                  secondTextAdditionalProps: {
                    objectId: item.ownerProfileInfoId,
                  },
                  secondText: `@${item.username}`,
                  thirdText: item.occupationName,
                  image: item.croppedImageUrl,
                }}
                firstTextProps={{
                  font: '700',
                  size: 18,
                  height: 22.5,
                  color: 'thirdText',
                }}
                secondTextProps={{
                  font: '400',
                  size: 15,
                  height: 21,
                  color: 'border',
                }}
                thirdTextProps={{
                  font: '400',
                  size: 15,
                  height: 21,
                  color: 'primaryText',
                  mt: gutter / 2,
                }}
              />
            )}
          />
        </Section>
      </Flex>
    </PermissionsGate>
  );
};

export default InviteConnect;
