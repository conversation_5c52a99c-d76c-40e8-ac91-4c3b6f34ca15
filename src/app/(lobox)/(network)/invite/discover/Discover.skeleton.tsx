import React from 'react';
import Section from 'shared/components/molecules/Section';
import useTranslation from 'shared/utils/hooks/useTranslation';
import noop from 'lodash/noop';
import classes from './Discover.skeleton.module.scss';
import GoogleLogo from '../partials/components/GoogleLogo';
import InvitationListSkeleton from '../partials/components/InvitationList.skeleton';
import InvitationList from '../partials/components/InvitationList';

const DiscoverSkeleton = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Section backAction={noop} hasSearchInput title={t('invite_p_t_l')}>
      <InvitationList
        className={classes.discoverSkeletonRoot}
        social={{
          title: 'Google',
          logo: <GoogleLogo />,
        }}
        skeleton={<InvitationListSkeleton />}
        isLoading
      />
    </Section>
  );
};

export default DiscoverSkeleton;
