'use client';

import React from 'react';
import { getInvitationList } from 'shared/utils/api/invite';
import {
  INVITE_STATUS,
  MAIN_CENTER_WRAPPER_ID,
} from 'shared/utils/constants/enums';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useScrollReachEnd from 'shared/utils/hooks/useScrollReachEnd';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import Flex from 'shared/uikit/Flex';
import Media from 'shared/uikit/Media';
import useTheme from 'shared/uikit/utils/useTheme';
import AdvanceCard from 'shared/components/molecules/AdvanceCard/AdvanceCard.component';
import Section from 'shared/components/molecules/Section';
import useRouteToInvite from 'shared/hooks/useRouteToInvite';
import useSendInvitation from 'shared/hooks/api-hook/useSendInvitation';
import PeopleEmptyState from 'shared/components/Organism/EmptyState/PeopleEmptyState';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import type ContactFollowItemType from 'shared/utils/ContactFollowItemType';
import classes from './page.module.scss';
import InvitationList from '../partials/components/InvitationList';
import GoogleLogo from '../partials/components/GoogleLogo';
import InviteButton from './InviteButton';
import InvitationListSkeleton from '../partials/components/InvitationList.skeleton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';

const statusListToShow = [
  INVITE_STATUS.madeInvitationRequest,
  INVITE_STATUS.invite,
];

const InviteDiscover: React.FC = () => {
  const { setValue, debounceValue } = useDebounceState('', 500);
  const { t } = useTranslation();
  const {
    theme: {
      variables: { gutter },
    },
  } = useTheme();

  const { invite } = useSendInvitation();
  const routeToInvite = useRouteToInvite();

  const dataKey = [QueryKeys.getInvitationList, debounceValue];
  const { data, totalElements, fetchNextPage, isLoading } =
    useInfiniteQuery<ContactFollowItemType>(dataKey, {
      func: getInvitationList,
      extraProps: {
        text: debounceValue,
      },
    });
  const { replace } = useUpdateInfinityData(dataKey);

  const dataToShow = data.filter(({ status }) =>
    statusListToShow.some((s) => s === status)
  );

  const handleInvite = (item: ContactFollowItemType) => () => {
    invite(item.email, {
      onSuccess: () => {
        const newItem = {
          ...item,
          status: INVITE_STATUS.madeInvitationRequest,
        };
        replace(newItem);
      },
    });
  };

  useScrollReachEnd({
    scrollEl: MAIN_CENTER_WRAPPER_ID,
    callback: fetchNextPage,
  });

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeInviteScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Flex className={classes.container}>
        <Section
          backAction={routeToInvite}
          hasSearchInput
          title={t('invite_p_t_l')}
          onSearchChanged={setValue}
        >
          <InvitationList
            social={{
              title: 'Google',
              message: `${totalElements} ${t(
                totalElements > 1 ? 'contacts' : 'contact'
              )}`,
              logo: <GoogleLogo />,
            }}
            emptyState={
              <EmptySectionInModules
                title={t('invite')}
                text={t('no_contact')}
              />
            }
            skeleton={<InvitationListSkeleton />}
            isLoading={isLoading}
            data={dataToShow}
            renderItem={(item) => (
              <AdvanceCard
                key={item.id}
                classNames={{
                  root: classes.itemWrapper,
                }}
                visibleImage={false}
                action={
                  <Media greaterThan="tablet">
                    <InviteButton
                      isSent={
                        item.status === INVITE_STATUS.madeInvitationRequest
                      }
                      onInvite={handleInvite(item)}
                    />
                  </Media>
                }
                bottomAction={
                  <Media at="tablet">
                    <InviteButton
                      isSent={
                        item.status === INVITE_STATUS.madeInvitationRequest
                      }
                      onInvite={handleInvite(item)}
                    />
                  </Media>
                }
                data={{
                  firstText:
                    item.name || item.surname
                      ? `${item.name || ''} ${item.surname || ''}`
                      : item.email,
                  secondText: item.email,
                }}
                firstTextProps={{
                  font: '700',
                  size: 18,
                  height: 22.5,
                  color: 'thirdText',
                }}
                secondTextProps={{
                  font: '400',
                  size: 15,
                  height: 21,
                  color: 'border',
                  mt: gutter / 2,
                }}
              />
            )}
          />
        </Section>
      </Flex>
    </PermissionsGate>
  );
};

export default InviteDiscover;
