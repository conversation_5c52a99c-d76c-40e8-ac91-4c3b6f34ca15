'use client';

import React, { useState } from 'react';
import { invitationEndpoints } from 'shared/utils/constants';
import { redirectUrl } from 'shared/utils/toolkit/redirection';
import { routeNames } from 'shared/utils/constants/routeNames';
import useComponentDidUpdate from 'shared/utils/hooks/useComponentDidUpdate';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Box from 'shared/uikit/Layout/Box';
import Button from 'shared/uikit/Button';
import DividerTitled from 'shared/uikit/Divider/DividerTitled';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useHistory from 'shared/utils/hooks/useHistory';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import Google from 'shared/svg/GoogleIcon';
import EmailIcon from 'shared/svg/EmailIcon';
import classes from './page.module.scss';
import InviteSelect from './partials/components/InviteSelect';

type SelectType = 'google' | 'email';

const InviteHome = (): JSX.Element => {
  const { t } = useTranslation();
  const history = useHistory();
  const [callGoogle, setCallGoogle] = useState<boolean>(false);
  const [selected, setSelected] = useState<SelectType | undefined>();

  const { data } = useReactQuery<any>({
    action: {
      key: ['GoogleAuthContacts'],
      url: invitationEndpoints.google,
    },
    config: {
      enabled: callGoogle,
    },
  });

  const handleSelect = (sT: SelectType) => () => {
    setSelected((s) => (s === sT ? undefined : sT));
  };

  const handleSubmit = () => {
    if (selected === 'google') {
      setCallGoogle(true);
    } else {
      history.push(routeNames.inviteByEmail);
    }
  };

  useComponentDidUpdate(() => {
    if (data?.url) {
      redirectUrl(data.url);
    }
  }, [callGoogle]);

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeInviteScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Flex className={classes.inviteHomeRoot}>
        <Box className={classes.box}>
          <Flex className={classes.titleWrapper}>
            <Typography
              color="thirdText"
              size={20}
              font="700"
              height={24}
              textAlign="center"
            >
              {t('invite_people_title')}
            </Typography>
            <Typography
              size={15}
              height={21}
              textAlign="center"
              className={classes.subTitle}
            >
              {t('invite_people_sub_title')}
            </Typography>
          </Flex>
          <Flex className={classes.buttonWrapper}>
            <Typography
              font="400"
              size={15}
              height={21}
              color="primaryText"
              mb={20}
            >
              {t('sync_invite')}
            </Typography>
            <InviteSelect
              onClick={handleSelect('google')}
              isSelected={selected === 'google'}
              svgIcon={<Google />}
              label={t('google')}
            />
            <DividerTitled
              label={t('or_send_invite')}
              className={classes.dividerTitled}
            />
            <InviteSelect
              onClick={handleSelect('email')}
              isSelected={selected === 'email'}
              svgIcon={<EmailIcon />}
              label={t('email')}
            />
          </Flex>
          <Flex className={classes.actionWrapper}>
            <Button
              isLoading={callGoogle}
              disabled={!selected || callGoogle}
              label={t('continue')}
              onClick={handleSubmit}
              className={classes.defaultWidth}
            />
          </Flex>
        </Box>
      </Flex>
    </PermissionsGate>
  );
};

export default InviteHome;
