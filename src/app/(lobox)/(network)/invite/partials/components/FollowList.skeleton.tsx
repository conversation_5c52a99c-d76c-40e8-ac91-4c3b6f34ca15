import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Skeleton from 'shared/uikit/Skeleton';
import classes from './FollowList.skeleton.module.scss';

export interface FollowListSkeletonProps {
  className?: string;
}

const FollowListSkeleton: React.FC<FollowListSkeletonProps> = ({
  className,
}) => (
  <Flex className={cnj(classes.followListSkeletonRoot, className)}>
    <Skeleton className={classes.skeleton__avatar} />
    <Flex className={classes.infoWrapper}>
      <Skeleton className={classes.skeleton__1} />
      <Skeleton className={classes.skeleton__2} />
      <Skeleton className={classes.skeleton__3} />
      <Skeleton className={classes.skeleton__button} />
    </Flex>
    <Flex className={classes.rightButtonWrapper}>
      <Skeleton className={classes.skeleton__r} />
    </Flex>
  </Flex>
);

export default FollowListSkeleton;
