import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Google from 'shared/svg/GoogleIcon';
import classes from './GoogleLogo.module.scss';

export interface GoogleLogoProps {
  className?: string;
}

const GoogleLogo: React.FC<GoogleLogoProps> = ({ className }) => (
  <Flex className={cnj(classes.googleLogoRoot, className)}>
    <Google width={18} height={18} />
  </Flex>
);

export default GoogleLogo;
