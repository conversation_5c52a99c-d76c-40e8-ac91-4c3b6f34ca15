import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Skeleton from 'shared/uikit/Skeleton';
import classes from './InvitationList.skeleton.module.scss';

export interface InvitationListSkeletonProps {
  className?: string;
}

const InvitationListSkeleton = ({
  className,
}: InvitationListSkeletonProps): JSX.Element => (
  <Flex className={cnj(classes.invitationListSkeletonRoot, className)}>
    <Flex className={classes.buttonsWrapper}>
      <Skeleton className={classes.skeleton__1} />
      <Skeleton className={classes.skeleton__2} />
      <Skeleton className={classes.skeleton__3} />
    </Flex>
    <Flex className={classes.rightButtonWrapper}>
      <Skeleton className={classes.skeleton__r} />
    </Flex>
  </Flex>
);

export default InvitationListSkeleton;
