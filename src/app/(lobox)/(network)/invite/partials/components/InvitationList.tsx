import React, { Fragment } from 'react';
import Avatar from 'shared/uikit/Avatar';
import cnj from 'shared/uikit/utils/cnj';
import Divider from 'shared/uikit/Divider';
import DividerVertical from 'shared/uikit/Divider/DividerVertical';
import Flex from 'shared/uikit/Flex';
import Paper from 'shared/uikit/Paper';
import Typography from 'shared/uikit/Typography';
import isObject from 'lodash/isObject';
import { placeholderData, SKELETON_NUMBERS } from 'shared/constants/enums';
import classes from './InvitationList.module.scss';

interface InvitationListProps {
  className?: string;
  isLoading?: boolean;
  data?: Array<any>;
  renderItem?: (item: any) => React.ReactNode;
  skeleton?: React.ReactNode;
  emptyState?: React.ReactNode;
  social: {
    title: string;
    logo?: React.ReactNode | string;
    message?: string;
  };
}

const InvitationList = ({
  className,
  isLoading,
  data,
  social,
  skeleton = null,
  emptyState = null,
  renderItem,
}: InvitationListProps): JSX.Element => (
  <Paper
    className={cnj(classes.paperRoot, className)}
    noHover
    contentClassName={classes.contentClassName}
  >
    <Flex className={classes.socialMedia} flexDir="row">
      {isObject(social?.logo) ? (
        social.logo
      ) : (
        <Avatar imgSrc={social?.logo as string} size="md" />
      )}
      <Typography ml={16} font="700" size={20} height={24}>
        {social?.title}
      </Typography>
      {!!social?.message && (
        <>
          <DividerVertical color="primaryText" distance={16} height={20} />
          <Typography color="colorIconForth" size={14} height={16}>
            {social.message}
          </Typography>
        </>
      )}
    </Flex>
    <Flex>
      {isLoading
        ? placeholderData('invitation-list').map(({ id }, index) => (
            <Fragment key={id}>
              {skeleton}
              {index !== SKELETON_NUMBERS - 1 && <Divider />}
            </Fragment>
          ))
        : data?.length
          ? data?.map((item, index) => (
              <Flex key={item.id}>
                {renderItem?.(item)}
                {index !== data?.length - 1 && <Divider />}
              </Flex>
            ))
          : emptyState}
    </Flex>
  </Paper>
);

export default InvitationList;
