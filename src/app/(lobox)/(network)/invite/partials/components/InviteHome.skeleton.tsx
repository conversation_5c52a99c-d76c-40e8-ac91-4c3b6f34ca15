import React from 'react';
import Box from 'shared/uikit/Layout/Box';
import cnj from 'shared/uikit/utils/cnj';
import Skeleton from 'shared/uikit/Skeleton';
import classes from './InviteHome.skeleton.module.scss';

export interface InviteHomeSkeletonProps {
  className?: string;
}

const InviteHomeSkeleton: React.FC<InviteHomeSkeletonProps> = ({
  className,
}) => (
  <Box className={cnj(classes.inviteHomeSkeletonRoot, className)}>
    <Skeleton className={classes.first} />
    <Skeleton className={classes.second} />
    <Skeleton className={classes.third} />
    <Skeleton className={classes.forth} />
    <Skeleton className={classes.fifth} />
  </Box>
);

export default InviteHomeSkeleton;
