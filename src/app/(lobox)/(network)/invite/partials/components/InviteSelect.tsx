import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import Icon from 'shared/uikit/Icon';
import classes from './InviteSelect.module.scss';

export interface InviteSelectProps {
  className?: string;
  isSelected?: boolean;
  svgIcon?: React.ReactNode;
  label?: string;
  onClick?: (e: any) => void;
}

const InviteSelect: React.FC<InviteSelectProps> = ({
  className,
  isSelected,
  svgIcon,
  label,
  onClick,
}) => (
  <Flex
    onClick={onClick}
    className={cnj(
      classes.inviteSelectRoot,
      isSelected && classes.selected,
      className
    )}
  >
    {svgIcon && <Flex className={classes.svgWrapper}>{svgIcon}</Flex>}
    <Typography
      font="400"
      size={15}
      height={21}
      color="thirdText"
      ml={6}
      mr={6}
    >
      {label}
    </Typography>
    <Icon
      size={16}
      color="success"
      name="check-circle"
      className={cnj(classes.icon, isSelected && classes.isSelected)}
    />
  </Flex>
);

export default InviteSelect;
