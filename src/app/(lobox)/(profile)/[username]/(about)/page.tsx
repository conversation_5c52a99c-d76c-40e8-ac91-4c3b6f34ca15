import { dehydrate } from '@tanstack/query-core';
import Hydrate from 'shared/utils/hydrate.client';
import getServerSideQueryClient from 'shared/utils/getServerSideQueryClient';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { getAboutSectionsData } from 'shared/utils/api/profile';
import getAccessTokenFromCookies from 'shared/utils/getAccessTokenFromCookies';
import About from './About';

interface PageProps {
  params: Promise<{ username: string }>;
}

export default async function Page({ params }: PageProps) {
  const { username } = await params;
  const queryClient = getServerSideQueryClient();
  const queryKey = [QueryKeys.aboutSections, username];
  const accessToken = await getAccessTokenFromCookies();

  await queryClient.prefetchQuery(queryKey, () =>
    getAboutSectionsData({ accessToken, params: { username } })
  );
  const dehydratedState = dehydrate(queryClient);

  return (
    <Hydrate state={dehydratedState}>
      <About />
    </Hydrate>
  );
}
