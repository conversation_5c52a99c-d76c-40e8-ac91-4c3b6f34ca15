import React from 'react';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import useTheme from 'shared/uikit/utils/useTheme';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import classes from './AddSectionButton.module.scss';

const EditProfileButton: React.FC = () => {
  const { isDark } = useTheme();
  const { t } = useTranslation();

  return (
    <Flex className={classes.addSectionRoot}>
      <Button
        schema={isDark ? 'ghost-black' : 'ghost'}
        label={t('edit_profile')}
        leftIcon="pen"
        leftType="far"
        onClick={() =>
          openMultiStepForm({
            stepKey: profileSectionsStepKeys.EDIT_OR_UPLOAD,
            formName: 'editProfileSections',
          })
        }
      />
    </Flex>
  );
};

export default EditProfileButton;
