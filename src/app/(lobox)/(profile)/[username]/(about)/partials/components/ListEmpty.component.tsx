import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import classes from './ListEmpty.component.module.scss';

interface StyleProps {
  container: string;
  headerText: string;
  content: string;
  title: string;
  subTitle: string;
}

interface Props {
  title?: string;
  subTitle?: string;
  image?: any;
  styles?: Partial<StyleProps>;
}

const ListEmptyComponent: React.FC<Props> = ({
  title,
  subTitle,
  image,
  styles,
}) => (
  <Flex className={cnj(classes.container, styles?.container)}>
    {image}
    {title && (
      <Typography
        textAlign="center"
        size={16}
        font="bold"
        height={20}
        className={cnj(classes.title, styles?.title)}
      >
        {title}
      </Typography>
    )}
    {subTitle && (
      <Typography
        height={21}
        textAlign="center"
        className={cnj(classes.subTitle, styles?.subTitle)}
      >
        {subTitle}
      </Typography>
    )}
  </Flex>
);

export default ListEmptyComponent;
