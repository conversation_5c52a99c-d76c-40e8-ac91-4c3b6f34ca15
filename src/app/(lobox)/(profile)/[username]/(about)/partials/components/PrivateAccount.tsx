import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import NotFound from 'shared/uikit/NotFound';
import useTranslation from 'shared/utils/hooks/useTranslation';
import PendingButton from 'shared/components/molecules/PendingButton/PendingButton';
import FollowButton from 'shared/components/molecules/FollowButton/FollowButton';
import useProfilePage from 'shared/hooks/useProfilePage';
import { followStatus } from 'shared/constants/enums';
import PrivateAccountSvg from 'shared/svg/PrivateAccount';
import classes from './PrivateAccount.module.scss';

interface PrivateAccountProps {
  largeMarginTop?: boolean;
}

const PrivateAccount: React.FC<PrivateAccountProps> = ({
  largeMarginTop,
}): JSX.Element => {
  const { t } = useTranslation();
  const { objectDetail, reFetchPageDetail, network } = useProfilePage();
  const pending = network?.followStatus === followStatus.PENDING;

  return (
    <NotFound
      className={cnj(classes.wrapper, largeMarginTop && classes.largeMarginTop)}
      imageWrapClassName={classes.imageWrap}
      title={t('this_acc_is_private')}
      message={t('this_acc_is_private_sub')}
      image={<PrivateAccountSvg />}
      action={
        <Flex className={classes.actionWrap}>
          {pending ? (
            <PendingButton
              object={{ id: objectDetail?.id, username: objectDetail.username }}
              onSuccess={reFetchPageDetail}
            />
          ) : (
            <FollowButton
              back={objectDetail?.network?.back}
              schema="primary-blue"
              object={{
                id: objectDetail?.id,
                isPage: objectDetail.isPage,
                username: objectDetail.username,
              }}
              onSuccess={reFetchPageDetail}
              className={classes.followingButton}
            />
          )}
        </Flex>
      }
    />
  );
};

export default PrivateAccount;
