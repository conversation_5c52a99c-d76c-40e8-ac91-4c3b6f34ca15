import React from 'react';
import BaseButton from 'shared/uikit/Button/BaseButton';
import useTranslation from 'shared/utils/hooks/useTranslation';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import useProfilePage from 'shared/hooks/useProfilePage';
import useVisibleProfileSectionActionButton from 'shared/hooks/useVisibleProfileSectionActionButton';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import Flex from '@shared/uikit/Flex';
import useAboutSectionData from './about.useData';
import AboutSectionSkeleton from '../../components/AboutSectionSkeleton.component';
import classes from './About.component.module.scss';

interface Props {
  className: string;
}

const AboutSection: React.FC<Props> = ({ className }) => {
  const { t } = useTranslation();
  const data = useAboutSectionData();
  const { isAuthUser, isLoading } = useProfilePage();
  const visibleActionButton = useVisibleProfileSectionActionButton();

  if (isLoading) {
    return <AboutSectionSkeleton />;
  }
  if (data?.length === 0) {
    return null;
  }

  return (
    <Flex className={className}>
      <SectionLayout
        onEditHandler={() =>
          openMultiStepForm({ formName: 'profileAboutEdit' })
        }
        title={t('contact')}
        classNames={{ childrenWrap: classes.childrenWrap }}
        visibleActionButton={visibleActionButton}
      >
        {data.map(({ id, title, subTitle, value, icon, onClick }) => (
          <BaseButton disabled={!isAuthUser} onClick={onClick} key={id}>
            <InfoCard
              {...{
                disabledHover: !isAuthUser,
                title,
                subTitle,
                value,
                icon,
              }}
            />
          </BaseButton>
        ))}
      </SectionLayout>
    </Flex>
  );
};

export default AboutSection;
