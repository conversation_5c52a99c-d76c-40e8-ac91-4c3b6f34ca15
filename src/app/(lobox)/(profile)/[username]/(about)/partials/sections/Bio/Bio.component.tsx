import React from 'react';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Card from 'shared/uikit/Card';
import Flex from 'shared/uikit/Flex';
import Paper from 'shared/uikit/Paper';
import RichTextView from 'shared/uikit/RichText/RichTextView';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import useProfilePage from 'shared/hooks/useProfilePage';
import BiographyPlus from 'shared/svg/BiographyPlusIcon';
import { useObjectClicks } from 'shared/hooks/useObjectClicks';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import classes from './Bio.component.module.scss';

const BioSection = (): JSX.Element => {
  const { t } = useTranslation();
  const { data } = useGetAboutSectionsData();
  const isFiled = data?.bio?.length > 0;
  const { isAuthUser, objectDetail: user } = useProfilePage();

  const { handleTagClick, handleHashtagClick, hoveredHashtag, onHashtagHover } =
    useObjectClicks();

  const onEditHandler = isAuthUser
    ? () =>
        openMultiStepForm({
          formName: 'editProfileSections',
          stepKey: profileSectionsStepKeys.BIO,
          data: {
            activeState: 'edit',
            isSingle: true,
          },
        })
    : undefined;

  return (
    <SectionLayout
      classNames={{
        title: classes.title,
        childrenWrap: classes.childrenWrap,
      }}
      title={t('bio')}
      visibleActionButton
      onEditHandler={onEditHandler}
    >
      <Flex>
        {!isFiled ? (
          <BaseButton disabled={!isAuthUser} onClick={onEditHandler}>
            <Card
              title={isAuthUser ? t('write_your_bio') : undefined}
              subTitle={
                isAuthUser
                  ? t('write_your_bio_helper')
                  : `${user?.name} ${t('empty_bio')}`
              }
              image={<BiographyPlus className={classes.cardImage} />}
              direction="row-reverse"
              className={classes.events}
              noHover={!isAuthUser}
              subTitleClassName={classes.subTitleWrap}
            />
          </BaseButton>
        ) : (
          <Paper
            noHover
            className={classes.paperRoot}
            contentClassName={classes.contentClassName}
          >
            <RichTextView
              html={data?.bio as string}
              typographyProps={{
                size: 15,
                color: 'thirdText',
              }}
              showMore
              onMentionClick={handleTagClick}
              onHashtagClick={handleHashtagClick}
              onHashtagHover={onHashtagHover}
              hoveredHashtag={hoveredHashtag}
            />
          </Paper>
        )}
      </Flex>
    </SectionLayout>
  );
};

export default BioSection;
