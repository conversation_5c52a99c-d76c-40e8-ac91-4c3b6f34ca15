import React from 'react';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Card from 'shared/uikit/Card';
import CardSkeleton from 'shared/uikit/Skeleton/CardSkeleton';
import cnj from 'shared/uikit/utils/cnj';
import Paper from 'shared/uikit/Paper';
import useAuthUser from 'shared/utils/hooks/useAuthUser';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import ExperiencePlus from 'shared/svg/ExperiencePlusIcon';
import AccomplishmentPlus from 'shared/svg/AccomplishmentPlusIcon';
import EducationPlus from 'shared/svg/EducationPlusIcon';
import RecommendationAskPlus from 'shared/svg/RecommendationAskPlusIcon';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import classes from './CompleteSection.component.module.scss';

const CompleteSections: React.FC = () => {
  const { t } = useTranslation();
  const { data: user } = useAuthUser();
  const { isLoading } = useGetAboutSectionsData();

  const onClickAskRecommendation = () => {
    openMultiStepForm({
      formName: 'editProfileSections',
      stepKey: profileSectionsStepKeys.RECOMMENDATION,
      data: {
        isSingle: true,
        activeState: 'add',
        initialIsWrite: true,
        initialSecondaryStep: 1,
      },
    });
  };

  const open = (stepKey) => {
    openMultiStepForm({
      formName: 'editProfileSections',
      stepKey,
    });
  };

  return isLoading ? (
    <CardSkeleton />
  ) : (
    <Paper
      title={user?.fullName}
      subTitle={t('to_get_t_m_o_lobox_comp_sections')}
      className={classes.cards}
      contentClassName={classes.cardsContent}
      titleClassName={classes.paperTitleClassName}
      subTitleClassName={classes.paperSubTitleClassName}
      noHover
      hasTitleBorder
    >
      <BaseButton
        className={classes.marginTop}
        onClick={() => open(profileSectionsStepKeys.PRO_EXPERIENCE)}
      >
        <Card
          title={t('experience')}
          image={<ExperiencePlus />}
          subTitle={t('experience_helper')}
          className={cnj(classes.cardItem, classes.marginTopZero)}
          direction="row-reverse"
          subtitleProps={{ isWordWrap: true }}
        />
      </BaseButton>
      <BaseButton onClick={() => open(profileSectionsStepKeys.EDUCATION)}>
        <Card
          title={t('education')}
          image={<EducationPlus />}
          subTitle={t('education_sub_title')}
          direction="row-reverse"
          className={cnj(classes.cardItem)}
          subtitleProps={{ isWordWrap: true }}
        />
      </BaseButton>
      <BaseButton onClick={() => open(profileSectionsStepKeys.ACCOMPLISHMENT)}>
        <Card
          title={t('accomplishment')}
          image={<AccomplishmentPlus />}
          subTitle={t('accomplishment_sub_title')}
          direction="row-reverse"
          className={cnj(classes.cardItem)}
          subtitleProps={{ isWordWrap: true }}
        />
      </BaseButton>
      <BaseButton onClick={onClickAskRecommendation}>
        <Card
          title={t('ask_for_recommendation')}
          image={<RecommendationAskPlus />}
          subTitle={t('ask_for_a_recommendation_sub_title')}
          direction="row-reverse"
          className={cnj(classes.cardItem)}
          subtitleProps={{ isWordWrap: true }}
        />
      </BaseButton>
    </Paper>
  );
};

export default CompleteSections;
