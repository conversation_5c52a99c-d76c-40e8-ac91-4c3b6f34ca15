import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import { COURSE } from 'shared/constants/profileModalsKeys';
import { courseNormalizer } from 'shared/utils/course.utils';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import useProfilePage from '@shared/hooks/useProfilePage';
import AdvancedCardList from 'shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import { useProfileSection } from '../../hooks/useProfileSection';

const Course: React.FC = () => {
  const { t } = useTranslation();
  const { isLoading, data } = useGetAboutSectionsData();
  const { handleBackClick, isEditMode, onClickHandler, onEditHandler } =
    useProfileSection(COURSE, profileSectionsStepKeys.COURSE);
  const courses = data?.courses?.map(courseNormalizer);
  const { isAuthUser } = useProfilePage();

  return (
    <AboutSectionLayout
      data={courses}
      isLoading={isLoading}
      title={t('courses')}
      handleBackClick={isEditMode ? handleBackClick : undefined}
      onEditHandler={isAuthUser ? onEditHandler : undefined}
      onClick={isAuthUser ? onClickHandler : undefined}
    >
      {(props) => (
        <AdvancedCardList
          {...props}
          isEditMode={isEditMode}
          isEditible={isAuthUser}
        />
      )}
    </AboutSectionLayout>
  );
};

export default Course;
