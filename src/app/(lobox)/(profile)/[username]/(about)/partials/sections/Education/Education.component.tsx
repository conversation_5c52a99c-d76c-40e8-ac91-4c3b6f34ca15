import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import { SCHOOL } from 'shared/constants/profileModalsKeys';
import { educationNormalizer } from 'shared/utils/education.utils';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import useProfilePage from '@shared/hooks/useProfilePage';
import type { Education } from '@shared/types/education';
import AdvancedCardList from 'shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import { useProfileSection } from '../../hooks/useProfileSection';

const EducationCmp: React.FC = () => {
  const { t } = useTranslation();
  const { isLoading, data } = useGetAboutSectionsData();
  const educations = data?.educations?.map((item: Education) =>
    educationNormalizer(item, t)
  );
  const { handleBackClick, isEditMode, onClickHandler, onEditHandler } =
    useProfileSection(SCHOOL, profileSectionsStepKeys.SCHOOL);
  const { isAuthUser } = useProfilePage();

  return (
    <AboutSectionLayout
      data={educations}
      isLoading={isLoading}
      title={t('education')}
      handleBackClick={isEditMode ? handleBackClick : undefined}
      onEditHandler={isAuthUser ? onEditHandler : undefined}
      onClick={isAuthUser ? onClickHandler : undefined}
    >
      {(props) => (
        <AdvancedCardList
          {...props}
          isEditMode={isEditMode}
          isEditible={isAuthUser}
        />
      )}
    </AboutSectionLayout>
  );
};

export default EducationCmp;
