import type { JSX } from 'react';
import { useMemo } from 'react';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import { PRO_EXPERIENCE } from 'shared/constants/profileModalsKeys';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import useProfilePage from 'shared/hooks/useProfilePage';
import {
  addDisplayDuration,
  experienceNormalizer,
} from 'shared/utils/experience.utils';
import useTranslation from 'shared/utils/hooks/useTranslation';
import AdvancedCardList from 'shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import { useProfileSection } from '../../hooks/useProfileSection';

const ProfessionalExperience = (): JSX.Element => {
  const { t } = useTranslation();
  const { isAuthUser } = useProfilePage();
  const { isLoading, data } = useGetAboutSectionsData();
  const { handleBackClick, isEditMode, onClickHandler, onEditHandler } =
    useProfileSection(PRO_EXPERIENCE, profileSectionsStepKeys.PRO_EXPERIENCE);
  const { checkSectionVisibility } = useProfilePage();
  const visibleCurrentExperience = checkSectionVisibility(
    'visibleCurrentExperience'
  );
  const visiblePastExperience = checkSectionVisibility('visiblePastExperience');

  const experiences = useMemo(
    () =>
      data?.experiences?.map((item) => ({
        ...item,
        workPlaceTypeLabel: item.workPlaceType
          ? t(item.workPlaceType)
          : undefined,
        companyPageId:
          item?.companyPageId ||
          `${item.companyName?.trim().toLowerCase()}_temp`,
      })),
    [data?.experiences]
  );

  const professionalExperiences = useMemo(
    () =>
      experiences
        ?.filter((i) => {
          if (visiblePastExperience && !i.currentlyWorking) {
            return true;
          }
          return !!(visibleCurrentExperience && i.currentlyWorking);
        })
        .reduce(experienceNormalizer, [])
        .map(addDisplayDuration),
    [experiences, visibleCurrentExperience, visiblePastExperience]
  );

  return (
    <AboutSectionLayout
      handleBackClick={isEditMode ? handleBackClick : undefined}
      data={professionalExperiences}
      isLoading={isLoading}
      title={t('experience')}
      onEditHandler={isAuthUser ? onEditHandler : undefined}
      onClick={isAuthUser ? onClickHandler : undefined}
    >
      {(props) => (
        <AdvancedCardList
          {...props}
          isEditMode={isEditMode}
          isEditible={isAuthUser}
        />
      )}
    </AboutSectionLayout>
  );
};

export default ProfessionalExperience;
