import React, { useCallback } from 'react';
import Flex from 'shared/uikit/Flex';
import TextLink from 'shared/uikit/Link/TextLink';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import { LICENCE } from 'shared/constants/profileModalsKeys';
import type Licence from 'shared/types/licence';
import { licenceNormalizer } from 'shared/utils/licence.utils';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import useProfilePage from '@shared/hooks/useProfilePage';
import AdvancedCardList from 'shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import classes from './Licence.component.module.scss';
import { useProfileSection } from '../../hooks/useProfileSection';

const LicenceComponent: React.FC = () => {
  const { t } = useTranslation();
  const { isLoading, data } = useGetAboutSectionsData();
  const licences = data?.certifications?.map(licenceNormalizer);
  const { handleBackClick, isEditMode, onClickHandler, onEditHandler } =
    useProfileSection(LICENCE, profileSectionsStepKeys.LICENCE);
  const { isAuthUser } = useProfilePage();

  const bottomAction = useCallback(
    (item: Licence) =>
      item?.realData?.certificateLink || item?.realData?.verificationCode ? (
        <Flex className={classes.row}>
          {!!item?.realData?.certificateLink && (
            <TextLink
              href={item?.realData?.certificateLink}
              linkProps={{ target: '_blank' }}
              typographyProps={{
                className: classes.titleText,
                color: 'brand',
              }}
            >
              {t('see_licence_certification')}
            </TextLink>
          )}
          {!!item.realData.verificationCode && (
            <Typography className={classes.breackall} color="colorIconForth2">
              {`${t('verification_code')} - ${item.realData.verificationCode}`}
            </Typography>
          )}
        </Flex>
      ) : undefined,
    [t]
  );

  return (
    <AboutSectionLayout
      data={licences}
      isLoading={isLoading}
      title={t('licences_certification')}
      handleBackClick={isEditMode ? handleBackClick : undefined}
      onEditHandler={isAuthUser ? onEditHandler : undefined}
      onClick={isAuthUser ? onClickHandler : undefined}
    >
      {(props) => (
        <AdvancedCardList
          {...props}
          isEditMode={isEditMode}
          isEditible={isAuthUser}
          bottomAction={bottomAction}
        />
      )}
    </AboutSectionLayout>
  );
};

export default LicenceComponent;
