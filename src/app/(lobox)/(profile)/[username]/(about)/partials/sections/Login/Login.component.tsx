import React, { useState } from 'react';
import Flex from 'shared/uikit/Flex';
import LoginForm from 'shared/uikit/LoginForm';
import TwoFactorAuthGetCodeForm from 'shared/uikit/TwoFactorAuthGetCodeForm';
import TwoFactorAuthBackupCodeForm from 'shared/uikit/TwoFactorAuthBackupCodeForm';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Cookies from 'shared/utils/toolkit/cookies';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import urls from 'shared/constants/urls';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import classes from './Login.component.module.scss';

const forms = {
  getEmailPass: 'getEmailPass',
  getTFACode: 'getTFACode',
  getBackupCode: 'getBackupCode',
};
const AboutRightSideLogin: React.FC = () => {
  const { t } = useTranslation();
  const [activeForm, setActiveForm] = useState(forms.getEmailPass);
  const TWO_FACTOR_LOGIN_DATA = getCookieKey('twoFactorLoginData');

  const title =
    (activeForm === forms.getBackupCode && t('use_bkup_code')) ||
    (activeForm === forms.getTFACode && t('2fa_auth')) ||
    t('login');

  const onSuccessLogin = (data: any) => {
    const signUpCompleted = data?.signUpCompleted;
    const tokenObjKey = getCookieKey('userObjToken');
    Cookies.set(tokenObjKey, data);
    if (!signUpCompleted) {
      window.location.href = urls.getName;
    } else {
      window.location.reload();
    }
  };
  const onsuccess = (data: any) => {
    if (data?.totpToken && !data?.accessToken) {
      Cookies.set(TWO_FACTOR_LOGIN_DATA, data);
      setActiveForm(forms.getTFACode);
    } else {
      onSuccessLogin(data);
    }
  };
  const handleNotVerify = () => {
    window.location.replace(urls.getCode);
  };
  const onUseBackupCodeClick = () => setActiveForm(forms.getBackupCode);
  const onSuccessTFA = (data: any) => {
    Cookies.remove(TWO_FACTOR_LOGIN_DATA);
    onSuccessLogin(data);
  };
  const handleBackClick = () =>
    setActiveForm(
      activeForm === forms.getBackupCode ? forms.getTFACode : forms.getEmailPass
    );

  return (
    <Flex>
      <SectionLayout
        title={title}
        handleBackClick={
          activeForm !== forms.getEmailPass ? handleBackClick : undefined
        }
      >
        {activeForm === forms.getBackupCode && (
          <TwoFactorAuthBackupCodeForm
            variant="thin"
            onSuccess={onSuccessTFA}
            onCancelClick={handleBackClick}
          />
        )}
        {activeForm === forms.getTFACode && (
          <TwoFactorAuthGetCodeForm
            variant="thin"
            onSuccess={onSuccessTFA}
            onUseBackupCodeClick={onUseBackupCodeClick}
            onCancelClick={handleBackClick}
          />
        )}
        {activeForm === forms.getEmailPass && (
          <LoginForm
            onSuccess={onsuccess}
            extraProps={{
              socialIconsInColumn: true,
              forgetPasswordHref: urls.forgetPassword,
              visibleCheckBox: false,
              socialLoginBtnProps: {
                schema: 'ghost',
                className: classes.bordered,
              },
            }}
            onNotVerify={handleNotVerify}
            alertVariant="thin"
            // socialLogin={{
            //   google: (deviceId: string) =>
            //     urls.socialAuth({ deviceId, socialName: 'google' }),
            //   linkedin: (deviceId: string) =>
            //     urls.socialAuth({ deviceId, socialName: 'linkedin' }),
            // }}
          />
        )}
      </SectionLayout>
    </Flex>
  );
};

export default AboutRightSideLogin;
