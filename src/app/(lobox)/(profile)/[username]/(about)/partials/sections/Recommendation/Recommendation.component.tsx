import React, { useEffect, useMemo } from 'react';
import Typography from 'shared/uikit/Typography';
import type RecommendationType from 'shared/types/recommendation';
import type UserType from 'shared/types/user';
import {
  useGlobalState,
  useGlobalDispatch,
} from 'shared/contexts/Global/global.provider';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import useProfilePage from 'shared/hooks/useProfilePage';
import {
  ASK_RECOMMENDATION,
  GIVEN_RECOMMENDATION,
  RECEIVED_RECOMMENDATION,
} from 'shared/constants/profileModalsKeys';
import { PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import orderBy from 'lodash/orderBy';
import { recommendationsNormalizer } from 'shared/utils/recommendation.utils';
import RecommendationList from 'shared/components/Organism/RecommendationList/RecommendationList.component';
import Tabs from 'shared/components/Organism/Tabs';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import classes from './Recommendation.component.module.scss';
import { useProfileSection } from '../../hooks/useProfileSection';

const Recommendation = (): JSX.Element => {
  const globalDispatch = useGlobalDispatch();
  const { t } = useTranslation();
  const { isLoading, data } = useGetAboutSectionsData();

  const { handleBackClick, isEditMode, onEditHandler } = useProfileSection(
    ASK_RECOMMENDATION,
    profileSectionsStepKeys.RECOMMENDATION
  );

  const isRecommendationOpenedFromMessage = useGlobalState(
    'isRecommendationOpenedFromMessage'
  );
  const { authUser } = useGetAppObject();

  const { isAuthUser, objectDetail } = useProfilePage();
  const visitor = objectDetail?.fullName;

  useEffect(() => {
    if (isRecommendationOpenedFromMessage) {
      const containerElement = document.getElementById(PROFILE_SCROLL_WRAPPER);

      // setTimeout(() => {
      //   if (containerElement) {
      //     containerElement.scrollTop = containerElement.scrollHeight;
      //   }
      // }, 250);
    }
    return () => {
      globalDispatch({
        type: 'TOGGLE_IS_OPENED_RECOMMENDATION_FROM_MESSAGE',
        payload: { isOpened: false },
      });
    };
  }, []);

  const receivedRecommendations = data?.receivedRecommendations?.map((item) =>
    recommendationsNormalizer(item, RECEIVED_RECOMMENDATION, authUser?.id, t)
  );
  const givenRecommendations = data?.givenRecommendations?.map((item) =>
    recommendationsNormalizer(item, GIVEN_RECOMMENDATION, authUser?.id, t)
  );
  const sortedReceivedRecommendations = useMemo(
    () => orderBy(receivedRecommendations, ['lastModifiedDate'], ['desc']),
    [receivedRecommendations]
  );

  const sortedGivenRecommendations = useMemo(
    () => orderBy(givenRecommendations, ['lastModifiedDate'], ['desc']),
    [givenRecommendations]
  );

  if (
    givenRecommendations?.length === 0 &&
    receivedRecommendations?.length === 0
  ) {
    return <></>;
  }

  const recommendationListClickHandlers = {
    onAskForRevision: (recom: Partial<{ realData: RecommendationType }>) => {
      openMultiStepForm({
        formName: 'editProfileSections',
        stepKey: profileSectionsStepKeys.RECOMMENDATION,
        data: {
          isSingle: true,
          activeState: 'edit',
          initialActiveItem: recom,
          initialIsWrite: false,
          initialSelectedUser: recom?.realData?.sender as UserType,
          initialSecondaryStep: 2,
        },
      });
    },
    onWriteRecommendation: (
      recom: Partial<{ realData: RecommendationType }>
    ) => {
      openMultiStepForm({
        formName: 'editProfileSections',
        stepKey: profileSectionsStepKeys.RECOMMENDATION,
        data: {
          isSingle: true,
          activeState: 'add',
          initialActiveItem: recom,
          initialIsWrite: true,
          initialSelectedUser: recom?.realData?.receiver as UserType,
          initialSecondaryStep: 2,
        },
      });
    },
    onWriteRevision: (recom: Partial<{ realData: RecommendationType }>) => {
      openMultiStepForm({
        formName: 'editProfileSections',
        stepKey: profileSectionsStepKeys.RECOMMENDATION,
        data: {
          isSingle: true,
          activeState: 'edit',
          initialActiveItem: recom,
          initialIsWrite: true,
          initialSelectedUser: recom?.realData?.receiver as UserType,
          initialSecondaryStep: 2,
        },
      });
    },
  };

  const addRecommendation = () => {
    openMultiStepForm({
      formName: 'editProfileSections',
      stepKey: profileSectionsStepKeys.RECOMMENDATION,
      data: {
        isSingle: true,
        activeState: 'add',
        initialIsWrite: true,
        initialSecondaryStep: 1,
      },
    });
  };

  return (
    <AboutSectionLayout
      isLoading={isLoading}
      data={[0]}
      title={t('recommendations')}
      classNames={{ childrenWrap: classes.childrenWrap }}
      handleBackClick={isEditMode ? handleBackClick : undefined}
      onEditHandler={isAuthUser ? onEditHandler : undefined}
      onClick={isAuthUser ? addRecommendation : undefined}
    >
      {() => (
        <Tabs
          tabs={[
            {
              path: 'received',
              title: `${t(
                'received'
              )} (${sortedReceivedRecommendations?.length})`,
              content: () =>
                sortedReceivedRecommendations?.length === 0 ? (
                  <Typography size={14} height={18} isWordWrap>
                    {isAuthUser
                      ? t('you_empty_received_recom')
                      : `${visitor} ${t('empty_received_recommendation')}`}
                  </Typography>
                ) : (
                  <RecommendationList
                    tab="received"
                    data={sortedReceivedRecommendations}
                    {...recommendationListClickHandlers}
                  />
                ),
            },
            {
              path: 'given',
              title: `${t('given')} (${sortedGivenRecommendations?.length})`,
              content: () =>
                sortedGivenRecommendations?.length === 0 ? (
                  <Typography size={14} height={18}>
                    {isAuthUser
                      ? t('you_empty_given_recom')
                      : `${visitor} ${t('empty_given_recommendation')}`}
                  </Typography>
                ) : (
                  <RecommendationList
                    tab="given"
                    data={sortedGivenRecommendations}
                    {...recommendationListClickHandlers}
                  />
                ),
            },
          ]}
          styles={{
            linksRoot: classes.linksRoot,
            content: classes.contentWrapper,
          }}
        />
      )}
    </AboutSectionLayout>
  );
};

export default Recommendation;
