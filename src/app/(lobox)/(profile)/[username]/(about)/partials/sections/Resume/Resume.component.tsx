import React from 'react';
import Flex from 'shared/uikit/Flex';
import useProfilePage from 'shared/hooks/useProfilePage';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import {
  closeMultiStepForm,
  openMultiStepForm,
} from 'shared/hooks/useMultiStepForm';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import useToast from 'shared/uikit/Toast/useToast';
import useGetResume from '@shared/hooks/useGetResume';
import useUploadResumeAndAnalyze from '@shared/hooks/useUploadResumeAndAnalyze';
import ResumePlusIcon from 'shared/svg/ResumePlusIcon';
import ResumeUploadBox from '@shared/uikit/ResumeUploadBox';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { AxiosError } from 'axios';
import AboutSectionSkeleton from '../../components/AboutSectionSkeleton.component';
import classes from './Resume.component.module.scss';

interface Props {
  className: string;
}

const ResumeSection: React.FC<Props> = ({ className }) => {
  const { t } = useTranslation();

  const { isAuthUser, isLoading, objectDetail: user } = useProfilePage();
  const toast = useToast();

  const { uploadResumeAndAnalyze, isUploading } = useUploadResumeAndAnalyze();
  const { data, refetch } = useGetResume({ userId: user?.id });
  const resumeLink = data?.url;
  const isFiled = !!resumeLink;

  const onError = ({ response }: AxiosError) => {
    const msg = response?.data?.error;

    toast({
      type: 'error',
      icon: 'times-circle',
      title: t(msg ?? 'something_went_wrong'),
      message: t('file_types_accept'),
    });
  };

  const onFilePickedHandler = (originalFile: Blob) => {
    uploadResumeAndAnalyze(originalFile, {
      onSuccess: (res) => {
        closeMultiStepForm('resumeUpload', res?.data);
        openMultiStepForm({
          formName: 'editProfileSections',
          stepKey: profileSectionsStepKeys.RESUME_UPLOADED,
        });
      },
      onError,
    });
  };

  if (isLoading) {
    return <AboutSectionSkeleton />;
  }
  if (!isAuthUser && !isFiled) {
    return null;
  }

  return (
    <Flex className={className}>
      <SectionLayout
        title={t('resume')}
        classNames={{
          childrenWrap: classes.childrenWrap,
        }}
      >
        <ResumeUploadBox
          value={data}
          onFilePickedHandler={onFilePickedHandler}
          labels={{
            uploadDate: t('upload_date'),
            uploading: t('file_uploading_3dot'),
            uploadNew: t('upload_new_resume'),
          }}
          icons={{
            uploadNew: <ResumePlusIcon />,
          }}
          isAjaxCall
          onChange={() => {}}
          isUploading={isUploading}
          onSuccessDelete={refetch}
          className="p-16_20 rounded-[12px]"
          ownerName={user?.fullName}
        />
      </SectionLayout>
    </Flex>
  );
};

export default ResumeSection;
