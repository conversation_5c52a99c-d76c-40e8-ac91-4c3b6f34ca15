import React from 'react';
import ProgressItem from 'shared/uikit/ProgressItem';
import skillNormalizer from 'shared/utils/normalizers/skillNormalizer';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import { SKILL_UPSERT } from 'shared/constants/profileModalsKeys';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import Flex from '@shared/uikit/Flex';
import useProfilePage from '@shared/hooks/useProfilePage';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import { useProfileSection } from '../../hooks/useProfileSection';

interface Props {
  className: string;
}

const SkillSection: React.FC<Props> = ({ className }) => {
  const { t } = useTranslation();
  const { data, isLoading } = useGetAboutSectionsData();
  const { handleBack<PERSON>lick, onClickHandler, onEditHandler, isEditMode } =
    useProfileSection(SKILL_UPSERT, profileSectionsStepKeys.SKILL);
  const { isAuthUser } = useProfilePage();

  const skills = data?.skills
    ?.map(skillNormalizer)
    ?.sort((a, b) => (a.progress < b.progress ? 1 : -1));

  if (!skills?.length) {
    return null;
  }
  return (
    <Flex className={className}>
      <AboutSectionLayout
        data={skills}
        title={t('skills')}
        isLoading={isLoading}
        handleBackClick={isEditMode ? handleBackClick : undefined}
        onEditHandler={isAuthUser ? onEditHandler : undefined}
        onClick={isAuthUser ? onClickHandler : undefined}
      >
        {({ data }) =>
          data.map((item, index) => (
            <ProgressItem
              key={item?.id}
              title={item?.name}
              progressValue={item?.progress}
              tooltipText={t(item?.level)}
              progressSteps={4}
              styles={
                index !== 0 ? { root: 'responsive-margin-top' } : undefined
              }
            />
          ))
        }
      </AboutSectionLayout>
    </Flex>
  );
};

export default SkillSection;
