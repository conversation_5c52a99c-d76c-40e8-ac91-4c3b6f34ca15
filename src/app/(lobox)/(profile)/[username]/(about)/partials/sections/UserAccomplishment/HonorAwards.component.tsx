import React, { useCallback } from 'react';
import Flex from 'shared/uikit/Flex';
import TextLink from 'shared/uikit/Link/TextLink';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import { HONOR } from 'shared/constants/profileModalsKeys';
import type Publication from 'shared/types/publication';
import { awardNormalizer } from 'shared/utils/userAccomplishment.utils';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import useProfilePage from '@shared/hooks/useProfilePage';
import AdvancedCardList from 'shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import classes from './HonorAwards.component.module.scss';
import { useProfileSection } from '../../hooks/useProfileSection';

const HonorAwards: React.FC = () => {
  const { t } = useTranslation();
  const { isLoading, data } = useGetAboutSectionsData();
  const publications = data?.awards?.map(awardNormalizer);
  const { handleBackClick, isEditMode, onClickHandler, onEditHandler } =
    useProfileSection(HONOR, profileSectionsStepKeys.HONOR);
  const { isAuthUser } = useProfilePage();

  const bottomAction = useCallback(
    (item: Publication) =>
      item?.realData?.link ? (
        <Flex className={classes.row}>
          <TextLink
            href={item?.realData?.link}
            linkProps={{ target: '_blank' }}
            typographyProps={{ color: 'brand', mt: 4 }}
          >
            {t('see_honor_awards')}
          </TextLink>
        </Flex>
      ) : undefined,
    [t]
  );

  return (
    <AboutSectionLayout
      data={publications}
      isLoading={isLoading}
      title={t('honrs_a_awrs')}
      handleBackClick={isEditMode ? handleBackClick : undefined}
      onEditHandler={isAuthUser ? onEditHandler : undefined}
      onClick={isAuthUser ? onClickHandler : undefined}
    >
      {(props) => (
        <AdvancedCardList
          {...props}
          isEditMode={isEditMode}
          isEditible={isAuthUser}
          bottomAction={bottomAction}
        />
      )}
    </AboutSectionLayout>
  );
};

export default HonorAwards;
