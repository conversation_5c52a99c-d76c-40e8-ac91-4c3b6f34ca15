import React, { useCallback } from 'react';
import Flex from 'shared/uikit/Flex';
import TextLink from 'shared/uikit/Link/TextLink';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import { PATENT } from 'shared/constants/profileModalsKeys';
import type { Patent } from '@shared/types/patent';
import { patentNormalizer } from 'shared/utils/userAccomplishment.utils';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import useProfilePage from '@shared/hooks/useProfilePage';
import AdvancedCardList from 'shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import classes from './Patent.component.module.scss';
import { useProfileSection } from '../../hooks/useProfileSection';

const PatentSection: React.FC = () => {
  const { t } = useTranslation();
  const { isLoading, data } = useGetAboutSectionsData();
  const patents = data?.patents?.map(patentNormalizer);
  const { handleBackClick, isEditMode, onClickHandler, onEditHandler } =
    useProfileSection(PATENT, profileSectionsStepKeys.PATENT);
  const { isAuthUser } = useProfilePage();

  const bottomAction = useCallback(
    (item: Patent) =>
      item?.realData?.link || item?.realData?.patentId ? (
        <Flex className={classes.row}>
          <TextLink
            href={item?.realData?.link}
            linkProps={{ target: '_blank' }}
            typographyProps={{
              className: classes.titleText,
              color: 'brand',
            }}
          >
            {t('see_patent')}
          </TextLink>
          {!!item.realData.patentId && (
            <Typography className={classes.breackall} color="colorIconForth2">
              {`${t('patent_number')} - ${item.realData.patentId}`}
            </Typography>
          )}
        </Flex>
      ) : undefined,
    [t]
  );

  return (
    <AboutSectionLayout
      data={patents}
      isLoading={isLoading}
      title={t('patents')}
      handleBackClick={isEditMode ? handleBackClick : undefined}
      onEditHandler={isAuthUser ? onEditHandler : undefined}
      onClick={isAuthUser ? onClickHandler : undefined}
    >
      {(props) => (
        <AdvancedCardList
          {...props}
          isEditMode={isEditMode}
          isEditible={isAuthUser}
          bottomAction={bottomAction}
        />
      )}
    </AboutSectionLayout>
  );
};

export default PatentSection;
