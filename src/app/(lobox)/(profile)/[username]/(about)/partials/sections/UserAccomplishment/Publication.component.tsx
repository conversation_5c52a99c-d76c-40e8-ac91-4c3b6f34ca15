import React, { useCallback } from 'react';
import Flex from 'shared/uikit/Flex';
import TextLink from 'shared/uikit/Link/TextLink';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import { PUBLICATION } from 'shared/constants/profileModalsKeys';
import { publicationNormalizer } from 'shared/utils/userAccomplishment.utils';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import useProfilePage from '@shared/hooks/useProfilePage';
import type { Publication } from '@shared/types/publication';
import AdvancedCardList from 'shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import classes from './Publication.component.module.scss';
import { useProfileSection } from '../../hooks/useProfileSection';

const PublicationSection: React.FC = () => {
  const { t } = useTranslation();
  const { isLoading, data } = useGetAboutSectionsData();
  const publications = data?.publications?.map(publicationNormalizer);
  const { handleBackClick, isEditMode, onClickHandler, onEditHandler } =
    useProfileSection(PUBLICATION, profileSectionsStepKeys.PUBLICATION);
  const { isAuthUser } = useProfilePage();

  const bottomAction = useCallback(
    (item: Publication) =>
      item?.realData?.link ? (
        <Flex className={classes.row}>
          <TextLink
            href={item?.realData?.link}
            linkProps={{ target: '_blank' }}
            typographyProps={{ color: 'brand', mt: 4 }}
          >
            {t('see_publication')}
          </TextLink>
        </Flex>
      ) : undefined,
    [t]
  );

  return (
    <AboutSectionLayout
      data={publications}
      isLoading={isLoading}
      title={t('publications')}
      handleBackClick={isEditMode ? handleBackClick : undefined}
      onEditHandler={isAuthUser ? onEditHandler : undefined}
      onClick={isAuthUser ? onClickHandler : undefined}
    >
      {(props) => (
        <AdvancedCardList
          {...props}
          isEditMode={isEditMode}
          isEditible={isAuthUser}
          bottomAction={bottomAction}
        />
      )}
    </AboutSectionLayout>
  );
};

export default PublicationSection;
