import React from 'react';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import { CollectionsSection } from './partials/sections';

const CollectionsSkeleton = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeCollectionsTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <ProfileTabLayout>
        <CollectionsSection isLoading title={t('collections')} />
      </ProfileTabLayout>
    </PermissionsGate>
  );
};

export default CollectionsSkeleton;
