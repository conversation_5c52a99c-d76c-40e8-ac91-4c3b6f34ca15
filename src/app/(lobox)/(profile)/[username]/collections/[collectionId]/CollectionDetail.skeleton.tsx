import React from 'react';
import ProfileTabBiColumn from 'shared/uikit/Layout/Profile/ProfileTabBiColumn';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import Section from 'shared/components/molecules/Section';
import { routeNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useHistory from 'shared/utils/hooks/useHistory';
import useParams from 'shared/utils/hooks/useParams';
import CollectionPosts from './partials/sections/CollectionPosts/CollectionPosts';
import { CollectionsSide } from './partials/sections';

const CollectionDetailSkeleton = (): JSX.Element => {
  const { username } = useParams<{ username: string }>();
  const history = useHistory();
  const { t } = useTranslation();

  const backToCollections = () => {
    history.push(`/${username}${routeNames.collections}`);
  };

  return (
    <ProfileTabLayout>
      <Section
        hasSearchInput
        title={t('collection_detail')}
        backAction={backToCollections}
      >
        <ProfileTabBiColumn
          showSideOnDesktop
          mainComponent={<CollectionPosts isLoading />}
          sideComponent={<CollectionsSide isSkeleton />}
        />
      </Section>
    </ProfileTabLayout>
  );
};

export default CollectionDetailSkeleton;
