'use client';

import React, { useEffect, useState } from 'react';
import useHistory from 'shared/utils/hooks/useHistory';
import useParams from 'shared/utils/hooks/useParams';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { routeNames } from 'shared/utils/constants/routeNames';
import useDisclosure from 'shared/utils/hooks/useDisclosure';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import { getCollectionItem } from 'shared/utils/api/collection';
import { getFeedOfCollection } from 'shared/utils/api/postSearch';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import { deleteCollection as deleteCollectionApi } from 'shared/utils/api/post';
import useScrollReachEnd from 'shared/utils/hooks/useScrollReachEnd';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import ProfileTabBiColumn from 'shared/uikit/Layout/Profile/ProfileTabBiColumn';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import CollectionModalForm from 'shared/components/Organism/CollectionModalForm';
import Section from 'shared/components/molecules/Section';
import useProfilePage from 'shared/hooks/useProfilePage';
import { PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import type { IFeedElement } from 'shared/types/feedElement';
import IconButton from 'shared/uikit/Button/IconButton';
import useMedia from 'shared/uikit/utils/useMedia';
import { flushSync } from 'react-dom';
import CollectionPosts from './partials/sections/CollectionPosts/CollectionPosts';
import { CollectionsSide } from './partials/sections';

export type FeedElementType = IFeedElement & { postId: string };

const Collection = (): JSX.Element => {
  const { isAuthUser, isAuthBusinessPage } = useProfilePage();
  const { isMoreThanTablet } = useMedia();
  const buttonsVisibility =
    !isMoreThanTablet && (isAuthUser || isAuthBusinessPage);
  const { t } = useTranslation();
  const { collectionId, username } = useParams<{
    collectionId: string;
    username: string;
  }>();
  const history = useHistory();
  const { openConfirmDialog } = useOpenConfirm();
  const [editItem, setEditItem] = useState(collectionId);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const collectionItemKey = [QueryKeys.collection, collectionId, username];

  const { mutate: deleteCollection } = useReactMutation({
    apiFunc: deleteCollectionApi,
  });
  const {
    data: { name: title, access: privacy } = {},
    isError,
    error,
  } = useReactQuery<any, any>({
    action: {
      apiFunc: () => getCollectionItem(collectionId),
      key: collectionItemKey,
    },
  });
  const { data, isLoading, fetchNextPage } = useInfiniteQuery<FeedElementType>(
    [QueryKeys.collectionFeed, collectionId, username],
    {
      func: getFeedOfCollection,
      extraProps: {
        collectionId,
      },
    }
  );
  useScrollReachEnd({
    scrollEl: PROFILE_SCROLL_WRAPPER,
    callback: fetchNextPage,
  });
  const backToCollections = () => {
    history.push(`/${username}${routeNames.collections}`);
  };

  const openConfirm = () => {
    openConfirmDialog({
      title: t('remove_collection'),
      message: t('remove_collection_m'),
      confirmButtonText: t('remove'),
      cancelButtonText: t('cancel'),
      confirmCallback: () => {
        deleteCollection(
          { collectionId },
          {
            onSuccess: backToCollections,
          }
        );
      },
    });
  };

  const handleEdit = (data: any) => {
    flushSync(() => setEditItem(data));
    onOpen();
  };

  useEffect(() => {
    if (
      isError &&
      error?.response?.data?.error === 'CollectionNotFoundException'
    ) {
      backToCollections();
    }
  }, [isError, error]); // eslint-disable-line

  return (
    <>
      <ProfileTabLayout>
        <Section
          title={title}
          backAction={backToCollections}
          customAction={
            buttonsVisibility
              ? () => (
                  <>
                    <IconButton
                      name="trash"
                      colorSchema="transparent1"
                      icon="trash"
                      type="far"
                      onClick={openConfirm}
                    />
                    <IconButton
                      name="pen-light"
                      colorSchema="transparent1"
                      icon="pen-light"
                      type="far"
                      onClick={onOpen}
                      className="mr-l-8"
                    />
                  </>
                )
              : undefined
          }
        >
          <ProfileTabBiColumn
            showSideOnDesktop
            mainComponent={
              <CollectionPosts isLoading={isLoading} list={data} />
            }
            sideComponent={
              <CollectionsSide feedList={data} handleEdit={handleEdit} />
            }
          />
        </Section>
      </ProfileTabLayout>
      {isOpen && (
        <CollectionModalForm
          collectionItemKey={collectionItemKey}
          modalTitle={t('edit_collection')}
          onClose={onClose}
          isOpen={isOpen}
          currentId={editItem?.id}
          initialValue={{
            value: editItem?.name,
            label: editItem?.name,
            privacy: editItem?.access,
          }}
        />
      )}
    </>
  );
};

export default Collection;
