import React, { useEffect } from 'react';
import useParams from 'shared/utils/hooks/useParams';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import event from 'shared/utils/toolkit/event';
import FeedCard, { FeedSkeleton } from 'shared/components/Organism/FeedCard';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import useProfilePage from 'shared/hooks/useProfilePage';
import type CollectionType from 'shared/utils/CollectionType';
import type { IFeedElement } from 'shared/types/feedElement';
import eventKeys from 'shared/constants/event-keys';
import classes from './CollectionPosts.module.scss';
import CollectionsEmptyState from '../../../../partials/sections/CollectionsSection/CollectionsEmptyState';

interface CollectionPostsProps {
  isLoading?: boolean;
  list?: Array<any>;
  isSearchEmpty?: boolean;
}

const CollectionPosts: React.FC<CollectionPostsProps> = ({
  isLoading,
  isSearchEmpty,
  list,
}) => {
  const { objectDetail, isAuthUser } = useProfilePage();
  const { t } = useTranslation();
  const { isBusinessApp } = useGetAppObject();
  const { collectionId, username } = useParams<{
    collectionId: string;
    username: string;
  }>();

  const {
    remove: removeFeed,
    replace: replaceFeed,
    get: getFeed,
  } = useUpdateInfinityData([QueryKeys.collectionFeed, collectionId, username]);
  const { get, replace: replaceCollection } =
    useUpdateInfinityData<CollectionType>([
      QueryKeys.collections,
      '',
      objectDetail?.id,
    ]);

  const handleRemovePostFromCollection = (postId: string) => () => {
    const item = (get(collectionId) || {}) as CollectionType;

    replaceCollection({
      ...item,
      itemsCounter: Number(item.itemsCounter) - 1,
    });
    removeFeed(postId);
  };
  const handleUpdatePost = (feed: IFeedElement) => {
    replaceFeed(feed);
  };

  useEffect(() => {
    event.on(eventKeys.updatePost, (props: any) => {
      const { data: updatedPost } = props.detail;
      const feed = getFeed(updatedPost.id);
      // @ts-ignore
      replaceFeed({ ...feed, post: updatedPost });
    });
    return () => {
      event.off(eventKeys.updatePost);
    };
  }, []);

  return (
    <Flex className={classes.collectionPostsRoot}>
      {isSearchEmpty ? (
        <Typography size={16} height={20}>
          {t('empty_msg')}
        </Typography>
      ) : isLoading ? (
        <FeedSkeleton />
      ) : list?.length ? (
        list.map((feed: any) => (
          <FeedCard
            key={feed.post.id}
            feedElement={feed}
            onReplacePost={handleUpdatePost}
            onDeletePost={handleRemovePostFromCollection(feed.post.id)}
            onUnsavePost={handleRemovePostFromCollection(feed.post.id)}
          />
        ))
      ) : (
        <CollectionsEmptyState
          message={
            isBusinessApp
              ? t('collection_es_msg_b')
              : isAuthUser
                ? t('collection_es_msg')
                : `${objectDetail?.fullName} ${t('collection_es_msg_else')}`
          }
        />
      )}
    </Flex>
  );
};

export default CollectionPosts;
