import React, { useState, useRef, useCallback, useEffect } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useMakeCollectionRoute from 'shared/utils/hooks/useMakeCollectionRoute';
import useComponentDidUpdate from 'shared/utils/hooks/useComponentDidUpdate';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import { deleteCollection as deleteCollectionApi } from 'shared/utils/api/post';
import { routeNames } from 'shared/utils/constants/routeNames';
import useHistory from 'shared/utils/hooks/useHistory';
import useParams from 'shared/utils/hooks/useParams';
import NoticeBoxWithoutAction from 'shared/components/molecules/NoticeBox/NoticeBoxWithoutAction';
import useGetCollections from 'shared/hooks/api-hook/useGetCollections';
import useProfilePage from 'shared/hooks/useProfilePage';
import PopperMenu from 'shared/uikit/PopperMenu';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import debounce from 'lodash/debounce';
import { MAIN_CENTER_WRAPPER_ID } from '@shared/utils/constants/enums';
import classes from './CollectionsSide.module.scss';

const LIST_THRESHOLD = 3;

interface CollectionsSideProps {
  isSkeleton?: boolean;
  feedList?: Array<any>;
  handleEdit?: (data: any) => any;
}

const CollectionsSide = ({
  isSkeleton,
  feedList,
  handleEdit,
}: CollectionsSideProps): JSX.Element => {
  const { collectionId: currentId, username } = useParams<{
    collectionId: string;
    username: string;
  }>();
  const { t } = useTranslation();
  const { isAuthUser, isAuthBusinessPage } = useProfilePage();
  const history = useHistory();

  const [showAll, setShowAll] = useState(false);

  const makeCollectionRoute = useMakeCollectionRoute();
  const { objectDetail } = useProfilePage();
  const { openConfirmDialog } = useOpenConfirm();
  const { mutate: deleteCollection } = useReactMutation({
    apiFunc: deleteCollectionApi,
  });
  const backToCollections = () => {
    history.push(`/${username}${routeNames.collections}`);
  };
  const isVisibleAction = isAuthUser || isAuthBusinessPage;

  const openConfirm = ({ id }: any) => {
    openConfirmDialog({
      title: t('remove_collection'),
      message: t('remove_collection_m'),
      confirmButtonText: t('remove'),
      cancelButtonText: t('cancel'),
      confirmCallback: () => {
        deleteCollection(
          { collectionId: id },
          {
            onSuccess: backToCollections,
          }
        );
      },
    });
  };

  const {
    data: collections = [],
    isLoading,
    refetch,
  } = useGetCollections({
    objectId: isSkeleton ? '' : objectDetail?.id,
    size: 100,
  });
  const list = collections.map((item) => ({
    ...item,
    count: item.itemsCounter
      ? `${item.itemsCounter} ${t(+item.itemsCounter > 1 ? 'posts' : 'post')}`
      : t('no_post_saved'),
    isActive: item.id === currentId,
  }));
  const inactiveList = list.filter(({ isActive }) => !isActive);
  const activeItem = list.find(({ isActive }) => isActive);
  const iterableList = [activeItem, ...inactiveList].filter(Boolean);
  const isVisibleShowMore = list?.length > LIST_THRESHOLD;

  useComponentDidUpdate(() => {
    refetch();
  }, feedList);

  const ref = useRef<HTMLDivElement | null>(null);
  const intersectee = useRef<HTMLDivElement | null>(null);

  const onWindowScroll = useCallback(
    debounce(() => {
      if (!showAll || !ref.current || !intersectee.current) return;
      const r1 = ref.current.getBoundingClientRect();
      const r2 = intersectee.current.getBoundingClientRect();
      const rp = intersectee.current.parentElement?.getBoundingClientRect();

      const hasScrollSpace = (rp?.height ?? 0) > r1.height + 50;

      if (hasScrollSpace && r1.bottom === r2.bottom) {
        setShowAll(false);
      }
    }, 100),
    [showAll, setShowAll]
  );

  useEffect(() => {
    const scrollArea = document.getElementById(MAIN_CENTER_WRAPPER_ID);
    scrollArea?.addEventListener('scroll', onWindowScroll);
    return () => scrollArea?.removeEventListener('scroll', onWindowScroll);
  }, [onWindowScroll]);

  return (
    <>
      <NoticeBoxWithoutAction
        ref={ref}
        isLoading={isSkeleton || isLoading}
        layoutProps={{
          className: classes.wrapper,
          title: t('collections'),
          onMoreClicked: () => setShowAll(true),
          isVisibleShowMore: isVisibleShowMore && !showAll,
        }}
        data={
          !isVisibleShowMore || showAll
            ? iterableList
            : iterableList.slice(0, LIST_THRESHOLD)
        }
        dataKeys={{
          imageKeyName: 'imageUrl',
          titleKeyName: 'name',
          subTitleKeyName: 'count',
        }}
        listItemProps={{
          hoverBgColor: 'hoverPrimary',
          hoverColor: 'thirdText',
          avatarProps: {
            isCompany: true,
            resolution: 'original',
          },
          onItemClicked: ({ id }: any) => {
            history.push(makeCollectionRoute(id));
          },
        }}
        rightActions={
          isVisibleAction
            ? (data: any) => (
                <PopperMenu
                  placement="bottom-end"
                  buttonComponent={
                    <IconButton
                      type="far"
                      name="ellipsis-h"
                      size="md20"
                      colorSchema="transparentMoreHover"
                    />
                  }
                >
                  <PopperItem
                    onClick={() => handleEdit?.(data)}
                    iconName="pen"
                    label={t('edit')}
                  />
                  <PopperItem
                    onClick={() => openConfirm(data)}
                    iconName="trash"
                    label={t('delete')}
                  />
                </PopperMenu>
              )
            : undefined
        }
      />
      <div ref={intersectee} className={classes.intersectDetector} />
    </>
  );
};

export default CollectionsSide;
