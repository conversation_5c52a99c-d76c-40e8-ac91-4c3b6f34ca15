'use client';

import React from 'react';
import Section from 'shared/components/molecules/Section';
import Flex from 'shared/uikit/Flex/Flex.component';
import { placeholderData } from 'shared/constants/enums';
import CollectionItemSkeleton from '@app/(profile)/[username]/collections/partials/components/CollectionItem.skeleton';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import classes from './loading.module.scss';

export default function loading() {
  return (
    <Section title={(<Skeleton style={{ height: 24, width: 170 }} />) as any}>
      <Flex className={classes.wrapper}>
        {placeholderData('collections').map(({ id }) => (
          <CollectionItemSkeleton key={id} className={classes.collectionItem} />
        ))}
      </Flex>
    </Section>
  );
}
