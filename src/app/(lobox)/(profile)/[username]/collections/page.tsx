'use client';

import React, { useEffect } from 'react';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useScrollReachEnd from 'shared/utils/hooks/useScrollReachEnd';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import useGetCollections from 'shared/hooks/api-hook/useGetCollections';
import useProfilePage from 'shared/hooks/useProfilePage';
import { PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import { CollectionsSection } from './partials/sections';

const Collections = (): JSX.Element => {
  const { t } = useTranslation();
  const { setValue, debounceValue } = useDebounceState('', 500);
  const { objectDetail, isAuthUser, getObjectProp } = useProfilePage();
  const name = getObjectProp({
    pageKey: 'title',
    userKey: 'name',
  });

  const {
    data = [],
    isLoading,
    fetchNextPage,
    refetch,
    getCollectionsKey,
  } = useGetCollections({
    objectId: objectDetail?.id,
    nameValue: debounceValue,
    size: 10,
  });

  useEffect(() => {
    refetch();
  }, [debounceValue, refetch]);

  useScrollReachEnd({
    scrollEl: PROFILE_SCROLL_WRAPPER,
    callback: fetchNextPage,
  });

  return (
    <ProfileTabLayout>
      <CollectionsSection
        isLoading={isLoading}
        title={isAuthUser ? t('svd_b_y') : `${t('svd_b')} ${name} `}
        list={data}
        dataKey={getCollectionsKey}
        onSearchChanged={setValue}
        isSearchEmpty={Boolean(!data?.length && debounceValue && !isLoading)}
      />
    </ProfileTabLayout>
  );
};

export default Collections;
