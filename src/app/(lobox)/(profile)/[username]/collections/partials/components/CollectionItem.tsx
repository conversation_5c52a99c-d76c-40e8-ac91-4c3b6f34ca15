import React, { useRef } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Image from 'shared/uikit/Image';
import Link from 'shared/uikit/Link';
import PrivacyVisibility from 'shared/uikit/PrivacyVisibility';
import Typography from 'shared/uikit/Typography';
import { updateCollection as updateCollectionApi } from 'shared/utils/api/post';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import usePlaceholder from 'shared/hooks/usePlaceholder';
import useProfilePage from 'shared/hooks/useProfilePage';
import useHasPermission from 'shared/hooks/useHasPermission';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useResponseToast from 'shared/hooks/useResponseToast';
import type CollectionType from 'shared/utils/CollectionType';
import type PrivacyValueType from 'shared/utils/PrivacyValueType';
import type { ItemType } from 'shared/types/collection';
import PostThumbnailRowMedia from 'shared/components/Organism/FeedCard/modals/SavePost/PostThumbnailRowMedia.gallery';
import classes from './CollectionItem.module.scss';

export interface CollectionItemProps {
  dataKey: any;
  className?: string;
  collectionId: string;
  thumbnailItems?: ItemType[];
  cover?: any;
  title: string;
  count: string | number;
  to: string;
  access: PrivacyValueType;
}

const CollectionItem = ({
  dataKey,
  className,
  collectionId,
  thumbnailItems,
  cover,
  title,
  count,
  to,
  access: privacy,
}: CollectionItemProps): JSX.Element => {
  const privacyRef = useRef<{ close: Function }>();
  const { t } = useTranslation();
  const { isAuthUser, isAuthBusinessPage } = useProfilePage();
  const hasAccess = useHasPermission([SCOPES.canEditProfile]);
  const privacyButtonVisibility =
    hasAccess && (isAuthUser || isAuthBusinessPage);
  const placeholder = usePlaceholder();
  const { handleError } = useResponseToast();

  const { mutate: updateCollection } = useReactMutation({
    apiFunc: updateCollectionApi,
  });
  const { replace, get } = useUpdateInfinityData<CollectionType>(dataKey);

  const handleUpdatePrivacy = (access: PrivacyValueType) => {
    const collectionItemData = get(collectionId);
    updateCollection(
      {
        collectionId,
        name: title,
        access,
      },
      {
        onSuccess: () => {
          privacyRef?.current?.close();

          replace({
            ...collectionItemData,
            access,
          });
        },
        onError: handleError,
      }
    );
  };

  return (
    <Flex className={cnj(classes.collectionItemRoot, className)}>
      <Link to={to}>
        <Flex className={classes.imageWrapper}>
          {thumbnailItems ? (
            <PostThumbnailRowMedia
              medias={thumbnailItems}
              innerWrapperClassName={classes.thumbnailWrapper}
            />
          ) : (
            <Image
              resolution="original"
              src={cover || placeholder}
              alt="."
              className={cnj(classes.coverImage, classes.imageWrapper)}
            />
          )}
          <Flex className={classes.overlay} />
        </Flex>
        <Flex
          className={cnj(
            classes.detailWrapper,
            !thumbnailItems?.length && classes.border
          )}
        >
          <Typography
            font="700"
            size={20}
            height={23.44}
            color="thirdText"
            mb={12}
            isTruncated
          >
            {title}
          </Typography>
          <Flex flexDir="row" className={classes.infoWrapper}>
            <Typography
              font="400"
              size={14}
              height={18.2}
              color="fifthText"
              isTruncated
              mr="auto"
            >
              {`${count} ${t(Number(count) > 1 ? 'items' : 'item')}`}
            </Typography>
          </Flex>
        </Flex>
      </Link>
      <Flex className={classes.privacyWrapper}>
        {privacyButtonVisibility && (
          <PrivacyVisibility
            onClick={(val: PrivacyValueType) => {
              handleUpdatePrivacy(val);
            }}
            privacy={privacy}
          />
        )}
      </Flex>
    </Flex>
  );
};

export default CollectionItem;
