import React, { type FC } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import EmptySectionInModules, {
  type EmptyProps,
} from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import { routeNames } from '@shared/utils/constants';
import useHistory from 'shared/utils/hooks/useHistory';
import { usePrefetchRoutes } from '@shared/hooks/usePrefetchRoutes';
import classes from './CollectionsSection.module.scss';

const CollectionsEmptyState: FC<Partial<EmptyProps>> = (
  props = { isFullParent: true }
) => {
  const { t } = useTranslation();
  const history = useHistory();

  usePrefetchRoutes([routeNames.home]);

  return (
    <EmptySectionInModules
      classNames={{ container: classes.blockedAccountRoot }}
      title={t('no_post_saved')}
      text={t('y_d_h_a_s_p_t_sh')}
      buttonProps={{
        title: t('see_posts'),
        onClick: () => history.push(routeNames.home),
      }}
      {...props}
    />
  );
};

export default CollectionsEmptyState;
