import React from 'react';
import Flex from 'shared/uikit/Flex';
import useMakeCollectionRoute from 'shared/utils/hooks/useMakeCollectionRoute';
import Section from 'shared/components/molecules/Section';
import { placeholderData } from 'shared/constants/enums';
import type { CollectionType } from '@shared/types/collection';
import classes from './CollectionsSection.module.scss';
import { CollectionItem } from '../../components';
import CollectionsEmptyState from './CollectionsEmptyState';
import CollectionItemSkeleton from '../../components/CollectionItem.skeleton';

interface CollectionSectionProps {
  title: string;
  isLoading?: boolean;
  list?: Array<CollectionType>;
  dataKey?: any;
}

const CollectionsSection: React.FC<CollectionSectionProps> = ({
  title,
  isLoading,
  list = [],
  dataKey,
}) => {
  const makeCollectionRoute = useMakeCollectionRoute();

  if (!isLoading && !list?.length) {
    return <CollectionsEmptyState isFullWidth isFullHeight isFullParent />;
  }

  return (
    <Section title={title} contentClassName={classes.sectionContent}>
      {isLoading ? (
        <Flex className={classes.wrapper}>
          {placeholderData('collections').map(({ id }) => (
            <CollectionItemSkeleton
              key={id}
              className={classes.collectionItem}
            />
          ))}
        </Flex>
      ) : list?.length ? (
        <Flex className={classes.wrapper}>
          {list.map(
            ({
              id,
              access,
              imageUrl,
              itemsCounter,
              name,
              normalizedMediaPosts,
            }) => (
              <CollectionItem
                key={id}
                dataKey={dataKey}
                collectionId={id}
                to={makeCollectionRoute(id)}
                title={name}
                count={itemsCounter}
                thumbnailItems={normalizedMediaPosts}
                cover={imageUrl}
                access={access}
                className={classes.collectionItem}
              />
            )
          )}
        </Flex>
      ) : (
        <CollectionsEmptyState />
      )}
    </Section>
  );
};

export default CollectionsSection;
