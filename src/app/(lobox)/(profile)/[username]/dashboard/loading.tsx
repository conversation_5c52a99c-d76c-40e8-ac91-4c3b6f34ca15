'use client';

import React from 'react';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import classes from './loading.module.scss';

export const MainLoading: React.FC = () => (
  <>
    <Skeleton className={classes.skeleton__main} />
    <Skeleton className={classes.skeleton__main} />
    <Skeleton className={classes.skeleton__main} />
    <Skeleton className={classes.skeleton__main} />
  </>
);
export const SideLoading: React.FC = () => (
  <Skeleton className={classes.skeleton__side} />
);

export default function loading() {
  return (
    <ProfileTabLayout
      mainComponent={<MainLoading />}
      sideComponent={<SideLoading />}
    />
  );
}
