'use client';

import React from 'react';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import useProfilePage from 'shared/hooks/useProfilePage';
import dynamic from 'next/dynamic';
import { MainLoading, SideLoading } from './loading';

const PostInteraction = dynamic(
  () => import('./partials/sections/PostInteraction'),
  {
    ssr: false,
  }
);
const Brief = dynamic(() => import('./partials/sections/Brief'), {
  ssr: false,
  loading: () => <SideLoading />,
});
const TopPosts = dynamic(() => import('./partials/sections/TopPosts'), {
  ssr: false,
});

const ViewChart = dynamic(
  () => import('./partials/sections/ChartSection/ViewChart'),
  {
    ssr: false,
    loading: () => <MainLoading />,
  }
);
const FollowerChart = dynamic(
  () => import('./partials/sections/ChartSection/FollowerChart'),
  {
    ssr: false,
    loading: () => <MainLoading />,
  }
);

const Dashboard = (): JSX.Element => {
  const { isAuthUser, isAuthBusinessPage } = useProfilePage();
  const isMyProfile = isAuthUser || isAuthBusinessPage;

  if (!isMyProfile) {
    return <PermissionDeniedAlert />;
  }

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeDashboardTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <ProfileTabLayout
        mainComponent={
          <>
            <ViewChart />
            <FollowerChart />
            <PostInteraction />
            <TopPosts />
          </>
        }
        sideComponent={<Brief />}
      />
    </PermissionsGate>
  );
};

export default Dashboard;
