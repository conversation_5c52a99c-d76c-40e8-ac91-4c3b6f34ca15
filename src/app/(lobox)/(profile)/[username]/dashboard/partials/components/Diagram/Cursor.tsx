import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import type { TooltipProps } from 'recharts';
import classes from './Cursor.module.scss';

export interface CursorProps extends TooltipProps<any, any> {
  title?: {
    plural: string;
    singular: string;
  };
}

const Cursor: React.FC<CursorProps> = ({ title, label, payload }) => {
  const { value = 0 } = payload?.[0] || {};

  return (
    <Flex className={cnj(classes.cursorRoot)}>
      <Flex className={classes.valueWrapper}>
        <Typography font="500" size={13} height={16} color="brand" mr={8}>
          {value}
        </Typography>
        <Typography font="500" size={13} height={16} color="thirdText" mr={8}>
          {Number(value) > 1 ? title?.plural : title?.singular}
        </Typography>
      </Flex>
      <Flex className={classes.nameWrapper}>
        <Typography font="500" size={13} height={16} color="fifthText" mr={8}>
          {label}
        </Typography>
      </Flex>
    </Flex>
  );
};

export default Cursor;
