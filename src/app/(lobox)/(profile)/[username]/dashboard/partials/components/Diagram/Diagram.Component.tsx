import React from 'react';
import useTheme from 'shared/uikit/utils/useTheme';
import {
  CartesianGrid,
  Area,
  AreaChart,
  XAxis,
  YAxis,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';
import classes from './Diagram.Component.module.scss';
import Cursor from './Cursor';

export interface DiagramProps {
  className?: string;
  title?: {
    plural: string;
    singular: string;
  };
  uniqueId: string;
  data?: Array<{
    name: string;
    value: string;
  }>;
}

const placeholderData = [
  { name: '', value: 40 },
  { name: 'A', value: 0 },
  { name: 'B', value: 0 },
  { name: 'C', value: 0 },
  { name: 'D', value: 0 },
  { name: 'E', value: 0 },
  { name: '', value: 40 },
];

const Diagram: React.FC<DiagramProps> = ({ uniqueId, data, title }) => {
  const { theme: { colors } = {}, isDark } = useTheme();
  const mainStrokeColor = data ? colors?.brand : colors?.transparent;

  const max = Math.max(
    ...(data || placeholderData)?.map((item) => Number(item?.value))
  );
  const numberOfDigits = String(max).length;
  const YaxisWidth =
    numberOfDigits === 1
      ? 10
      : numberOfDigits === 2
        ? 15
        : numberOfDigits === 3
          ? 20
          : 30;

  return (
    <ResponsiveContainer width="100%" aspect={610 / 220}>
      <AreaChart data={data || placeholderData} className={classes.diagramRoot}>
        <defs>
          <linearGradient id={uniqueId} x1="0" y1="0" x2="0" y2="1">
            <stop offset="1%" stopColor={mainStrokeColor} stopOpacity={0.2} />
            <stop offset="99%" stopColor={mainStrokeColor} stopOpacity={0} />
          </linearGradient>
        </defs>
        <Area
          type="monotone"
          dataKey="value"
          isAnimationActive={false}
          stroke={mainStrokeColor}
          strokeWidth={2}
          fillOpacity={1}
          fill={`url(#${uniqueId})`}
        />
        <CartesianGrid
          vertical={false}
          stroke={colors?.brand_10}
          strokeDasharray="8 8"
        />
        <XAxis
          dataKey="name"
          tickMargin={16}
          axisLine={{
            stroke: isDark ? colors?.muteMidGray : colors?.hover_2,
          }}
        />
        <YAxis
          width={YaxisWidth}
          axisLine={false}
          tickLine={false}
          allowDecimals={false}
        />
        {!!data && (
          <Tooltip
            cursor={{ stroke: colors?.techGray_20, strokeWidth: 1 }}
            content={<Cursor title={title} />}
          />
        )}
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default Diagram;
