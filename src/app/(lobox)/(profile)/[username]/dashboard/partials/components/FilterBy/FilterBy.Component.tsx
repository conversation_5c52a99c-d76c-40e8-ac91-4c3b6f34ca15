import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import PopperMenu from 'shared/uikit/PopperMenu';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useStateCallback from 'shared/utils/hooks/useStateCallback';
import classes from './FilterBy.Component.module.scss';

export interface FilterByProps {
  className?: string;
  onChange?: (currentState: string) => void;
}

export const filterValues = {
  people: 'people',
  page: 'page',
  all: 'all',
};

const FilterBy: React.FC<FilterByProps> = ({ className, onChange }) => {
  const [current, setCurrent] = useStateCallback(filterValues.all);
  const { t } = useTranslation();

  const isPeopleFiltered = current === filterValues.people;
  const isPageFiltered = current === filterValues.page;
  const isAllFiltered = current === filterValues.all;

  const itemClickHandle = (item: string) => () => {
    setCurrent(item, onChange);
  };

  return (
    <Flex className={cnj(classes.filterByRoot, className)}>
      <Typography font="400" size={12} height={14} color="colorIconForth">
        {t('filter_by')}
      </Typography>
      <Typography
        font="400"
        size={12}
        height={14}
        color="secondaryDisabledText"
        className={classes.result}
      >
        {t(current)}
      </Typography>
      <PopperMenu
        placement="bottom-end"
        buttonComponent={
          <IconButton
            name="chevron-down"
            type="fas"
            size="sm"
            colorSchema="transparent"
            className={classes.iconButton}
            iconClassName={classes.iconButtonIcon}
          />
        }
      >
        <PopperItem
          onClick={itemClickHandle(filterValues.all)}
          isSelected={isAllFiltered}
          iconName="globe-americas"
          label={t('all')}
          className={cnj(isAllFiltered && classes.active)}
        />
        <PopperItem
          onClick={itemClickHandle(filterValues.people)}
          isSelected={isPeopleFiltered}
          iconName="user-friends"
          label={t('people')}
          className={cnj(isPeopleFiltered && classes.active)}
        />
        <PopperItem
          onClick={itemClickHandle(filterValues.page)}
          isSelected={isPageFiltered}
          iconName="flag"
          label={t('pages')}
          className={cnj(isPageFiltered && classes.active)}
        />
      </PopperMenu>
    </Flex>
  );
};

export default FilterBy;
