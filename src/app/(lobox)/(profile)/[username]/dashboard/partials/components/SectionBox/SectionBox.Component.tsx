import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import type { TypographyProps } from 'shared/uikit/Typography';
import classes from './SectionBox.Component.module.scss';
import SectionBoxHead from './SectionBox.Head';

interface SectionBoxProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  headActionComponent?: React.ReactNode;
  footerLabel?: string;
  onClickFooter?: (...args: any[]) => any;
  titleProps?: Omit<TypographyProps, 'children'>;
  id?: string;
  headClassname?: string;
}

const SectionBox: React.FC<SectionBoxProps> = ({
  children,
  className,
  title,
  headActionComponent,
  footerLabel,
  onClickFooter,
  titleProps,
  headClassname,
  id,
}) => (
  <Flex id={id} className={cnj(classes.sectionBoxRoot, className)}>
    <Flex className={classes.sectionBoxWrapper}>
      {(headActionComponent || !!title) && (
        <SectionBoxHead
          className={headClassname}
          title={title}
          headActionComponent={headActionComponent}
          titleProps={titleProps}
        />
      )}
      {children}
      {!!footerLabel && (
        <Flex className={classes.footer}>
          <Typography
            font="400"
            size={12}
            height={14}
            color="primaryText"
            onClick={onClickFooter}
          >
            {footerLabel}
          </Typography>
        </Flex>
      )}
    </Flex>
  </Flex>
);

export default SectionBox;
