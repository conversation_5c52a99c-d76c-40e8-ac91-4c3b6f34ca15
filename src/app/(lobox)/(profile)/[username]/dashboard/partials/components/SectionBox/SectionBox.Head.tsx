import React from 'react';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import type { TypographyProps } from 'shared/uikit/Typography';
import classes from './SectionBox.Head.module.scss';

interface SectionBoxTitleProps {
  className?: string;
  title?: string;
  headActionComponent?: React.ReactNode;
  titleProps?: Omit<TypographyProps, 'children'>;
}

const SectionBoxHead: React.FC<SectionBoxTitleProps> = ({
  className,
  title,
  headActionComponent,
  titleProps = {},
}) => (
  <Flex
    className={cnj(
      classes.sectionBox,
      title && classes.sectionBoxTitle,
      className
    )}
  >
    {!!title && (
      <Typography
        font="700"
        size={16}
        height={20}
        color="thirdText"
        mr="auto"
        {...titleProps}
      >
        {title}
      </Typography>
    )}
    {headActionComponent}
  </Flex>
);

export default SectionBoxHead;
