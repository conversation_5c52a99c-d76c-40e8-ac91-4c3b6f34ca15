import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import classes from './WeeklyStatsTitle.component.module.scss';

export interface WeeklyStatsTitleProps {
  className?: string;
  title: string;
}

const WeeklyStatsTitle: React.FC<WeeklyStatsTitleProps> = ({
  className,
  title,
}) => (
  <Flex className={cnj(classes.weeklyStatsTitleRoot, className)}>
    <Typography font="400" size={15} height={21} color="primaryText">
      {title}
    </Typography>
  </Flex>
);

export default WeeklyStatsTitle;
