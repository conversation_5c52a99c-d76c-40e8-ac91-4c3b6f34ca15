import React, { useState } from 'react';
import FilterTabular from 'shared/uikit/Filter/FilterTabular';
import Divider from 'shared/uikit/Divider';
import cnj from 'shared/uikit/utils/cnj';
import { getViewCount } from 'shared/utils/api/statistics';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useParams from 'shared/utils/hooks/useParams';
import Information from 'shared/svg/Information';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import Skeleton from 'shared/uikit/Skeleton';
import { SectionBox } from '../../components';
import BriefItem from './Brief.Item';
import { SideLoading } from '../../../loading';
import classes from './Brief.Component.module.scss';

interface BriefProps {
  className?: string;
}

const filterList = [
  { title: 'weekly', day: 7, defaultActive: true },
  { title: 'monthly', day: 30 },
  { title: 'all_time' },
];
const heroList = [
  {
    iconName: 'user-tie',
    titleKey: 'views',
    subtitle: (isPlural: boolean, isBusiness: boolean) => {
      const pageLabel = isPlural ? 'page_views' : 'page_view';
      const profileLabel = isPlural ? 'profile_views' : 'profile_view';

      return isBusiness ? pageLabel : profileLabel;
    },
    color: 'orange',
  },
  /* { // temporary commented
    iconName: 'file-search',
    titleKey: '------wait-for-saeed.Mirza------',
    subtitle: () => 'search_results',
    color: 'green',
  },
  {  // temporary commented
    iconName: 'briefcase',
    titleKey: '------wait-for-saeed.Mirza------',
    subtitle: (condition: boolean) =>
      condition ? 'job_applications' : 'applied_job',
    color: 'blue',
  }, */
  {
    iconName: 'file-alt',
    titleKey: 'postViews',
    subtitle: (isPlural: boolean): string =>
      isPlural ? 'post_views' : 'post_view',
    color: 'gray',
  },
];

const Brief: React.FC<BriefProps> = ({ className }) => {
  const { username } = useParams<{ username: string }>();
  const { t } = useTranslation();
  const [day, setDay] = useState(7);
  const { data, isLoading } = useReactQuery<any>({
    action: {
      apiFunc: getViewCount,
      key: [QueryKeys.getViewCount, `${day}`, username],
      params: {
        from: day ? `${day} day` : undefined,
      },
    },
  });

  const handleAction = (d?: number) => () => {
    setDay(d as any);
  };

  return (
    <SectionBox
      className={cnj(classes.briefSectionWrapper, className)}
      headActionComponent={
        <FilterTabular
          list={filterList.map(({ title, day: d, defaultActive }) => ({
            defaultActive,
            title: t(title),
            action: handleAction(d),
          }))}
          itemClassName={classes.filterTabularItem}
          className={classes.filterTabularWrapper}
        />
      }
    >
      {isLoading ? (
        <SideLoading />
      ) : (
        <>
          {heroList.map(({ iconName, titleKey, subtitle, color }) => {
            const count = data?.[titleKey] || '0';
            const isPlural = Number(count) > 1;

            return (
              <BriefItem
                key={titleKey + color}
                color={color as any}
                subtitle={t(subtitle(isPlural, isBusinessApp))}
                iconName={iconName}
                title={
                  isLoading ? (
                    <Skeleton style={{ width: 50, height: 30 }} />
                  ) : (
                    count
                  )
                }
              />
            );
          })}
          <Divider />
          <BriefItem
            icon={<Information />}
            subtitle={t('keep_as_secret')}
            className={classes.info}
          />
        </>
      )}
    </SectionBox>
  );
};

export default Brief;
