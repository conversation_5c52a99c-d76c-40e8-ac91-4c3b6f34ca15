import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import HeroIcon from 'shared/uikit/HeroIcon';
import Typography from 'shared/uikit/Typography';
import type HeroIconColorTypes from 'shared/uikit/HeroIconColorTypes';
import classes from './Brief.Item.module.scss';

interface BriefItemProps {
  className?: string;
  iconName?: string;
  icon?: React.ReactNode;
  color?: HeroIconColorTypes;
  title?: string;
  subtitle: string;
}

const BriefItem: React.FC<BriefItemProps> = ({
  className,
  iconName = 'house-thin',
  icon,
  color = 'gray',
  title,
  subtitle,
}) => (
  <Flex className={cnj(classes.briefItemRoot, className)}>
    {icon || <HeroIcon iconName={iconName} color={color} />}
    <Flex className={classes.textWrapper}>
      {title && (
        <Typography
          font="700"
          size={26}
          height={30.47}
          color="thirdText"
          className={classes.title}
          isTruncated
        >
          {title}
        </Typography>
      )}
      <Typography
        font="400"
        size={14}
        height={16.41}
        color="primaryDisabledText"
        className={classes.subtitle}
      >
        {subtitle}
      </Typography>
    </Flex>
  </Flex>
);

export default BriefItem;
