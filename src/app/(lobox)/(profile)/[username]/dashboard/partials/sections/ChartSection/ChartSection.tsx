import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import FilterTabular from 'shared/uikit/Filter/FilterTabular';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type FilterTabularProps from 'shared/uikit/Filter/Filter.TabularProps';
import classes from './ChartSection.module.scss';
import { Diagram, SectionBox } from '../../components';
import type { FilterByProps } from '../../components/FilterBy';
import FilterBy from '../../components/FilterBy';
import type { DiagramProps } from '../../components/Diagram/Diagram.Component';
import { MainLoading } from '../../../loading';

interface ChartSectionProps {
  title: {
    plural: string;
    singular: string;
  };
  onTypeFilterChange?: FilterByProps['onChange'];
  onDayFilterChange?: FilterTabularProps['onChange'];
  filterList?: FilterTabularProps['list'];
  report?: {
    label: string;
    number: string | number;
    compareText?: string;
    percent?: string;
    isAscending: boolean;
  };
  data: DiagramProps['data'];
  isLoading?: boolean;
}

const ChartSection: React.FC<ChartSectionProps> = ({
  title,
  filterList,
  onTypeFilterChange,
  onDayFilterChange,
  report,
  data,
  isLoading,
}) => {
  const { t } = useTranslation();

  return (
    <SectionBox
      title={title.plural}
      headActionComponent={<FilterBy onChange={onTypeFilterChange} />}
    >
      {filterList && (
        <FilterTabular
          onChange={onDayFilterChange}
          list={filterList}
          className={classes.tabFilter}
        />
      )}
      {isLoading ? (
        <MainLoading />
      ) : (
        <>
          {' '}
          {report && (
            <Flex className={classes.reportWrapper}>
              <Typography font="700" size={16} height={20} color="trench">
                {report?.number}
              </Typography>
              <Typography
                font="400"
                size={14}
                height={18.2}
                color="muteMidGray"
                ml={6}
                mr="auto"
              >
                {report?.label}
              </Typography>
              {report?.percent && (
                <Flex className={classes.percentReport}>
                  <Typography
                    font="400"
                    size={14}
                    height={18}
                    color="muteMidGray"
                  >
                    {`${t('compare_to')} ${report?.compareText}`}
                  </Typography>
                  <Typography
                    font="700"
                    size={16}
                    height={20}
                    color="error"
                    className={cnj(report?.isAscending && classes.green)}
                  >
                    {report?.percent}
                  </Typography>
                </Flex>
              )}
            </Flex>
          )}
          <Diagram
            title={title}
            uniqueId={Math.random().toString()}
            data={data}
          />
        </>
      )}
    </SectionBox>
  );
};

export default ChartSection;
