import React, { useState } from 'react';
import formatDate from 'shared/utils/toolkit/formatDate';
import { getFollowDiagram } from 'shared/utils/api/statistics';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import isFinite from 'lodash/isFinite';
import useParams from 'shared/utils/hooks/useParams';
import ChartSection from './ChartSection';
import { filterValues } from '../../components/FilterBy/FilterBy.Component';
import differencePercentage, {
  percentStr,
} from '../../components/differencePercentage';
import { filterList } from './ViewChart';

const FollowersChart: React.FC = () => {
  const { username } = useParams<{ username: string }>();
  const [dayIndex, setIndexDay] = useState(0);
  const [type, setType] = useState<string>();
  const { t } = useTranslation();
  const isToday = dayIndex === 0;

  const { data }: any = useReactQuery({
    action: {
      apiFunc: getFollowDiagram,
      key: [QueryKeys.getFollowDiagram, `${dayIndex}`, `${type}`, username],
      params: {
        userType: !type
          ? undefined
          : type === filterValues.page
            ? filterValues.page.toUpperCase()
            : type === filterValues.people
              ? 'PERSON'
              : undefined,
        interval: filterList[dayIndex].interval,
        from: filterList[dayIndex].from,
        compareFrom: filterList[dayIndex].compareFrom,
      },
    },
  });

  const percentNumber = parseFloat(data?.growthPercentage);

  const titles = {
    plural: t('followers_cap'),
    singular: t('follower_cap'),
  };

  return (
    <ChartSection
      onDayFilterChange={setIndexDay}
      onTypeFilterChange={setType}
      title={titles}
      filterList={filterList.map(({ title }) => ({
        title: t(title),
      }))}
      report={{
        isAscending: percentNumber > 0,
        label: Number(data?.count) > 1 ? t('new_followers') : t('new_follower'),
        number: data?.count,
        compareText: t(filterList[dayIndex]?.title),
        percent: isFinite(percentNumber)
          ? percentStr(percentNumber)
          : percentStr(differencePercentage(0, parseInt(data?.count || 0, 10))),
      }}
      data={data?.timeBuckets?.map(
        ({ bucket, count }: any, i: number, { length }: any) => {
          const zoneBucket = bucket?.endsWith('Z') ? bucket : `${bucket}Z`;
          const zoneBucketWithT = zoneBucket.replace(' ', 'T');

          return {
            name:
              i === 0 || i === length - 1
                ? ''
                : formatDate(zoneBucketWithT, isToday ? 'MMM D, h a' : 'MMM D'),
            value: isFinite(parseFloat(count)) ? parseFloat(count) : 0,
          };
        }
      )}
    />
  );
};

export default FollowersChart;
