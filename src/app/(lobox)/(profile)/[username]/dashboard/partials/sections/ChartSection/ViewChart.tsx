import React, { useState } from 'react';
import { getViewDiagram } from 'shared/utils/api/statistics';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formatDate from 'shared/utils/toolkit/formatDate';
import isFinite from 'lodash/isFinite';
import useParams from 'shared/utils/hooks/useParams';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import ChartSection from './ChartSection';
import { filterValues } from '../../components/FilterBy/FilterBy.Component';
import differencePercentage, {
  percentStr,
} from '../../components/differencePercentage';

export const filterList = [
  {
    title: 'today',
    compareText: '24 hours',
    from: '1 day',
    compareFrom: '2 day',
    interval: '4 hour',
  },
  {
    title: '7_days',
    compareText: '7 days',
    from: '7 day',
    compareFrom: '14 day',
    interval: '1 day',
  },
  {
    title: '30_days',
    compareText: '30 days',
    from: '30 day',
    compareFrom: '30 day',
    interval: '3 day',
  },
  {
    title: '90_days',
    compareText: '90 days',
    from: '90 day',
    compareFrom: '180 day',
    interval: '9 day',
  },
];

const ViewChart: React.FC = () => {
  const { username } = useParams<{ username: string }>();
  const [dayIndex, setIndexDay] = useState(0);
  const [type, setType] = useState<string>();
  const { t } = useTranslation();
  const isToday = dayIndex === 0;

  const { data, isLoading } = useReactQuery<any>({
    action: {
      apiFunc: getViewDiagram,
      key: [QueryKeys.getViewDiagram, `${dayIndex}`, `${type}`, username],
      params: {
        userType: !type
          ? undefined
          : type === filterValues.page
            ? filterValues.page.toUpperCase()
            : type === filterValues.people
              ? 'PERSON'
              : undefined,
        interval: filterList[dayIndex].interval,
        from: filterList[dayIndex].from,
        compareFrom: filterList[dayIndex].compareFrom,
      },
    },
  });

  const percentNumber = parseFloat(data?.growthPercentage);

  const businessTitle = {
    plural: t('page_views'),
    singular: t('page_view'),
  };
  const userTitle = {
    plural: t('profile_views'),
    singular: t('profile_view'),
  };

  return (
    <ChartSection
      isLoadin={isLoading}
      onDayFilterChange={setIndexDay}
      onTypeFilterChange={setType}
      title={isBusinessApp ? businessTitle : userTitle}
      filterList={filterList.map(({ title }) => ({
        title: t(title),
      }))}
      report={{
        isAscending: percentNumber > 0,
        label: Number(data?.count) > 1 ? t('views_cap') : t('view'),
        number: data?.count,
        compareText: t(filterList[dayIndex]?.title),
        percent: isFinite(percentNumber)
          ? percentStr(percentNumber)
          : percentStr(differencePercentage(0, parseInt(data?.count || 0, 10))),
      }}
      data={data?.timeBuckets?.map(
        ({ bucket, count }: any, i: number, { length }: any) => {
          const zoneBucket = bucket?.endsWith('Z') ? bucket : `${bucket}Z`;
          const zoneBucketWithT = zoneBucket.replace(' ', 'T');

          return {
            name:
              i === 0 || i === length - 1
                ? ''
                : formatDate(zoneBucketWithT, isToday ? 'MMM D, h A' : 'MMM D'),
            value: isFinite(parseFloat(count)) ? parseFloat(count) : 0,
          };
        }
      )}
    />
  );
};
// Sep 22, 03 pm
// Sep 22, 4 PM
export default ViewChart;
