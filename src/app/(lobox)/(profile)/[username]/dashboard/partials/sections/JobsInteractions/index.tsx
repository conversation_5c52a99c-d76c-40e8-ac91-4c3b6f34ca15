import React from 'react';
import Flex from 'shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './index.module.scss';
import { SectionBox } from '../../components';
import JobsInteractionsItem from '../PostInteraction/PostInteraction.Item';

const JobsInteractions: React.FC = () => {
  const { t } = useTranslation();
  const mockData = [
    { iconName: 'briefcase', number: 86, label: t('active_jobs') },
    { iconName: 'business-time', number: 682, label: t('total_jobs') },
    { iconName: 'file-check', number: '1,548', label: t('apps_need_review') },
    { iconName: 'file-chart-line', number: '2,3k', label: t('total_apps') },
  ];

  return (
    <SectionBox title={t('jobs')}>
      <Flex className={classes.itemWrapper}>
        {mockData.map(({ iconName, number, label }) => (
          <JobsInteractionsItem
            key={iconName + label}
            iconName={iconName}
            number={number}
            label={label}
          />
        ))}
      </Flex>
    </SectionBox>
  );
};

export default JobsInteractions;
