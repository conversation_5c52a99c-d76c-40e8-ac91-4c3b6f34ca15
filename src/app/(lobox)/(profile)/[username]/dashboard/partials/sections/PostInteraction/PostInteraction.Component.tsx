import React, { useState } from 'react';
import FilterTabular from 'shared/uikit/Filter/FilterTabular';
import Flex from 'shared/uikit/Flex';
import Icon from 'shared/uikit/Icon';
import Tooltip from 'shared/uikit/Tooltip';
import Typography from 'shared/uikit/Typography';
import { getPostInteractionsCount } from 'shared/utils/api/statistics';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useParams from 'shared/utils/hooks/useParams';
import classes from './PostInteraction.Component.module.scss';
import { SectionBox } from '../../components';
import PostInteractionItem from './PostInteraction.Item';

const filterList: Array<{
  title: string;
  day: number;
  defaultActive?: boolean;
}> = [
  { title: 'today', day: 1, defaultActive: true },
  { title: '7_days', day: 7 },
  { title: '30_days', day: 30 },
  { title: '90_days', day: 90 },
];
const interactionList = [
  {
    iconName: 'file-alt',
    numKey: 'posts',
    label: { plural: 'posts', singular: 'post' },
  },
  {
    iconName: 'comment-alt-lines',
    numKey: 'comments',
    label: { plural: 'comments', singular: 'comment' },
  },
  {
    iconName: 'share',
    numKey: 'shares',
    label: { plural: 'shares', singular: 'share' },
  },
  {
    iconName: 'thumbs-up',
    numKey: 'actions',
    label: { plural: 'reactions', singular: 'reaction' },
  },
];

const PostInteraction: React.FC = () => {
  const { username } = useParams<{ username: string }>();
  const [day, setDay] = useState(1);
  const { t } = useTranslation();

  const { data }: { data: any } = useReactQuery({
    action: {
      apiFunc: getPostInteractionsCount,
      key: [QueryKeys.getPostInteractionsCount, `${day}`, username],
      params: {
        from: `${day} day`,
      },
    },
  });

  const handleAction = (d: number) => () => {
    setDay(d);
  };

  return (
    <SectionBox
      id="PostInteraction"
      headActionComponent={
        <Tooltip
          triggerWrapperClassName={classes.toolTip}
          trigger={
            <Icon
              color="highlightIcon"
              type="fal"
              name="info-circle"
              size={15}
            />
          }
        >
          <Typography
            className={classes.toolTipContent}
            size={14}
            font="400"
            height={18}
            color="tooltipText"
          >
            {t('this_sec_interactions_posts')}
          </Typography>
        </Tooltip>
      }
      title={t('post_integrations')}
      titleProps={{ mr: 0 }}
    >
      <FilterTabular
        list={filterList.map(({ defaultActive, title, day: d }) => ({
          defaultActive,
          title: t(title),
          action: handleAction(d),
        }))}
        className={classes.tabFilter}
      />
      <Flex className={classes.itemWrapper}>
        {interactionList.map(({ iconName, numKey, label }) => {
          const number = Number(data?.[numKey] || 0);
          const isPlural = number > 1;

          return (
            <PostInteractionItem
              key={iconName + label}
              iconName={iconName}
              number={number}
              label={t(isPlural ? label.plural : label.singular)}
            />
          );
        })}
      </Flex>
    </SectionBox>
  );
};

export default PostInteraction;
