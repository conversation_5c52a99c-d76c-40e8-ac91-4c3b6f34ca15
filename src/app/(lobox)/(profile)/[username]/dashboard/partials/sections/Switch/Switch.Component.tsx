import React from 'react';
import Form from 'shared/uikit/Form';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import { setOpenForJobOpportunities } from 'shared/utils/api/profile';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useGetOpenForJobOpportunities from 'shared/hooks/api-hook/useGetOpenForJobOpportunities';
import { SectionBox } from '../../components';

const Switch: React.FC = () => {
  const { t } = useTranslation();
  const { openForJobOpportunities } = useGetOpenForJobOpportunities();

  return (
    <SectionBox>
      <Form
        enableReinitialize
        initialValues={{ openForJobOpportunities }}
        apiFunc={setOpenForJobOpportunities}
      >
        {({ handleSubmit }: any) => (
          <DynamicFormBuilder
            groups={[
              {
                name: 'openForJobOpportunities',
                cp: 'switch',
                label: t('open_to_job'),
                onChange: () => {
                  handleSubmit();
                },
              },
            ]}
          />
        )}
      </Form>
    </SectionBox>
  );
};

export default Switch;
