import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalDialog from 'shared/uikit/Modal/ModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import type { ModalDialogProps } from 'shared/components/molecules';
import classes from './TopPosts.Modal.module.scss';
import TopPostsShowAll from '../TopPosts.ShowAll';
import type { TopPostItemType } from '../index';

type OmitChildrenModalDialogProps = Omit<ModalDialogProps, 'children'>;

export interface TopPostsModalProps extends OmitChildrenModalDialogProps {
  selectedPost?: TopPostItemType;
}

const TopPostsModal = ({
  isOpen,
  onClose,
}: TopPostsModalProps): JSX.Element => {
  const { t } = useTranslation();

  return (
    <ModalDialog isOpen={isOpen} onBack={onClose} onClose={onClose}>
      <ModalHeaderSimple title={t('top_posts')} visibleHeaderDivider />
      <ModalBody className={classes.modalBodyView}>
        <TopPostsShowAll />
      </ModalBody>
    </ModalDialog>
  );
};

export default TopPostsModal;
