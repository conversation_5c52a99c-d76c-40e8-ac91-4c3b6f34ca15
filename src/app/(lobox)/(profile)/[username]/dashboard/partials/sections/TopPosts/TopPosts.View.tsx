import React from 'react';
import Flex from 'shared/uikit/Flex';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { useQueryClient } from '@tanstack/react-query';
import FeedCard, {
  FeedCardError,
  FeedSkeleton,
} from 'shared/components/Organism/FeedCard';
import useGetFeedDetail from 'shared/hooks/api-hook/useGetFeedDetail';
import type { IFeedElement } from 'shared/types/feedElement';
import classes from './TopPosts.View.module.scss';
import type { TopPostItemType } from './index';

interface TopPostViewProps {
  post?: TopPostItemType;
  onClose: any;
}

const TopPostsView = ({ post, onClose }: TopPostViewProps): JSX.Element => {
  const queryClient = useQueryClient();
  const feedId = post?.id;
  const { data, isLoading } = useGetFeedDetail({ feedId });

  const handleDeletePost = () => {
    queryClient.setQueryData([QueryKeys.getFeedDetail, `${feedId}`], undefined);
  };
  const handleUpdatePost = (feed: IFeedElement) => {
    queryClient.setQueryData([QueryKeys.getFeedDetail, `${feedId}`], feed);
  };

  return (
    <Flex className={classes.topPostsViewRoot}>
      {isLoading ? (
        <FeedSkeleton />
      ) : data ? (
        <FeedCard
          feedElement={data}
          onDeletePost={handleDeletePost}
          onReplacePost={handleUpdatePost}
        />
      ) : (
        <FeedCardError onBack={onClose} />
      )}
    </Flex>
  );
};

export default TopPostsView;
