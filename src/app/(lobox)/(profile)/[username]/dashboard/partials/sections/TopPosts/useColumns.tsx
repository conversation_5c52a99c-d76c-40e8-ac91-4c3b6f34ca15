import useDisclosure from 'shared/utils/hooks/useDisclosure';
import useTranslation from 'shared/utils/hooks/useTranslation';
import React, { useState } from 'react';
import Typography from 'shared/uikit/Typography';
import RichTextView from 'shared/uikit/RichText/RichTextView';
import Flex from 'shared/uikit/Flex';
import type UseDisclosureType from 'shared/utils/UseDisclosureType';
import classes from './useColumns.module.scss';

export type UseColumnsType = Omit<UseDisclosureType, 'onToggle'> & {
  columns: Array<{
    key: string;
    title: string;
    render: (...args: any[]) => JSX.Element;
  }>;
  handleClickView: (item: any) => () => void;
  selectedPost?: any;
  setPost: Function;
};

const useColumns = (initialSelectedPost?: any): UseColumnsType => {
  const { t } = useTranslation();
  const { isOpen, onOpen, onClose } = useDisclosure(!!initialSelectedPost);
  const [selectedPost, setPost] = useState(initialSelectedPost);

  const handleClickView = (dataRow: any) => () => {
    setPost(dataRow);
    onOpen();
  };

  const columns = [
    // {
    //   key: 'date',
    //   title: t('date'),
    //   render: ({ value }: any) => (
    //     <Typography font="500" size={14} height={16.41} color="colorIconForth">
    //       {value}
    //     </Typography>
    //   ),
    // },
    {
      key: 'title',
      title: t('title'),
      render: ({ value }: any) => (
        <Flex className={classes.titleColumn}>
          {value ? (
            <RichTextView
              html={value}
              typographyProps={{
                size: 14,
                font: '400',
                color: 'primaryText',
                height: 18,
                isWordWrap: true,
                isTruncated: true,
                lineNumber: 1,
              }}
              row={1}
              showMore={false}
            />
          ) : (
            <Typography color="colorIconForth2" size={14}>
              {t('no_title_no_paranthesis')}
            </Typography>
          )}
        </Flex>
      ),
    } /*
    {
      key: 'reactions',
      title: t('reactions'),
      render: ({ value }: any) => (
        <Typography
          font="400"
          size={14}
          height={18.2}
          color="primaryText"
          textAlign="center"
        >
          {value}
        </Typography>
      ),
    },
    {
      key: 'comments',
      title: t('comments'),
      render: ({ value }: any) => (
        <Typography
          font="400"
          size={14}
          height={18.2}
          color="primaryText"
          textAlign="center"
        >
          {value}
        </Typography>
      ),
    }, */,
    {
      key: 'action',
      title: '',
      render: ({ dataRow }: any) => (
        <Typography
          font="400"
          size={14}
          height={16.41}
          color="brand"
          onClick={handleClickView(dataRow)}
          textAlign="right"
        >
          {t('view')}
        </Typography>
      ),
    },
  ];

  return {
    columns,
    handleClickView,
    isOpen,
    onOpen,
    onClose,
    selectedPost,
    setPost,
  };
};

export default useColumns;
