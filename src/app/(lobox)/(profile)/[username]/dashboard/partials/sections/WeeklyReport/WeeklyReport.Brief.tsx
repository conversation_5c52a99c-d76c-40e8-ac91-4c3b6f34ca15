import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './WeeklyReport.Brief.module.scss';

interface WeeklyReportBriefProps {
  className?: string;
}

const SEARCH_WEEKLY_BRIEF_NUMBER = 0;

const WeeklyReportBrief: React.FC<WeeklyReportBriefProps> = ({ className }) => {
  const { t } = useTranslation();

  return (
    <Flex className={cnj(classes.weeklyReportBriefRoot, className)}>
      <Typography font="700" size={16} height={20} color="trench" mr={6}>
        {SEARCH_WEEKLY_BRIEF_NUMBER}
      </Typography>
      <Flex>
        <Typography
          font="400"
          size={14}
          height={18}
          color="disabledGray_muteMidGray"
          mb={8}
        >
          {t('searched_times')}
        </Typography>
        <Typography font="500" size={14} height={16.41} color="colorIconForth2">
          {t('search_boundary')}
        </Typography>
      </Flex>
    </Flex>
  );
};

export default WeeklyReportBrief;
