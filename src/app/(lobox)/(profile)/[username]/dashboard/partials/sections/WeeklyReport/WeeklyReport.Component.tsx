import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Divider from 'shared/uikit/Divider';
import classes from './WeeklyReport.Component.module.scss';
import { SectionBox } from '../../components';
import WeeklyReportBrief from './WeeklyReport.Brief';
import WeeklyReportSearchers from './WeeklyReport.Searchers';
import WeeklyReportWorks from './WeeklyReport.Works';
import WeeklyReportKeywords from './WeeklyReport.Keywords';

const WeeklyReport: React.FC = () => {
  const { t } = useTranslation();

  return (
    <SectionBox title={t('weekly_search')}>
      <WeeklyReportBrief />
      <Divider className={classes.resultDivider} />
      <WeeklyReportSearchers />
      <Divider className={classes.resultDivider} />
      <WeeklyReportWorks />
      <Divider className={classes.resultDivider} />
      <WeeklyReportKeywords />
    </SectionBox>
  );
};

export default WeeklyReport;
