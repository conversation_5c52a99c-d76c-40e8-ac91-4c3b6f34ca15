import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Pagination from 'shared/uikit/Pagination';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './WeeklyReport.Keywords.module.scss';
import { WeeklyStatsTitle } from '../../components';

const mockList = [
  'User Interface Designer',
  'Lead User Interface Designer',
  'Lead UX Architect',
  'Creative Director',
  'Freelance UI Designer',
  'Remote Senior Art Director',
];

interface WeeklyReportKeywordsProps {
  className?: string;
}

const WeeklyReportKeywords: React.FC<WeeklyReportKeywordsProps> = ({
  className,
}) => {
  const { t } = useTranslation();

  return (
    <Flex className={cnj(classes.weeklyReportKeywordsRoot, className)}>
      <WeeklyStatsTitle title={t('searchers_key')} />
      <Flex className={classes.listWrapper}>
        {mockList.map((item) => (
          <Typography
            key={item}
            font="700"
            size={15}
            height={21}
            color="thirdText"
            className={classes.keywordItem}
          >
            {item}
          </Typography>
        ))}
      </Flex>
      <Pagination
        onPageChange={console.info}
        // defaultPage={45}
        totalPagesCount={4}
        componentName="keyword-pagination"
      />
    </Flex>
  );
};

export default WeeklyReportKeywords;
