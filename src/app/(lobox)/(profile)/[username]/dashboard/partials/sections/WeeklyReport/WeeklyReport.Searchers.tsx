import React from 'react';
import Flex from 'shared/uikit/Flex';
import ProgressItem from 'shared/uikit/ProgressItem';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './WeeklyReport.Searchers.module.scss';
import { WeeklyStatsTitle } from '../../components';

interface WeeklyReportSearchersProps {
  className?: string;
}

const WeeklyReportSearchers: React.FC<WeeklyReportSearchersProps> = ({
  className,
}) => {
  const { t } = useTranslation();
  const mockData = [
    { pV: 30, pN: 'founder', title: t('founder') },
    { pV: 22, pN: 'creative', title: t('creative_designer') },
    { pV: 13, pN: 'business', title: t('business_strategist') },
    { pV: 12, pN: 'research', title: t('research_fellow') },
    { pV: 8, pN: 'recruit', title: t('recruiters') },
    { pV: 15, pN: 'others', title: t('others_cap') },
  ];

  return (
    <Flex className={className}>
      <WeeklyStatsTitle title={t('searchers_do')} />
      <Flex className={classes.progressWrapper}>
        {mockData.map(({ pV, pN, title }) => (
          <ProgressItem
            key={pN}
            progressValue={pV}
            progressName={pN}
            title={title}
            styles={{ root: classes.progressItem }}
          />
        ))}
      </Flex>
    </Flex>
  );
};

export default WeeklyReportSearchers;
