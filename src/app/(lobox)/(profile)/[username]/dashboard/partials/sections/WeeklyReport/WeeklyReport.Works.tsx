import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Pagination from 'shared/uikit/Pagination';
import useTranslation from 'shared/utils/hooks/useTranslation';
import CardItem from 'shared/components/molecules/CardItem/CardItem';
import classes from './WeeklyReport.Works.module.scss';
import { WeeklyStatsTitle } from '../../components';

const mockList = [
  {
    ttl: 'Tribal Worldwide',
    sttl: '@tribalworldwide',
    dsc: 'Company',
    img: 'https://picsum.photos/id/23/40/40',
  },
  {
    ttl: 'Unilever',
    sttl: '@unilevercompany',
    dsc: 'Company',
    img: 'https://picsum.photos/id/24/40/40',
  },
  {
    ttl: 'Microsoft',
    sttl: '@microsoft',
    dsc: 'Company',
    img: 'https://picsum.photos/id/25/40/40',
  },
  {
    ttl: 'Marmara University',
    sttl: '@marmarauniversity',
    dsc: 'University',
    img: 'https://picsum.photos/id/26/40/40',
  },
  {
    ttl: 'IBM',
    sttl: '@ibmcompany',
    dsc: 'Company',
    img: 'https://picsum.photos/id/27/40/40',
  },
  {
    ttl: '4129 Grey',
    sttl: '@4129grey',
    dsc: 'Company',
    img: 'https://picsum.photos/id/28/40/40',
  },
];

interface WeeklyReportWorksProps {
  className?: string;
}

const WeeklyReportWorks: React.FC<WeeklyReportWorksProps> = ({ className }) => {
  const { t } = useTranslation();

  return (
    <Flex className={cnj(classes.weeklyReportWorksRoot, className)}>
      <WeeklyStatsTitle title={t('searchers_at')} />
      <Flex className={classes.cardItemsWrapper}>
        {mockList.map(({ img, ttl, sttl, dsc }) => (
          <CardItem
            key={sttl + ttl}
            image={img}
            title={ttl}
            subtitle={sttl}
            description={dsc}
            className={classes.cardItem}
          />
        ))}
      </Flex>
      <Pagination onPageChange={console.info} totalPagesCount={10} />
    </Flex>
  );
};

export default WeeklyReportWorks;
