import React from 'react';
import { dehydrate } from '@tanstack/query-core';
import Hydrate from 'shared/utils/hydrate.client';
import getServerSideQueryClient from 'shared/utils/getServerSideQueryClient';
import { getObjectDetail } from 'shared/utils/api/object';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { notFound } from 'next/navigation';
import getAccessTokenFromCookies from 'shared/utils/getAccessTokenFromCookies';

interface Props extends React.PropsWithChildren {
  params: { username: string };
}

export default async function DataWrapper({ children, params }: Props) {
  const username = params?.username;
  const queryClient = getServerSideQueryClient();
  const queryKey = [QueryKeys.objectDetail, username];
  const accessToken = await getAccessTokenFromCookies();

  await queryClient.prefetchQuery(queryKey, () =>
    getObjectDetail({ accessToken, params: { username } })
  );

  const data = queryClient.getQueryData(queryKey);

  if (!data || data?.status === '404' || data?.youAreBlocked) {
    return notFound();
  }

  const dehydratedState = dehydrate(queryClient);

  return <Hydrate state={dehydratedState}>{children}</Hydrate>;
}
