'use client';

import React from 'react';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import Flex from 'shared/uikit/Flex';
import FeedCardSkeleton from 'shared/components/Organism/FeedCard/FeedCard.skeleton';
import NoticeBoxSkeleton from 'shared/components/molecules/NoticeBox/NoticeBox.skeleton';
import FeedFilter from 'shared/components/molecules/FeedFilter/FeedFilter';
import classes from './Feed.skeleton.module.scss';

export const MainSkeleton = () => (
  <>
    <FeedFilter />
    <FeedCardSkeleton />
  </>
);

const Loading = (): JSX.Element => (
  <ProfileTabLayout
    className={classes.feedSkeletonRoot}
    showSideOnDesktop
    mainComponent={<MainSkeleton />}
    sideComponent={
      <Flex className={classes.sticky}>
        <NoticeBoxSkeleton className={classes.noticeBoxSkeleton} />
      </Flex>
    }
  />
);

export default Loading;
