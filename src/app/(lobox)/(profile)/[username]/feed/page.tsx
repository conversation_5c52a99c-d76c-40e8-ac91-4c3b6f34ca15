'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import QueryKeys from '@shared/utils/constants/queryKeys';
import CreatePostProgressBar from '@shared/components/Organism/CreatePostProgressBar';
import Flex from '@shared/uikit/Flex/index';
import CreatePostBox from '@shared/components/Organism/CreatePostBox';
import PermissionDeniedAlert from '@shared/components/Organism/PermissionDeniedAlert';
import ProfileTabLayout from '@shared/uikit/Layout/Profile/ProfileTabLayout';
import PermissionsGate from '@shared/components/molecules/PermissionsGate';
import { SCOPES } from '@shared/constants/userRoles.scopes';
import useHasPermission from '@shared/hooks/useHasPermission';
import useProfilePage from '@shared/hooks/useProfilePage';
import { MAIN_CENTER_WRAPPER_ID } from '@shared/constants/enums';
import PostsList from 'shared/components/Organism/PostsList';
import {
  getUserAllPosts,
  getUserFeedList,
  getUserHighlights,
} from '@shared/utils/api/post';
import { Highlights } from './partials/sections';
import classes from './FeedList.module.scss';
import { MainSkeleton } from './loading';

const FeedFilter = dynamic(
  () => import('@shared/components/molecules/FeedFilter/FeedFilter'),
  {
    ssr: false,
  }
);

const FeedList = (): JSX.Element => {
  const [filter, setFilter] = React.useState<'all' | 'posts' | 'highlights'>(
    'all'
  );
  const [parentRef, setParentRef] = React.useState<{ current: HTMLElement }>();
  const { getObjectProp, isAuthBusinessPage, isAuthUser } = useProfilePage();
  const hasEditPageAccess = useHasPermission([SCOPES.canCreatePost]);

  const visibleCreatePost =
    isAuthUser || (isAuthBusinessPage && hasEditPageAccess);

  const objectId = getObjectProp({ userKey: 'id', pageKey: 'id' });

  const userAllPostsKey = React.useMemo(
    () => [QueryKeys.userAllPosts, objectId],
    [objectId]
  );

  const userPostsKey = React.useMemo(
    () => [QueryKeys.userPosts, objectId],
    [objectId]
  );

  const userHighlightsKey = React.useMemo(
    () => [QueryKeys.userHighlights, objectId],
    [objectId]
  );

  const queryKey =
    filter === 'all'
      ? userAllPostsKey
      : filter === 'posts'
        ? userPostsKey
        : userHighlightsKey;

  const apiFunc =
    filter === 'all'
      ? () => getUserAllPosts({ userId: objectId })
      : filter === 'posts'
        ? () => getUserFeedList({ userId: objectId })
        : () => getUserHighlights({ userId: objectId });

  React.useEffect(() => {
    const scrollWrapper = document.getElementById(
      MAIN_CENTER_WRAPPER_ID
    ) as HTMLElement;
    setParentRef({ current: scrollWrapper });
  }, []);

  const initialLoadingSkeleton = React.useMemo(() => <MainSkeleton />, []);

  const isFiltersVisible = filter === 'all';

  const topListItem = React.useMemo(
    () => (
      <>
        {visibleCreatePost && <CreatePostBox />}
        <CreatePostProgressBar />
        {isFiltersVisible && (
          <FeedFilter selectedFilter={filter} onFilterClick={setFilter} />
        )}
      </>
    ),
    [isFiltersVisible, visibleCreatePost, filter]
  );

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeFeedTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <ProfileTabLayout
        className="mt-8_12"
        key={filter}
        showSideOnDesktop
        mainComponent={
          <PostsList
            scrollRef={parentRef}
            topListItem={topListItem}
            queryKey={queryKey}
            apiFunc={apiFunc}
            initialLoadingSkeleton={initialLoadingSkeleton}
            className="!p-0"
            startMargin={730}
          />
        }
        sideComponent={
          <Flex className={classes.sticky}>
            <Highlights />
          </Flex>
        }
      />
    </PermissionsGate>
  );
};

export default FeedList;
