import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useAuthUser from 'shared/utils/hooks/useAuthUser';
import EmptyState from 'shared/components/Organism/EmptyState/EmptyState';
import noFeedSvg from '../../../images/esFeed.svg';

const FeedEmptyState = (): JSX.Element => {
  const { t } = useTranslation();
  const {
    data: { fullName },
  }: any = useAuthUser();

  return (
    <EmptyState
      hasMargin
      image={noFeedSvg}
      caption={fullName}
      message={t('share_ur_thoughts')}
      action={{
        title: t('create_post'),
        onClick: () => console.info('some action'),
      }}
    />
  );
};

export default FeedEmptyState;
