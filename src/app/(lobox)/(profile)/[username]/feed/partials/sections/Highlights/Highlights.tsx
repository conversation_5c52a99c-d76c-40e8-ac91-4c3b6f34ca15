import React, { useEffect } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { routeNames } from 'shared/utils/constants/routeNames';
import dateFromNow from 'shared/utils/toolkit/dateFromNow';
import event from 'shared/utils/toolkit/event';
import ListItem from 'shared/uikit/ListItem';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useGetUserHighlights from 'shared/hooks/api-hook/useGetUserHighlights';
import useProfilePage from 'shared/hooks/useProfilePage';
import NoticeBox from 'shared/components/molecules/NoticeBox/NoticeBox';
import ItemSkeleton from 'shared/components/molecules/NoticeBox/Item.skeleton';
import eventKeys from 'shared/constants/event-keys';
import HighlightImage from 'shared/svg/Highlights';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import postTypes from 'shared/constants/postTypes';
import classes from './Highlights.module.scss';

const mockList = ['item_1', 'item_4', 'item_3'];

const Highlights = (): JSX.Element => {
  const { t } = useTranslation();

  const { getObjectProp } = useProfilePage();
  const objectId = getObjectProp({ userKey: 'id', pageKey: 'id' });
  const { data, hasNextPage, isLoading, refetch } = useGetUserHighlights({
    key: 'feedSidebarHighlights',
    objectId: objectId as string,
    enabled: !!objectId,
    size: 6,
  });

  const highlightsList = !data.length
    ? []
    : data.map((x: any) => ({ ...x.post.highlight, id: x.post.id }));

  useEffect(() => {
    event.on(eventKeys.deleteHighlight, refetch);
  }, []);

  const navigateSearchPage = useNavigateSearchPage();

  const handleClick = (item) => {
    navigateSearchPage({
      pathname: routeNames.searchPosts,
      currentEntityId: item.id,
      postedByUserIds: objectId,
      type: postTypes.HIGHLIGHT,
    });
  };

  return (
    <NoticeBox
      title={t('highlights')}
      onMoreClicked={handleClick}
      isVisibleShowMore={hasNextPage}
      className={classes.highlightsContainer}
    >
      <Flex className={classes.listContainer}>
        {isLoading ? (
          mockList.map((key) => <ItemSkeleton isPage key={key} />)
        ) : !highlightsList.length ? (
          <Typography color="border" font="400" size={15}>
            {t('no_highlight')}
          </Typography>
        ) : (
          highlightsList?.map((item) => (
            <ListItem
              labelTruncated
              onClick={() => handleClick(item)}
              key={item.id}
              className={classes.item}
              leftSvg={
                <Flex className={classes.icon}>
                  <HighlightImage type={item?.type} />
                </Flex>
              }
              label={t(`highlight_${item.type.toLowerCase()}`)}
              labelsContainerClassName={classes.textContainer}
              labelColor="thirdText"
              labelSize={16}
              labelFont="bold"
              labelClassName={classes.labelClassName}
              secondaryLabel={dateFromNow(item.startDate, false, t)}
              secondaryLabelColor="fifthText"
              secondaryLabelSize={15}
              hover
              hoverBgColor="hoverPrimary"
              hoverColor="thirdText"
            />
          ))
        )}
      </Flex>
    </NoticeBox>
  );
};

export default Highlights;
