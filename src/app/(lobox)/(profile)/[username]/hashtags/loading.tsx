'use client';

import React from 'react';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import { HashtagItemSkeleton } from '@shared/components/molecules/HashtagItem/HashtagItemSkeleton.component';
import classes from './loading.module.scss';

export function SectionSkeleton({ items = 6 }: { items?: number }) {
  return (
    <>
      <Skeleton className={classes.label} />
      <Flex className={classes.skeletonListWrapper}>
        {Array(items)
          .fill(null)
          .map((_, idx) => (
            <Flex key={`card-skeleton-${idx}`} className={classes.cardSkeleton}>
              <HashtagItemSkeleton />
            </Flex>
          ))}
      </Flex>
    </>
  );
}

export default function loading() {
  return (
    <Flex className={classes.pageWrapper}>
      <SectionSkeleton items={6} />
      <SectionSkeleton items={9} />
    </Flex>
  );
}
