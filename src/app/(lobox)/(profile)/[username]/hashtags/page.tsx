'use client';

import React, { type FC } from 'react';
import dynamic from 'next/dynamic';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import useProfilePage from 'shared/hooks/useProfilePage';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import { SectionSkeleton } from './loading';

const UserHashtags = dynamic(() => import('./partials/sections/UserHashtags'), {
  ssr: false,
  loading: () => <SectionSkeleton />,
});
const MostPopularHashtags = dynamic(
  () => import('./partials/sections/MostPopularHashtags'),
  {
    ssr: false,
    loading: () => <SectionSkeleton />,
  }
);

const ProfileHashtags: FC = () => {
  const { isAuthUser, isAuthBusinessPage } = useProfilePage();
  const mostPopularVisibility = isAuthUser || isAuthBusinessPage;

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeHashtagTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <ProfileTabLayout>
        {mostPopularVisibility && <MostPopularHashtags />}
        <UserHashtags />
      </ProfileTabLayout>
    </PermissionsGate>
  );
};

export default ProfileHashtags;
