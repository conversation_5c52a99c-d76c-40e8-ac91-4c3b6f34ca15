import cnj from 'shared/uikit/utils/cnj';
import Section from 'shared/components/molecules/Section';
import { routeNames } from 'shared/utils/constants/routeNames';
import type { EmptyProps } from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import type { HashtagType } from '@shared/types/hashtag';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import HashtagsList from './HashtagsList.component';
import classes from './HashtagSection.module.scss';

interface HashtagSectionProps {
  className?: string;
  title: string;
  list?: Array<
    HashtagType & {
      onSuccessFollow?: (...arg: any[]) => void;
      onSuccessUnfollow?: (...arg: any[]) => void;
    }
  >;
  isLoading?: boolean;
  isSearchEmpty?: boolean;
  emptyProps?: EmptyProps;
}

const HashtagSection = ({
  className,
  title,
  isLoading,
  list,
  isSearchEmpty,
  emptyProps,
}: HashtagSectionProps): JSX.Element => {
  const navigateToSearchPage = useNavigateSearchPage();
  const onHashtagClick = (hashtag: string) => {
    navigateToSearchPage({
      pathname: routeNames.searchPosts,
      hashtags: [hashtag],
    });
  };

  return (
    <Section
      title={title}
      className={cnj(classes.sectionModified, className)}
      contentClassName={classes.sectionContent}
    >
      <HashtagsList
        isLoading={isLoading}
        isSearchEmpty={isSearchEmpty}
        onHashtagClick={onHashtagClick}
        list={list}
        emptyProps={emptyProps}
        title={title}
      />
    </Section>
  );
};

export default HashtagSection;
