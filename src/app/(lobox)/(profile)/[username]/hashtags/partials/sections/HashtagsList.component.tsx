import type { FC } from 'react';
import { placeholderData } from 'shared/constants/enums';
import type { EmptyProps } from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import HashtagItem from 'shared/components/molecules/HashtagItem';
import type { HashtagType } from '@shared/types/hashtag';
import Flex from 'shared/uikit/Flex';
import classes from './HashtagsList.module.scss';

type HashtagsListProps = {
  isLoading?: boolean;
  isSearchEmpty?: boolean;
  emptyProps?: EmptyProps;
  title: string;
  onHashtagClick: (hashtag: string) => void;
  list?: Array<
    HashtagType & {
      onSuccessFollow?: (...arg: any[]) => void;
      onSuccessUnfollow?: (...arg: any[]) => void;
    }
  >;
};

const HashtagsList: FC<HashtagsListProps> = ({
  isSearchEmpty,
  list,
  isLoading,
  title,
  onHashtagClick,
  emptyProps,
}) => {
  if (isLoading) {
    return (
      <Flex className={classes.wrapper}>
        {placeholderData(title).map(({ id }) => (
          <HashtagItem
            key={`hashtag-item-skeleton-${id}`}
            hashtag={id}
            hadFollowed
            usageCount=""
            isLoading
            hashtagClass={classes.hashtagItem}
          />
        ))}
      </Flex>
    );
  }
  if (isSearchEmpty) {
    return <EmptySectionInModules {...emptyProps} />;
  }
  return (
    <Flex className={classes.wrapper}>
      {list?.map(
        ({ id, follow, onSuccessFollow, onSuccessUnfollow, usageCount }) => (
          <HashtagItem
            key={id}
            hashtag={id}
            hadFollowed={Boolean(follow)}
            usageCount={usageCount || ''}
            showFollowAction
            isLoading={Boolean(isLoading)}
            followHashtagOnSuccess={onSuccessFollow}
            unfollowHashtagOnSuccess={onSuccessUnfollow}
            onHashtagClick={onHashtagClick}
            hashtagClass={classes.hashtagItem}
          />
        )
      )}
    </Flex>
  );
};

export default HashtagsList;
