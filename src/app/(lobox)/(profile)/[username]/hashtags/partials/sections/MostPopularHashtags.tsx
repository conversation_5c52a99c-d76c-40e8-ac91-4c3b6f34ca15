import React from 'react';
import { getMostPopularHashtags } from 'shared/utils/api/postSearch';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import useUpdateQueryData from 'shared/utils/hooks/useUpdateQueryData';
import type { HashtagType } from '@shared/types/hashtag';
import type { ListResponse } from 'shared/types/response';
import HashtagSection from './HashtagSection';
import useHashtagKeys from './useHashtagKeys';

const MostPopularHashtags = (): JSX.Element => {
  const { t } = useTranslation();
  const { userHashtagsKey, mostPopularHashtagsKey } = useHashtagKeys();

  const { data: { content = [] } = {}, isLoading } = useReactQuery<
    ListResponse<HashtagType>
  >({
    action: {
      spreadParams: true,
      apiFunc: getMostPopularHashtags,
      key: mostPopularHashtagsKey,
      params: {
        size: 9,
      },
    },
  });
  const { replace } = useUpdateQueryData(mostPopularHashtagsKey);
  const { add, remove } = useUpdateInfinityData(userHashtagsKey);

  const success = (item: any, follow: boolean) => () => {
    const followersCounter = Number(item.followersCounter);
    const newItem = {
      ...item,
      follow,
      followersCounter: follow ? followersCounter + 1 : followersCounter - 1,
    };

    replace(newItem);

    if (follow) {
      add(newItem);
    } else {
      remove(newItem.id);
    }
  };

  return (
    <HashtagSection
      title={t('popular_hashtags')}
      isLoading={isLoading}
      list={content?.map((item) => ({
        ...item,
        onSuccessFollow: success(item, true),
        onSuccessUnfollow: success(item, false),
      }))}
      emptyProps={{
        title: t('no_popular_hashtags'),
        text: t('there_are_no_popular_hashtags'),
      }}
      isSearchEmpty={!content?.length}
    />
  );
};

export default MostPopularHashtags;
