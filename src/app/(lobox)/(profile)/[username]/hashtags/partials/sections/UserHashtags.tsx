import React, { type FC } from 'react';
import { getUserHashtags } from 'shared/utils/api/network/hashtag';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import useUpdateQueryData from 'shared/utils/hooks/useUpdateQueryData';
import useScrollReachEnd from 'shared/utils/hooks/useScrollReachEnd';
import useProfilePage from 'shared/hooks/useProfilePage';
import { PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import type { HashtagType } from '@shared/types/hashtag';
import HashtagSection from './HashtagSection';
import useHashtagKeys from './useHashtagKeys';

const UserHashtags: FC = () => {
  const { isAuthUser, isAuthBusinessPage, objectDetail } = useProfilePage();
  const isMyProfile = isAuthUser || isAuthBusinessPage;
  const { t } = useTranslation();
  const { userHashtagsKey, mostPopularHashtagsKey } = useHashtagKeys();
  const {
    data = [],
    fetchNextPage,
    isLoading,
    isFetching,
  } = useInfiniteQuery<HashtagType>(userHashtagsKey, {
    func: getUserHashtags,
    extraProps: {
      userId: objectDetail?.id,
    },
  });

  const { replace: replaceMostPopular } = useUpdateQueryData(
    mostPopularHashtagsKey
  );
  const { remove, replace: userReplace } =
    useUpdateInfinityData(userHashtagsKey);

  const handleSuccessFollow = (item: any) => () => {
    const followersCounter = Number(item.followersCounter);
    const newItem = {
      ...item,
      follow: true,
      followersCounter: followersCounter + 1,
    };

    if (!isMyProfile) {
      userReplace(newItem);
    }
  };

  const handleSuccessUnfollow = (item: any) => () => {
    const followersCounter = Number(item.followersCounter);
    const newItem = {
      ...item,
      follow: false,
      followersCounter: followersCounter - 1,
    };

    if (isMyProfile) {
      replaceMostPopular(newItem);
      remove(item.id);
    } else {
      userReplace(newItem);
    }
  };

  useScrollReachEnd({
    scrollEl: PROFILE_SCROLL_WRAPPER,
    callback: fetchNextPage,
  });
  if (!isFetching && !isLoading && !data?.length) return null;
  return (
    <HashtagSection
      isLoading={isLoading}
      title={
        isMyProfile
          ? t('followed_by_y')
          : `${t('followed_by')} ${
              objectDetail?.name || objectDetail?.fullName
            }`
      }
      list={data.map((item) => ({
        ...item,
        onSuccessFollow: handleSuccessFollow(item),
        onSuccessUnfollow: handleSuccessUnfollow(item),
      }))}
      emptyProps={{
        title: t('no_hashtag_followed'),
        text: t('you_are_not_following_any_hashtag'),
      }}
    />
  );
};

export default UserHashtags;
