import useParams from 'shared/utils/hooks/useParams';
import QueryKeys from 'shared/utils/constants/queryKeys';

type UseHashtagKeys = {
  mostPopularHashtagsKey: Array<string>;
  userHashtagsKey: Array<string>;
};

const useHashtagKeys = (): UseHashtagKeys => {
  const { username } = useParams<{ username: string }>();
  const mostPopularHashtagsKey = [QueryKeys.popularHashtags, username];
  const userHashtagsKey = [QueryKeys.userHashtags, username];

  return {
    mostPopularHashtagsKey,
    userHashtagsKey,
  };
};

export default useHashtagKeys;
