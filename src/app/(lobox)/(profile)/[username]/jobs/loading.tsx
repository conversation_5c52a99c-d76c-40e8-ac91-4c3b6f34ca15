'use client';

import React from 'react';
import Section from 'shared/components/molecules/Section';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import Flex from 'shared/uikit/Flex/Flex.component';
import classes from './loading.module.scss';

export default function loading() {
  return (
    <Section title={(<Skeleton style={{ height: 24, width: 170 }} />) as any}>
      <Flex className={classes.wrapper}>
        <Skeleton className={classes.skeleton} />
        <Skeleton className={classes.skeleton} />
      </Flex>
    </Section>
  );
}
