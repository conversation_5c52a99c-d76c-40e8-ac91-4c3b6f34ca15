'use client';

import React from 'react';
import JobsSectionLayoutWithJobsList from 'shared/components/Organism/JobsSectionLayoutWithJobsList';
import jobsApi from 'shared/utils/api/jobs';
import { jobStatusKeys } from 'shared/utils/constants/enums';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import useProfilePage from 'shared/hooks/useProfilePage';
import classes from './loading.module.scss';

const ProfileJobs = (): JSX.Element => {
  const { t } = useTranslation();
  const navigateSearchPage = useNavigateSearchPage();
  const { isPage, getObjectProp } = useProfilePage();
  const pageId = getObjectProp({ pageKey: 'id' });

  const navigateToSearchPage =
    (searchGroupType: keyof typeof searchGroupTypes) => () => {
      navigateSearchPage({ searchGroupType });
    };

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeJobsTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      {isPage ? (
        <JobsSectionLayoutWithJobsList
          childrenClassName={classes.childrenInLoading}
          firstTitle={t('created_jobs')}
          onClick={() =>
            navigateSearchPage({
              [searchFilterQueryParams.searchGroupType]:
                searchGroupTypes.CREATED_BY,
              pageIds: pageId,
            })
          }
          jobGroupStatus={jobStatusKeys.all}
          queryKey={QueryKeys.getPagePublishedJobs}
          apiFunc={jobsApi.getPagePublishedJobs}
          searchGroupType={searchGroupTypes.CREATED_BY}
          params={{ pageId }}
          emptyProps={{
            title: t('no_published_jobs'),
            text: t('no_items_to_display'),
            buttonProps: {
              title: t('discover_jobs'),
              onClick: navigateToSearchPage(searchGroupTypes.CREATED_BY),
            },
            classNames: {
              container: classes.emptyContainer,
            },
            doNotShowSectionTitleIFEmpty: true,
          }}
        />
      ) : (
        <>
          <JobsSectionLayoutWithJobsList
            childrenClassName={classes.childrenInLoading}
            title={t('applied_b_y')}
            onClick={() =>
              navigateSearchPage({
                searchGroupType: searchGroupTypes.APPLIED,
                jobGroupStatus: jobStatusKeys.open,
              })
            }
            jobGroupStatus={jobStatusKeys.open}
            queryKey={QueryKeys.activeAppliedJobs}
            apiFunc={jobsApi.getJobsApplicationList}
            searchGroupType={searchGroupTypes.APPLIED}
            emptyProps={{
              title: t('no_applied_jobs'),
              text: t('no_items_to_display'),
              buttonProps: {
                title: t('discover_jobs'),
                onClick: navigateToSearchPage(searchGroupTypes.ALL),
              },
              doNotShowSectionIfEmpty: true,
            }}
          />
          <JobsSectionLayoutWithJobsList
            childrenClassName={classes.childrenInLoading}
            title={t('top_suggestions')}
            onClick={navigateToSearchPage(searchGroupTypes.TOP_SUGGESTION)}
            queryKey={QueryKeys.topSuggestionJobs}
            apiFunc={jobsApi.getJobsTopSuggestionList}
            searchGroupType={searchGroupTypes.TOP_SUGGESTION}
            emptyProps={{
              title: t('no_top_suggestions'),
              text: t('no_items_to_display'),
              buttonProps: {
                title: t('discover_jobs'),
                onClick: navigateToSearchPage(searchGroupTypes.ALL),
              },
            }}
          />
        </>
      )}
    </PermissionsGate>
  );
};

export default ProfileJobs;
