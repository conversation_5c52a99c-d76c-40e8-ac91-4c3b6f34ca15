import ProfileLayout from 'shared/components/layouts/ProfileLayout';
import type { Metadata } from 'next';
import type { PropsWithChildren } from 'react';
import { Suspense } from 'react';
import { getObjectDetail, isPageType } from 'shared/utils/api/object';
import Loading from '../../lb/[objectId]/loading';
import DataWrapper from './data-wrapper';
import { getPageMetadata, getUserMetadata } from './partials/utils/getMetadata';

interface ProfilePageProps {
  params: Promise<{ username: string }>;
}

export default async function ProfilePage({
  children,
  params: promise,
}: PropsWithChildren<ProfilePageProps>) {
  const params = await promise;
  return (
    <Suspense fallback={<Loading />}>
      <DataWrapper params={params}>
        <ProfileLayout>{children}</ProfileLayout>
      </DataWrapper>
    </Suspense>
  );
}

export async function generateMetadata({
  params: promise,
}: ProfilePageProps): Promise<Metadata> {
  const params = await promise;
  try {
    const object = await getObjectDetail({ params });
    if (isPageType(object)) return getPageMetadata(object);
    return getUserMetadata(object);
  } catch {
    return {};
  }
}
