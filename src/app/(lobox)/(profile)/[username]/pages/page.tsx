'use client';

import React from 'react';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useProfilePage from 'shared/hooks/useProfilePage';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PagesYouManage from 'shared/components/Organism/PagesYouManage';
import { PagesSection } from './partials/sections';
import classes from './page.module.scss';

const Pages = (): JSX.Element => {
  const { t } = useTranslation();
  const { isAuthUser, getObjectProp } = useProfilePage();
  const name = getObjectProp({
    pageKey: 'title',
    userKey: 'name',
  });

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeePagesTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <ProfileTabLayout>
        {isAuthUser && <PagesYouManage isWide className={classes.yours} />}
        <PagesSection
          title={
            isAuthUser ? t('followed_by_y') : `${t('followed_by')} ${name} `
          }
        />
      </ProfileTabLayout>
    </PermissionsGate>
  );
};

export default Pages;
