import React, { useCallback, useEffect } from 'react';
import collectionToObjectByKey from 'shared/utils/toolkit/collectionToObjectByKey';
import { db } from 'shared/utils/constants/enums';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { routeNames } from 'shared/utils/constants/routeNames';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useScrollReachEnd from 'shared/utils/hooks/useScrollReachEnd';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import useGetMyFollowingPages from 'shared/hooks/api-hook/useGetMyFollowingPages';
import useProfilePage from 'shared/hooks/useProfilePage';
import FollowButton from 'shared/components/molecules/FollowButton/FollowButton';
import FollowersItem from 'shared/components/molecules/FollowersItem/FollowersItem';
import useCardMenuBuilder from 'shared/hooks/useCardMenuBuilder';
import { PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import useHistory from 'shared/utils/hooks/useHistory';
import useParams from 'shared/utils/hooks/useParams';
import useHasPermission from 'shared/hooks/useHasPermission';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import InfiniteScrollUnitSection from 'shared/components/Organism/UnitSection/InfiniteScrollUnitSection';
import type { PageType } from 'shared/types/page';

interface PagesSectionProps {
  title: string;
}

const pagesCategories = collectionToObjectByKey(db.CATEGORY_TYPES);

const PagesSection = ({ title }: PagesSectionProps): JSX.Element => {
  const { objectDetail } = useProfilePage();
  const { debounceValue } = useDebounceState('', 500);
  const { username } = useParams<{ username: string }>();
  const PageSectionDataKey = [
    QueryKeys.getMyFollowingPages,
    username,
    debounceValue,
  ];
  const { t } = useTranslation();
  const history = useHistory();
  const userId = objectDetail?.id;
  const hasAccess = useHasPermission([SCOPES.canFollowUnFollow]);
  const {
    data,
    fetchNextPage,
    isLoading,
    refetch,
    isFetchingNextPage,
    hasNextPage,
  } = useGetMyFollowingPages(
    userId as string,
    PageSectionDataKey,
    debounceValue
  );
  const followingList = data?.filter(({ hideIt }) => !hideIt) || [];
  const { replace, remove } = useUpdateInfinityData(PageSectionDataKey);
  useEffect(() => {
    refetch();
  }, [debounceValue, refetch]);
  const success = (item: PageType, follow: boolean) => () => {
    const newItem = { ...item, follow };
    replace(newItem);
  };

  const handleBlock = (item: PageType, isBlocked: boolean) => () => {
    const newItem = { ...item, isBlocked };
    replace(newItem);
  };
  const handleRemove = (id: string) => () => {
    remove(id);
  };

  useScrollReachEnd({
    scrollEl: PROFILE_SCROLL_WRAPPER,
    callback: fetchNextPage,
  });
  const menuBuilder = useCardMenuBuilder({
    isPage: true,
    queryKeyToUpdate: PageSectionDataKey as string[],
  });

  const handleDiscoverPages = useCallback(() => {
    history.push(routeNames.pagesDiscover);
  }, [history]);

  return !followingList?.length && !isLoading && !debounceValue ? null : (
    <InfiniteScrollUnitSection
      hasNextPage={hasNextPage}
      avatarProps={{
        isCompany: true,
      }}
      showDefaultCard
      sectionProps={{
        title,
      }}
      isLoading={isLoading}
      fetchNextPage={fetchNextPage}
      isFetchingNextPage={isFetchingNextPage}
      list={followingList?.map((item) => {
        const menu = menuBuilder({
          ...item,
          unfollowSuccess: success(item, false),
          block: handleBlock(item, true),
        });

        return {
          id: item?.id,
          image: item?.croppedImageUrl,
          title: item.title,
          subtitle: `@${item.username}`,
          to: `/${item.username}`,
          postsCounter: item.postsCounter,
          croppedHeaderImageLink: item.croppedHeaderImageLink,
          locationTitle: item.locationTitle,
          description: t(pagesCategories[item.category]?.label),
          followers: item.followersCounter,
          following: item.followingsCounter,
          actions: hasAccess ? (
            item.follow ? (
              <FollowersItem
                object={{
                  isPage: true,
                  id: item.id,
                  name: item.title,
                  username: item.username as string,
                  croppedImageUrl: item.croppedImageUrl,
                  fullName: item.title,
                }}
                onSuccess={success(item, false)}
              />
            ) : (
              <FollowButton
                object={{
                  id: item.id,
                  isPage: true,
                  username: item.username,
                }}
                onSuccess={success(item, true)}
              />
            )
          ) : undefined,
          moreList: menu,
          isBlocked: item.isBlocked,
          undoBlock: handleBlock(item, false),
          remove: handleRemove(item.id),
          username: item.username as string,
        };
      })}
      emptyState={
        <EmptySectionInModules
          title={t('no_followed_pages')}
          text={t('you_are_not_following_any_page')}
          buttonProps={{
            title: t('discover_pages'),
            onClick: handleDiscoverPages,
          }}
        />
      }
    />
  );
};

export default PagesSection;
