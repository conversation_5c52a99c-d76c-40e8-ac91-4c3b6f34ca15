import React from 'react';
import Carousel from 'shared/uikit/Carousel';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { routeNames } from 'shared/utils/constants/routeNames';
import { carouselPlaceholderData } from 'shared/constants/enums';
import useGetPopularPageCategories from 'shared/hooks/api-hook/useGetPopularPageCategories';
import SectionTitle from 'shared/components/Organism/Objects/Common/SectionTitle';
import { PageCategoryItem } from 'shared/components/Organism/PageCategoryItem';
import classes from './TopSection.module.scss';

const TopSection: React.FC = () => {
  const { t } = useTranslation();
  const { data: { content = [] } = {}, isLoading } =
    useGetPopularPageCategories();

  return (
    <>
      <SectionTitle title={t('pp_category')} />
      <Carousel
        visibleHead={false}
        className={classes.topSectionCarousel}
        moveWalkDistance={208}
      >
        {(isLoading ? carouselPlaceholderData : content).map(
          ({ id, title, imageUrl }) => (
            <PageCategoryItem
              key={id}
              to={routeNames.pagesCategory.makeRoute(id)}
              title={title}
              cover={imageUrl}
              className={classes.item}
              isLoading={isLoading}
            />
          )
        )}
      </Carousel>
    </>
  );
};

export default TopSection;
