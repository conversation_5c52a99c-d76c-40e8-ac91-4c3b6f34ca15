import { getObjectDetail, isPageType } from 'shared/utils/api/object';
import appEnvironment from 'shared/utils/constants/env';
import type { PageType } from 'shared/types/page';
import { getPartialMetadata } from '../utils/getMetadata';

// internal components:

/**
 * renders JSON+LD Schemas for PageType objects
 */
interface PageProfileSEOProps {
  page: PageType;
}
const PageProfileSEO: React.FC<PageProfileSEOProps> = async ({ page }) => {
  const {
    title,
    lastModifiedDate,
    createdDate,
    description: htmlDescription,
  } = page;
  const { pathname, image } = getPartialMetadata(page);
  const description = htmlDescription?.replace(/<[^>]+>/g, '') ?? '';
  const datePublished = createdDate ?? new Date().toISOString();
  const dateModified = lastModifiedDate ?? new Date().toISOString();

  let author = title;
  if (page.ownerId) {
    try {
      const ownerObject = await getObjectDetail({
        params: { objectId: page.ownerId },
      });
      if (ownerObject && !isPageType(ownerObject)) {
        author = ownerObject.fullName;
      }
    } catch {
      /** no action required */
    }
  }

  const copyrightYear = new Date().getFullYear();

  // schema.org in JSONLD format
  // https://developers.google.com/search/docs/guides/intro-structured-data
  // You can fill out the 'author', 'creator' with more data or another type (e.g. 'Organization')
  // Structured Data Testing Tool >>
  // https://search.google.com/structured-data/testing-tool

  const schemaOrgWebPage = {
    '@context': 'http://schema.org',
    '@type': 'WebPage',
    url: pathname,
    headline: description,
    inLanguage: 'siteLanguage',
    mainEntityOfPage: pathname,
    description,
    name: title,
    author: {
      '@type': 'Person',
      name: author,
    },
    copyrightHolder: {
      '@type': 'Person',
      name: author,
    },
    copyrightYear,
    creator: {
      '@type': 'Person',
      name: author,
    },
    publisher: {
      '@type': 'Person',
      name: author,
    },
    datePublished,
    dateModified,
    image: {
      '@type': 'ImageObject',
      url: `${image}`,
    },
  };

  // Initial breadcrumb list
  const breadcrumb = {
    '@context': 'http://schema.org',
    '@type': 'BreadcrumbList',
    description: 'Breadcrumbs list',
    name: 'Breadcrumbs',
    itemListElement: [
      {
        '@type': 'ListItem',
        item: {
          '@id': appEnvironment.baseUrl,
          name: 'Homepage',
        },
        position: 1,
      },
      {
        '@type': 'ListItem',
        item: {
          '@id': pathname,
          name: title,
        },
        position: 2,
      },
    ],
  };

  return (
    <>
      <script type="application/ld+json">{JSON.stringify(breadcrumb)}</script>
      <script type="application/ld+json">
        {JSON.stringify(schemaOrgWebPage)}
      </script>
    </>
  );
};

export default PageProfileSEO;
