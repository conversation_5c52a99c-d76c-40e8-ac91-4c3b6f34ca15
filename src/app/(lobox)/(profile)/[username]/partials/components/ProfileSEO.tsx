import { getObjectDetail, isPageType } from 'shared/utils/api/object';
import PageProfileSEO from './PageProfileSEO.json-ld';
import UserProfileSEO from './UserProfileSEO.json-ld';

interface ProfileSEOProps {
  params: {
    username: string;
  };
}

const ProfileSEO: React.FC<ProfileSEOProps> = async ({ params }) => {
  try {
    const object = await getObjectDetail({ params });
    if (!object) return null;
    return isPageType(object) ? (
      <PageProfileSEO page={object} />
    ) : (
      <UserProfileSEO user={object} />
    );
  } catch {
    return null
  }
};

export default ProfileSEO;
