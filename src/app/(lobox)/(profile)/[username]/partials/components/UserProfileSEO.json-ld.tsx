import appEnvironment from 'shared/utils/constants/env';
import type { UserType } from 'shared/types/user';
import { getPartialMetadata } from '../utils/getMetadata';

/**
 * renders JSON+LD Schemas for UserType objects
 */
interface UserProfileSEOProps {
  user: UserType;
}
const UserProfileSEO: React.FC<UserProfileSEOProps> = async ({ user }) => {
  const {
    fullName: title,
    lastModifiedDate,
    createdDate,
    publicSetting,
    occupation,
  } = user;
  const { pathname, image } = getPartialMetadata(user);
  const description = publicSetting?.visibleJobTitle
    ? occupation.label
    : undefined;
  const datePublished = createdDate ?? new Date().toISOString();
  const dateModified = lastModifiedDate ?? new Date().toISOString();

  const author = title;

  const copyrightYear = new Date().getFullYear();

  // schema.org in JSONLD format
  // https://developers.google.com/search/docs/guides/intro-structured-data
  // You can fill out the 'author', 'creator' with more data or another type (e.g. 'Organization')
  // Structured Data Testing Tool >>
  // https://search.google.com/structured-data/testing-tool

  const schemaOrgWebPage = {
    '@context': 'http://schema.org',
    '@type': 'WebPage',
    url: pathname,
    headline: description,
    inLanguage: 'siteLanguage',
    mainEntityOfPage: pathname,
    description,
    name: title,
    author: {
      '@type': 'Person',
      name: author,
    },
    copyrightHolder: {
      '@type': 'Person',
      name: author,
    },
    copyrightYear,
    creator: {
      '@type': 'Person',
      name: author,
    },
    publisher: {
      '@type': 'Person',
      name: author,
    },
    datePublished,
    dateModified,
    image: {
      '@type': 'ImageObject',
      url: `${image}`,
    },
  };

  // Initial breadcrumb list
  const breadcrumb = {
    '@context': 'http://schema.org',
    '@type': 'BreadcrumbList',
    description: 'Breadcrumbs list',
    name: 'Breadcrumbs',
    itemListElement: [
      {
        '@type': 'ListItem',
        item: {
          '@id': appEnvironment.baseUrl,
          name: 'Homepage',
        },
        position: 1,
      },
      {
        '@type': 'ListItem',
        item: {
          '@id': pathname,
          name: title,
        },
        position: 2,
      },
    ],
  };

  return (
    <>
      <script type="application/ld+json">{JSON.stringify(breadcrumb)}</script>
      <script type="application/ld+json">
        {JSON.stringify(schemaOrgWebPage)}
      </script>
    </>
  );
};

export default UserProfileSEO;
