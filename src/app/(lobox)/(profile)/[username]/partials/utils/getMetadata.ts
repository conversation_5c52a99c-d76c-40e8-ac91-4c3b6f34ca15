import type { Metadata } from 'next';
import type { PageType } from 'shared/types/page';
import type { UserType } from '@shared/types/user';
import type { ImageResolutionType } from 'shared/types/image';
import appEnvironment from 'shared/utils/constants/env';
import textTruncate from 'shared/uikit/utils/textTruncate';
import { routeNames } from 'shared/utils/constants/routeNames';
import applyResolutionToImageSrc from 'shared/utils/toolkit/applyResolutionToImageSrc';

export function getPageMetadata(page: PageType): Metadata {
  const { title, description: htmlDescription, username } = page;
  const { image, pathname } = getPartialMetadata(page, 'medium');

  const description = htmlDescription?.replace(/<[^>]+>/g, '') ?? '';
  const metaDescription = textTruncate(description, 160);
  const metaTitle = title?.slice(0, 70);

  return {
    title: {
      default: metaTitle,
      template: '%s | Lobox',
    },
    description: metaDescription,
    alternates: {
      canonical: pathname,
    },
    openGraph: {
      title,
      description,
      images: image,
      url: pathname,
      type: 'website',
    },
    robots: {
      index: true,
      follow: true,
      nocache: true,
    },
    twitter: {
      card: 'summary_large_image',
      site: `${appEnvironment.baseUrl}/${username}`,
      title,
      description,
      images: image,
    },
  };
}

export function getUserMetadata(user: UserType): Metadata {
  const {
    surname,
    name,
    fullName,
    publicSetting,
    occupation,
    username,
    deactivated,
    privateProfile,
  } = user;
  const { image, pathname } = getPartialMetadata(user);

  const description = publicSetting?.visibleJobTitle
    ? occupation.label
    : undefined;
  const metaDescription = description?.slice(0, 160);
  const metaTitle = fullName?.slice(0, 70);

  if (privateProfile) {
    return {
      title: {
        default: metaTitle,
        template: '%s | Lobox',
      },
      alternates: {
        canonical: pathname,
      },
      description: 'Private Profile',
      robots: {
        index: false,
        follow: false,
        nocache: true,
      },
    };
  }

  if (deactivated) {
    return {
      title: {
        default: 'deactivated',
        template: '%s | Lobox',
      },
      robots: {
        index: false,
        follow: false,
        nocache: true,
      },
    };
  }

  return {
    title: {
      default: metaTitle,
      template: '%s | Lobox',
    },
    alternates: {
      canonical: pathname,
    },
    description: metaDescription,
    openGraph: {
      type: 'profile',
      firstName: name,
      lastName: surname,
      username,
      title: fullName,
      description,
      url: pathname,
      images: image,
    },
    robots: {
      index: true,
      follow: true,
      nocache: true,
    },
    twitter: {
      card: 'summary',
      site: `${appEnvironment.baseUrl}/${username}`,
      title: fullName,
      description,
      images: image,
    },
  };
}

export function getPartialMetadata(
  { id, croppedImageUrl }: UserType | PageType,
  imgResolution?: ImageResolutionType
) {
  const image = croppedImageUrl
    ? applyResolutionToImageSrc(croppedImageUrl, imgResolution)
    : undefined;

  const pathname =
    appEnvironment.baseUrl + routeNames.middleScreen.makeRoute(id);
  return { image, pathname };
}
