'use client';

import React, { type FC } from 'react';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import { getAssociatedPeopleWithPage } from 'shared/utils/api/profile';
import type { PeopleType } from 'shared/types/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { routeNames } from 'shared/utils/constants/routeNames';
import useHistory from 'shared/utils/hooks/useHistory';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import PeopleCard from 'shared/components/Organism/PeopleCard';
import SearchAllSection from 'shared/components/Organism/SearchAllSection';
import type { BlockGen } from 'shared/types';
import { useObjectClicks } from 'shared/hooks/useObjectClicks';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useProfilePage from '../../../../../shared/hooks/useProfilePage';

const People: FC = () => {
  const { t } = useTranslation();
  const { getObjectProp } = useProfilePage();
  const { handleTagClick } = useObjectClicks();
  const history = useHistory();

  const pageId = getObjectProp({ pageKey: 'id' });
  const {
    data: searchPeopleData,
    isLoading: searchPeopleIsLoading,
    totalElements: searchPeopleTotal,
    isEmpty,
  } = useInfiniteQuery<BlockGen<PeopleType>>(
    [QueryKeys.getAssociatedPeopleWithPage, pageId],
    {
      extraProps: {
        pageId,
      },
      func: getAssociatedPeopleWithPage,
      size: 51,
    }
  );

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeePeopleTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <ProfileTabLayout>
        <SearchAllSection
          title={t('associated')}
          totalElements={searchPeopleTotal}
          showAllRouteName={`${routeNames.searchPeople}?searchGroupType=${searchGroupTypes.ALL}&${searchFilterQueryParams.relatedPageIds}=${pageId}`}
          routeName={(item) => `/${item.username}`}
          scopes={[SCOPES.canSeePeopleScreen]}
          renderItem={(item) => (
            <PeopleCard
              people={item}
              queryKey={[QueryKeys.pageEmployees]}
              onSelectHandler={() =>
                handleTagClick(item?.username, item?.id, 'people', {
                  searchGroupType: searchGroupTypes.ALL,
                  [searchFilterQueryParams.relatedPageIds]: pageId,
                })
              }
            />
          )}
          visibleShowAll={!isEmpty}
          isLoading={searchPeopleIsLoading}
          data={searchPeopleData}
          useVirtual
          emptyComponent={() => (
            <EmptySectionInModules
              {...{
                title: t('no_associated_people'),
                text: t('yr_associated_p_t_t_p'),
                buttonProps: {
                  title: t('all_people'),
                  onClick: () => history.push(routeNames.searchPeople),
                },
                isFullParent: false,
              }}
            />
          )}
        />
      </ProfileTabLayout>
    </PermissionsGate>
  );
};

export default People;
