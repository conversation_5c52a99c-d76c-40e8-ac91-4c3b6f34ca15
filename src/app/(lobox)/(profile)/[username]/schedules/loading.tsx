'use client';

import React from 'react';
import Flex from 'shared/uikit/Flex/Flex.component';
import Skeleton from 'shared/uikit/Skeleton';
import Section from 'shared/components/molecules/Section';
import classes from './loading.module.scss';

const skeletons = Array.from({ length: 6 }, (_, i) => `${i}_key`);

export default function loading() {
  return (
    <Section title={(<Skeleton style={{ height: 24, width: 170 }} />) as any}>
      <Flex className={classes.list}>
        {skeletons?.map((item) => (
          <Flex className={classes.item} key={item}>
            <Skeleton className={classes.skeleton} />
          </Flex>
        ))}
      </Flex>
    </Section>
  );
}
