import React from 'react';
import schedulesApi from 'shared/utils/api/schedules';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import ScheduleEventsSectionLayout from '@app/schedules/partials/ScheduleEventsSectionLayout/ScheduleEventsSectionLayout.component';
import Svg from 'shared/svg/SvgExperience';
import EmptyState from 'shared/components/Organism/EmptyState/EmptyState';
import useMedia from 'shared/uikit/utils/useMedia';
import type ISchedulesEvent from 'shared/utils/ISchedulesEvent';
import type SchedulesEventTypes from 'shared/utils/SchedulesEventTypes';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import getSchedulesSectionsQueryKey from '@/shared/utils/getSchedulesSectionsQueryKey';
import classes from './ScheduleSection.module.scss';

interface ScheduleSectionProps {
  title: string;
  schedulesEventType: SchedulesEventTypes;
  action: (event: ISchedulesEvent) => React.ReactElement;
  emptyProps: {
    message: string;
    caption: string;
    action: {
      title: string;
    };
  };
}

const ScheduleSection: React.FC<ScheduleSectionProps> = ({
  title,
  schedulesEventType,
  action,
  emptyProps = {},
}): JSX.Element => {
  const { upComingQueryKey } = getSchedulesSectionsQueryKey(schedulesEventType);
  const { isMoreThanTablet } = useMedia();
  const { openCreateEventWithDate } = useSchedulesCalendar();

  const { data, isLoading } = useInfiniteQuery<ISchedulesEvent>(
    upComingQueryKey,
    {
      func: schedulesApi.getCalendarUpcoming,
      size: 6,
      extraProps: {
        types: schedulesEventType,
      },
    }
  );

  const isEmpty = !data?.length && !isLoading;

  const onClickHandler = () => {
    openCreateEventWithDate(undefined, {
      schedulesEventType,
    });
  };

  return (
    <ScheduleEventsSectionLayout
      title={title}
      data={data}
      isLoading={isLoading}
      action={action}
      titleClassName={classes.titleClassName}
      childrenWrapClassName={!isEmpty && classes.childrenWrapClassName}
      emptyComponent={() => (
        <EmptyState
          image={
            <Svg {...(isMoreThanTablet ? { width: 380, height: 226 } : {})} />
          }
          className={classes.emptyClassName}
          contentClassName={classes.contentClassName}
          caption={emptyProps.caption}
          captionProps={{
            className: 'responsive-margin-top',
          }}
          messageProps={{
            className: 'responsive-margin-top',
          }}
          message={emptyProps.message}
          action={{
            ...emptyProps.action,
            onClick: onClickHandler,
          }}
          actionProps={{
            schema: 'primary-blue',
            className: classes.action,
          }}
        />
      )}
    />
  );
};

export default ScheduleSection;
