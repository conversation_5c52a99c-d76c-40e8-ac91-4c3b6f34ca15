'use client';

import appEnvironment from 'shared/utils/constants/env';
import { redirectUrl } from 'shared/utils/toolkit/redirection';
import removePageCookies from 'shared/utils/toolkit/removePageCookies';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { verifyPageDelete } from 'shared/utils/api/page';
import { useEffect } from 'react';
import useToast from 'shared/uikit/Toast/useToast';
import useIsMounted from 'shared/hooks/useIsMounted';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import Spinner from 'shared/uikit/Spinner';

export default function page({ params }) {
  const { t } = useTranslation();
  const token = params?.token;
  const isMounted = useIsMounted();
  const { isLoading, authUser, businessPage } = useGetAppObject();
  const toast = useToast();

  const { mutate: deletion } = useReactMutation({
    apiFunc: verifyPageDelete,
  });

  const handleDeletion = () => {
    deletion(
      { token },
      {
        onSuccess: () => {
          toast({
            type: 'success',
            icon: 'check-circle',
            title: t('page_deleted'),
            message: t('y_page_deleted_successfully'),
          });
          removePageCookies();
          setTimeout(
            () =>
              redirectUrl(`${appEnvironment.baseUrl}/${authUser?.username}`),
            250
          );
        },

        onError: () => {
          toast({
            type: 'error',
            icon: 'times-circle',
            title: t('link_expired'),
            message: t('pg_delete_failed_expired'),
          });
          setTimeout(
            () =>
              redirectUrl(
                `${appEnvironment.businessAppBaseUrl}/${businessPage?.username}`
              ),
            500
          );
        },
      }
    );
  };
  useEffect(() => {
    if (!isMounted) {
      handleDeletion();
    }
  }, []);

  return isLoading && <Spinner />;
}
