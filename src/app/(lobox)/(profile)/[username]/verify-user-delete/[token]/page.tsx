'use client';

import appEnvironment from 'shared/utils/constants/env';
import { redirectUrl } from 'shared/utils/toolkit/redirection';
import useLogout from 'shared/utils/hooks/useLogoutMutation';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { verifyUserDelete } from 'shared/utils/api/user';
import { useEffect } from 'react';
import useToast from 'shared/uikit/Toast/useToast';
import useIsMounted from 'shared/hooks/useIsMounted';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import Spinner from 'shared/uikit/Spinner';

export default function page({ params }) {
  const { t } = useTranslation();
  const token = params?.token;
  const isMounted = useIsMounted();
  const { isLoading, authUser, businessPage } = useGetAppObject();
  const toast = useToast();
  const logoutHandler = useLogout();

  const { isLoading: isDeleting, mutate: deletion } = useReactMutation({
    apiFunc: verifyUserDelete,
  });

  const handleDeletion = () => {
    deletion(
      { token },
      {
        onSuccess: () => {
          toast({
            type: 'success',
            icon: 'check-circle',
            title: t('account_deleted'),
            message: t('y_acc_deleted_successfully'),
          });
          setTimeout(() => logoutHandler(), 250);
        },

        onError: () => {
          toast({
            type: 'error',
            icon: 'times-circle',
            title: t('link_expired'),
            message: t('acc_del_fai_d_t_ex_l'),
          });
          setTimeout(
            () =>
              redirectUrl(`${appEnvironment.baseUrl}/${authUser?.username}`),
            500
          );
        },
      }
    );
  };
  useEffect(() => {
    if (!isMounted) {
      handleDeletion();
    }
  }, []);

  return (isLoading || isDeleting) && <Spinner />;
}
