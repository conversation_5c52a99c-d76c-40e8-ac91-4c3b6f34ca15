'use client';

import { routeNames } from 'shared/utils/constants/routeNames';
import useGetObjectDetail from 'shared/utils/hooks/useGetObjectDetail';
import useTranslation from 'shared/utils/hooks/useTranslation';
import EmptySearchSvg from 'shared/svg/EmptySearchSvg';
import { useRouter, useSearchParams } from 'next/navigation';
import NotFound from 'shared/uikit/NotFound';
import Loading from './[username]/(about)/loading';

export default function NotFoundP() {
  const searchParams = useSearchParams();
  const objectTempId = searchParams.get('objectTempId');
  const { t } = useTranslation();
  const { isFetching, data } = useGetObjectDetail({
    objectId: objectTempId,
    enabled: !!objectTempId,
  });
  const router = useRouter();

  const onBackHandler = () => router.back();
  const onClickHomeHandler = () => router.push(routeNames.home);

  if (isFetching) return Loading;
  if (data?.username && !data?.youAreBlocked) {
    router.prefetch(`/${data.username}`);
    return router.replace(`/${data.username}`);
  }

  return (
    <NotFound
      title={t('not_found_profile_page')}
      message={t('not_fount_profile_message')}
      image={<EmptySearchSvg />}
      secondaryActionProps={{
        label: t('back'),
        onClick: onBackHandler,
      }}
      primaryActionProps={{
        label: t('go_to_home_p'),
        onClick: onClickHomeHandler,
      }}
    />
  );
}
