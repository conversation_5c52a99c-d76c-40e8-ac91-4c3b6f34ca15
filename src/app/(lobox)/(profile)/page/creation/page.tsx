'use client';

import React from 'react';
import Flex from 'shared/uikit/Flex';
import OverviewSection from 'shared/components/Organism/Objects/Page/Overview/Overview.component';
import LocationSection from 'shared/components/Organism/Objects/Page/Location/Location.component';
import ContactInfoSection from 'shared/components/Organism/Objects/Page/ContactInfo/ContactInfo.component';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import ProfileTabLayout from 'shared/uikit/Layout/Profile/ProfileTabLayout';
import classes from './page.module.scss';

const PageCreation = () => {
  React.useLayoutEffect(() => {
    openMultiStepForm({ formName: 'createPageForm' });
  }, []);

  return (
    <ProfileTabLayout
      showSideOnDesktop={false}
      mainComponent={
        <Flex>
          <OverviewSection />
          <LocationSection />
        </Flex>
      }
      sideComponent={
        <Flex className={classes.sticky}>
          <ContactInfoSection />
        </Flex>
      }
    />
  );
};

export default PageCreation;
