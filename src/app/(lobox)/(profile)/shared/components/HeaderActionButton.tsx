import React from 'react';
import Button from 'shared/uikit/Button';
import useMedia from 'shared/uikit/utils/useMedia';
import IconButton from 'shared/uikit/Button/IconButton';
import classes from './HeaderActionButton.module.scss';

interface HeaderActionButtonProps {
  label?: string;
  icon: string;
  onClick?: Function;
}
const HeaderActionButton = ({
  onClick,
  icon,
  label,
}: HeaderActionButtonProps): JSX.Element => {
  const { isMoreThanTablet } = useMedia();

  return !isMoreThanTablet ? (
    <IconButton
      onClick={onClick as any}
      className={classes.headerIconButton}
      name={icon}
      type="far"
    />
  ) : (
    <Button
      schema="ghost-brand"
      className={classes.headerActionButton}
      labelClassName={classes.actionLabelStyle}
      onClick={onClick as any}
      leftIcon={icon}
      leftSize={15}
      labelFont="700"
      leftColor="brand"
      label={label}
    />
  );
};

export default HeaderActionButton;
