'use client';

import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex/index';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import Skeleton from '@shared/uikit/Skeleton';
import ComingSoon from '@shared/svg/ComingSoon';
import classes from './page.module.scss';

export interface JobsComingProps {
  className?: string;
}

const ComingLoading: React.FC<JobsComingProps> = ({ className }) => (
  <>
    <ModalHeaderSimple
      visibleHeaderDivider={false}
      hideBack
      noCloseButton
      className={classes.mobileHeader}
      title={<Skeleton className={classes.header} />}
    />

    <Flex className={cnj(classes.jobsComingComponentRoot, className)}>
      <EmptySectionInModules
        title={<Skeleton className={classes.title} />}
        text={<Skeleton className={classes.text} />}
        image={<ComingSoon />}
        classNames={{ container: classes.container }}
      />
    </Flex>
  </>
);

export default ComingLoading;
