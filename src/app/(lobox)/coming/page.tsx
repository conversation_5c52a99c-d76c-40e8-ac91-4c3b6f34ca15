'use client';

import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex/index';
import ComingSoon from 'shared/svg/ComingSoon';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import { useRouter } from 'next/navigation';
import FullHeightLayout from '@shared/components/layouts/FullHeightLayout';
import classes from './page.module.scss';

export interface JobsComingProps {
  className?: string;
  title: string;
}

const ComingPage: React.FC<JobsComingProps> = ({ className, title }) => {
  const { t } = useTranslation();
  const router = useRouter();

  const onBackHandler = () => {
    router.back();
  };

  return (
    <>
      <ModalHeaderSimple
        visibleHeaderDivider
        className={classes.mobileHeader}
        title={t(title)}
        backButtonProps={{ onClick: onBackHandler }}
      />

      <FullHeightLayout className={className}>
        <EmptySectionInModules
          title={t('coming_3dot')}
          text={t('w_r_w_o_it')}
          image={<ComingSoon />}
        />
      </FullHeightLayout>
    </>
  );
};

export default ComingPage;
