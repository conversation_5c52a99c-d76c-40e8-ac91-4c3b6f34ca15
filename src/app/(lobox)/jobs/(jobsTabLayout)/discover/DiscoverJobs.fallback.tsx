import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import classes from './DiscoverJobs.fallback.module.scss';

export interface DiscoverJobsFallbackProps {
  className?: string;
}

const DiscoverJobsFallback: React.FC<DiscoverJobsFallbackProps> = ({
  className,
}) => (
  <Flex className={cnj(classes.discoverJobsFallbackRoot, className)}>
    Skeleton
  </Flex>
);

export default DiscoverJobsFallback;
