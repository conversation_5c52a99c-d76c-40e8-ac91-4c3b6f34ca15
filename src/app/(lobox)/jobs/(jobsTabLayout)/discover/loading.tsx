'use client';

import useTranslation from 'shared/utils/hooks/useTranslation';
import Flex from 'shared/uikit/Flex/Flex.component';
import Carousel from 'shared/uikit/Carousel';
import { carouselPlaceholderData } from 'shared/constants/enums';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import React from 'react';
import JobSectionSkeleton from '@app/jobs/(jobsTabLayout)/discover/partials/components/JobSkeleton/JobSectionSkeleton';
import SectionTitle from '@shared/components/Organism/Objects/Common/SectionTitle';
import classes from './loading.module.scss';

const Loading = () => {
  const { t } = useTranslation();

  return (
    <Flex className={classes.rootSkeletonWrapper}>
      <SectionTitle title={t('pp_category')} />
      <Carousel className={classes.carouselSkeleton}>
        {carouselPlaceholderData.map(({ id }) => (
          <Flex
            key={`${id}_people`}
            className={classes.skeletonCarouselItemRoot}
          >
            <Flex className={classes.imageWrapper}>
              <Skeleton className={classes.coverImage} />
            </Flex>
            <Flex className={classes.details}>
              <Skeleton className={classes.title} />
            </Flex>
          </Flex>
        ))}
      </Carousel>
      <JobSectionSkeleton title={t('top_suggestions')} />
    </Flex>
  );
};

export default Loading;
