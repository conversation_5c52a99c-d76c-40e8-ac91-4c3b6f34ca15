import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import classes from './JobItemSkeleton.module.scss';

export interface JobItemSkeletonProps {
  className?: string;
}

const JobItemSkeleton: React.FC<JobItemSkeletonProps> = ({ className }) => (
  <div className={cnj(classes.jobItem, className)}>
    <div className="flex items-start gap-12 ">
      <Skeleton className="!w-[80px] !h-[80px] rounded " />
      <Skeleton className="!h-[80px] rounded flex-1" />
    </div>

    <div>
      <Skeleton className="!h-[63px] rounded " />
    </div>

    <div className="flex flex-col gap-8">
      <Skeleton className="!h-[14px] !w-[112px] rounded " />

      <div className="flex items-start gap-12 ">
        <Skeleton className="!h-32 flex-1 rounded " />
        <Skeleton className="!h-32 flex-1 rounded " />
        <Skeleton className="!h-32 flex-1 rounded " />
      </div>
    </div>

    <div className="flex justify-between">
      <div className="flex items-start gap-12 ">
        <Skeleton className="!h-26 !w-[89px] rounded " />
        <Skeleton className="!h-26 !w-[66px] rounded " />
      </div>
      <Skeleton className="!h-26 !w-[58px] rounded " />
    </div>

    <div className="flex items-start gap-12 ">
      <Skeleton className="!h-32 flex-1 rounded " />
      <Skeleton className="!h-32 flex-1 rounded " />
    </div>
  </div>
);

export default JobItemSkeleton;
