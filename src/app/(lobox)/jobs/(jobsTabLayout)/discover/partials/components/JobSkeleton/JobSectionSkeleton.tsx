import React from 'react';
import Flex from 'shared/uikit/Flex/Flex.component';
import Section from 'shared/components/molecules/Section';
import isString from 'lodash/isString';
import classes from './JobSectionSkeleton.module.scss';
import JobItemSkeleton from './JobItemSkeleton';

export interface JobSectionSkeletonProps {
  title?: React.ReactNode;
  number?: number;
}

const JobSectionSkeleton: React.FC<JobSectionSkeletonProps> = ({
  title,
  number = 4,
}) => {
  const list = Array(number).fill(1);

  return (
    <Section title={title as any}>
      <Flex className={classes.jobItemWrapper}>
        {list.map((_, i) => (
          <JobItemSkeleton key={`${isString(title) ? title : ''}_${i}_job`} />
        ))}
      </Flex>
    </Section>
  );
};

export default JobSectionSkeleton;
