import React, { useEffect, useState } from 'react';
import Carousel from 'shared/uikit/Carousel';
import jobsApi from 'shared/utils/api/jobs';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { carouselPlaceholderData } from 'shared/constants/enums';
import SectionTitle from 'shared/components/Organism/Objects/Common/SectionTitle';
import { PageCategoryItem } from 'shared/components/Organism/PageCategoryItem';
import classes from './PopularCategories.component.module.scss';

interface PopularCategoriesProps {
  onClick: (key: any) => void;
}

const PopularCategories: React.FC<PopularCategoriesProps> = ({ onClick }) => {
  const { t } = useTranslation();
  const { data = carouselPlaceholderData, isLoading } = useReactQuery({
    action: {
      apiFunc: jobsApi.getPopularCategories,
      key: [QueryKeys.jobPopularCategories],
    },
  });
  return (
    <>
      <SectionTitle title={t('pp_category')} />
      <Carousel
        className={classes.carousel}
        visibleHead={false}
        moveWalkDistance={208}
        childrenWrapperClassName={classes.wrapper}
      >
        {data.map(({ id, title, imageUrl }, idx) => (
          <PageCategoryItem
            key={id}
            onClick={() => onClick(id)}
            title={title}
            cover={imageUrl}
            alt={title}
            isLoading={isLoading}
            eagerLoad={idx < 8}
          />
        ))}
      </Carousel>
    </>
  );
};

export default PopularCategories;
