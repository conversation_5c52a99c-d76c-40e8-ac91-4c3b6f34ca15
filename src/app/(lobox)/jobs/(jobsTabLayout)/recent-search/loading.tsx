import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Skeleton from 'shared/uikit/Skeleton';
import classes from './loading.module.scss';

const ListSkeleton: React.FC<{ count?: number }> = ({ count = 8 }) => (
  <Flex className={classes.root}>
    {Array.from({ length: count }).map((_, i) => (
      <Flex key={i} className={cnj(classes.item)}>
        <Skeleton className={classes.title} />
        <Skeleton className={classes.subtitle} />
      </Flex>
    ))}
  </Flex>
);

export default ListSkeleton;
