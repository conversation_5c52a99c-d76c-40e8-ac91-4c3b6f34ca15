'use client';

import React from 'react';
import QueryKeys from 'shared/utils/constants/queryKeys';
import usePaginateQuery from 'shared/utils/hooks/usePaginateQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { getJobsRecentSearches } from 'shared/utils/api/depot';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import RecentSearchList from './partials/components/RecentSearch.list';

export interface RecentSearchProps {
  className?: string;
}

const RecentSearch: React.FC<RecentSearchProps> = ({ className }) => {
  const { t } = useTranslation();
  const { content, isEmpty, isLoading, refetch } = usePaginateQuery({
    action: {
      apiFunc: getJobsRecentSearches,
      key: [QueryKeys.recentSearchJobs],
    },
  });

  if (isEmpty) {
    return (
      <EmptySectionInModules
        title={t('no_recent_searches')}
        text={t('no_items_to_display')}
        isFullHeight
        isFullWidth
        isInsideTab
      />
    );
  }

  return (
    <RecentSearchList data={content} isLoading={isLoading} refetch={refetch} />
  );
};

export default RecentSearch;
