import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import PopperMenu from 'shared/uikit/PopperMenu';
import Typography from 'shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import classes from './RecentSearch.item.module.scss';

interface IRecentSearchMenu {
  key: string;
  iconName: string;
  label: string;
  onClick: (id: string) => void;
}

export interface RecentSearchItemProps {
  job?: any;
  menuList?: Array<IRecentSearchMenu>;
  className?: string;
}

const RecentSearchItem = ({
  className,
  job,
  menuList,
}: RecentSearchItemProps): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Flex className={cnj(classes.cardContainer, className)}>
      <Flex className={classes.centerContainer}>
        <Flex flexDir="row" className={classes.centerHeader}>
          <Typography
            size={18}
            color="thirdText"
            font="700"
            height={25}
            isWordWrap
            lineNumber={1}
          >
            {job?.text}
          </Typography>
          {!menuList?.length ? null : (
            <PopperMenu
              closeOnScroll
              placement="bottom-end"
              buttonComponent={(visible: boolean) => (
                <IconButton
                  className={cnj(
                    classes.actionButton,
                    visible && classes.activeMenuButton
                  )}
                  colorSchema={visible ? 'primary-blue' : 'primary'}
                  type="far"
                  name="ellipsis-h"
                />
              )}
            >
              {menuList.map(({ key, iconName, label, onClick }) => (
                <PopperItem
                  {...{
                    key,
                    iconName,
                    label,
                    onClick,
                    labelClassName: classes.labelClassName,
                  }}
                />
              ))}
            </PopperMenu>
          )}
        </Flex>
        <Typography
          font="400"
          size={14}
          color="border"
          height={21}
          isTruncated
          isWordWrap
          lineNumber={1}
        >
          {job?.location || t('no_location_ent')}
        </Typography>
      </Flex>
    </Flex>
  );
};

export default RecentSearchItem;
