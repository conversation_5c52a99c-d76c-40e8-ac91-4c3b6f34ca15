import React from 'react';
import Button from 'shared/uikit/Button';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import useToast from 'shared/uikit/Toast/useToast';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useGlobalSearchUtilities from 'shared/hooks/useGlobalSearchUtilities';
import classes from './RecentSearch.remove.module.scss';

type Props = {
  refetch: Function;
};

const RemoveAllRecentSearch: React.FC<Props> = ({ refetch }) => {
  const { t } = useTranslation();

  const { removeAllRecentSearches } = useGlobalSearchUtilities();
  const { openConfirmDialog } = useOpenConfirm();
  const toast = useToast();

  const openConfirm = () => {
    openConfirmDialog({
      title: t('remove_all_recent_jobs_lbl'),
      message: t('remove_all_recent_msg'),
      confirmButtonText: t('remove'),
      cancelButtonText: t('cancel'),
      confirmCallback: () => {
        removeAllRecentSearches({
          moduleType: 'JOBS',
          onSuccess: () => {
            toast({
              type: 'success',
              icon: 'check-circle',
              title: t('remove_all_recent_toast_lbl'),
              message: t('remove_all_recent_toast_msg'),
            });
            refetch?.();
          },
        });
      },
    });
  };

  return (
    <Button
      label={t('remove_all')}
      schema="error-semi-transparent"
      className={classes.button}
      onClick={openConfirm}
    />
  );
};

export default RemoveAllRecentSearch;
