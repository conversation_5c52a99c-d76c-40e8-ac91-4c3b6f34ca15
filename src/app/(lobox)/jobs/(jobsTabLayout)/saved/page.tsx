'use client';

import React from 'react';
import Flex from 'shared/uikit/Flex';
import jobsApi from 'shared/utils/api/jobs';
import { jobSelectedKeys, jobStatusKeys } from 'shared/utils/constants/enums';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import { searchGroupTypes } from 'shared/constants/search';
import JobsSectionLayoutWithJobsList from 'shared/components/Organism/JobsSectionLayoutWithJobsList';
import Loading from './loading';

const SavedJobs = (): JSX.Element => {
  const { t } = useTranslation();
  const navigateSearchPage = useNavigateSearchPage();
  const { data: hasSavedJobs, isLoading } = useReactQuery({
    action: {
      apiFunc: jobsApi.userSavedJobsAvailable,
      key: [QueryKeys.userSavedJobsAvailable],
    },
  });
  const navigateToSearchPage =
    (jobGroupStatus: keyof typeof jobStatusKeys) => () => {
      navigateSearchPage({
        searchGroupType: searchGroupTypes.SAVED,
        jobGroupStatus,
      });
    };

  if (isLoading) {
    return <Loading />;
  }

  if (!hasSavedJobs) {
    return (
      <EmptySectionInModules
        title={t('no_saved_jobs')}
        text={t('no_items_to_display')}
        isFullHeight
        isFullWidth
        isInsideTab
      />
    );
  }

  return (
    <Flex>
      <JobsSectionLayoutWithJobsList
        firstTitle={t('open')}
        onClick={navigateToSearchPage(jobStatusKeys.open)}
        jobGroupStatus="OPEN"
        queryKey={QueryKeys.openSavedJobs}
        apiFunc={jobsApi.getJobsSavedList}
        searchGroupType={searchGroupTypes.SAVED}
        params={{
          status: jobSelectedKeys.open,
        }}
        emptyProps={{ doNotShowSectionIfEmpty: true }}
      />
      <JobsSectionLayoutWithJobsList
        firstTitle={t('applied')}
        onClick={navigateToSearchPage(jobStatusKeys.applied)}
        jobGroupStatus={jobStatusKeys.applied}
        queryKey={QueryKeys.appliedSavedJobs}
        apiFunc={jobsApi.getJobsSavedList}
        searchGroupType={searchGroupTypes.SAVED}
        params={{
          isApplied: true,
        }}
        emptyProps={{ doNotShowSectionIfEmpty: true }}
      />
      <JobsSectionLayoutWithJobsList
        firstTitle={t('hired')}
        onClick={navigateToSearchPage(jobStatusKeys.hired)}
        jobGroupStatus={jobStatusKeys.hired}
        queryKey={QueryKeys.appliedSavedJobs}
        apiFunc={jobsApi.getJobsSavedList}
        searchGroupType={searchGroupTypes.SAVED}
        params={{
          isHired: true,
        }}
        emptyProps={{ doNotShowSectionIfEmpty: true }}
      />
      <JobsSectionLayoutWithJobsList
        firstTitle={t('closed')}
        tooltipText={t('w_r_go_r_closed')}
        onClick={navigateToSearchPage(jobStatusKeys.closed)}
        jobGroupStatus={jobStatusKeys.closed}
        queryKey={QueryKeys.closedSavedJobs}
        apiFunc={jobsApi.getJobsSavedList}
        searchGroupType={searchGroupTypes.SAVED}
        params={{
          status: jobSelectedKeys.closed,
        }}
        emptyProps={{ doNotShowSectionIfEmpty: true }}
      />
    </Flex>
  );
};

export default SavedJobs;
