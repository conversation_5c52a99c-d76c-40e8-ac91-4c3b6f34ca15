'use client';

import classes from './loading.module.scss';

import React, { type FC } from 'react';
import JobElementHeaderSkeleton from '@app/jobs/partials/components/JobElement/JobElementHeader/JobElementHeader.skeleton';
import Flex from '@shared/uikit/Flex/index';
import SectionSkeleton from '@shared/components/molecules/Section/Section.skeleton';
import Skeleton from '@shared/uikit/Skeleton';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cnj from '@shared/uikit/utils/cnj';

const SearchJobsDetails: FC = () => {
  const { t } = useTranslation();

  return (
    <Flex className={cnj('h-full', classes.mainSkeletonWrapper)}>
      <ModalHeaderSimple
        visibleHeaderDivider
        className={classes.mobileHeader}
        title={t('job_details')}
      />
      <JobElementHeaderSkeleton isJob />
      <SectionSkeleton hasTitle>
        <Skeleton className="h-[300px] w-full rounded-md" />
      </SectionSkeleton>
      <SectionSkeleton hasTitle>
        <Skeleton className="h-[300px] w-full rounded-md" />
      </SectionSkeleton>
    </Flex>
  );
};

export default SearchJobsDetails;
