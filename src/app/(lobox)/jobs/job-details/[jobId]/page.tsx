import type { Metadata } from 'next';
import { dehydrate } from '@tanstack/query-core';
import Hydrate from 'shared/utils/hydrate.client';
import QueryKeys from 'shared/utils/constants/queryKeys';
import jobsApi from 'shared/utils/api/jobs';
import getAccessTokenFromCookies from 'shared/utils/getAccessTokenFromCookies';
import getServerSideQueryClient from 'shared/utils/getServerSideQueryClient';
import JobDetails from './partials/components/JobDetails';
import fetchJobMetadata from './partials/utils/fetchJobMetadata';

interface PageProps {
  params: Promise<{ jobId: string }>;
}
export default async function Page({ params }: PageProps) {
  const { jobId } = await params;
  const queryClient = getServerSideQueryClient();
  const queryKey = [QueryKeys.jobDetails, jobId];
  const accessToken = getAccessTokenFromCookies();

  const apiParams = {
    params: { id: jobId },
    ...(accessToken && {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    }),
  };

  await queryClient.prefetchQuery(queryKey, () =>
    jobsApi.getJobDetails(apiParams)
  );

  const dehydratedState = dehydrate(queryClient);

  return (
    <Hydrate state={dehydratedState}>
      <JobDetails jobId={jobId} />
    </Hydrate>
  );
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  try {
    const { jobId } = await params;
    return await fetchJobMetadata(jobId);
  } catch {
    return {};
  }
}
