'use client';

import React from 'react';
import { JobElementProvider } from '@app/jobs/partials/context/JobElement/JobElement.provider';
import JobElement from '@app/jobs/partials/components/JobElement';

interface Props {
  jobId: string;
}

const JobDetails: React.FC<Props> = ({ jobId }) => (
  <JobElementProvider initialValue={{ jobId, mode: 'edit' }}>
    <JobElement />
  </JobElementProvider>
);

export default JobDetails;
