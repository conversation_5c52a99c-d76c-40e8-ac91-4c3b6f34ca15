import React from 'react';
import { routeNames } from 'shared/utils/constants/routeNames';
import appEnvironment from 'shared/utils/constants/env';
import { getObjectDetail, isPageType } from 'shared/utils/api/object';
import { getJobDetails } from 'shared/utils/api/jobs';
import applyResolutionToImageSrc from 'shared/utils/toolkit/applyResolutionToImageSrc';

interface JobDetailsSEOProps {
  jobId?: string;
}

const JobDetailsSeo: React.FC<JobDetailsSEOProps> = async ({ jobId }) => {
  if (!jobId) return null;

  const job = await getJobDetails({
    params: { id: jobId },
  });
  if (!job) return null;

  const {
    id,
    title: unsafeTitle,
    description: htmlDescription,
    category,
    pageCroppedImageUrl,
    createdDate,
    updatedDate,
  } = job;
  const title = unsafeTitle?.label ?? '';
  const description =
    (htmlDescription || category?.label || '')?.replace(/<[^>]+>/g, '') ?? '';

  const datePublished = createdDate ?? new Date().toISOString();
  const dateModified = updatedDate ?? new Date().toISOString();

  const pathname = appEnvironment.baseUrl + routeNames.jobDetails.makeRoute(id);
  const image = pageCroppedImageUrl
    ? applyResolutionToImageSrc(pageCroppedImageUrl, 'medium')
    : undefined;

  let author = '';
  if (job.ownerId) {
    try {
      const ownerObject = await getObjectDetail({
        params: { objectId: job.ownerId },
      });
      if (ownerObject && !isPageType(ownerObject)) {
        author = ownerObject.fullName;
      }
    } catch {
      /** no action required */
    }
  }

  const copyrightYear = new Date().getFullYear();

  // schema.org in JSONLD format
  // https://developers.google.com/search/docs/guides/intro-structured-data
  // You can fill out the 'author', 'creator' with more data or another type (e.g. 'Organization')
  // Structured Data Testing Tool >>
  // https://search.google.com/structured-data/testing-tool

  const schemaOrgWebPage = {
    '@context': 'http://schema.org',
    '@type': 'WebPage',
    url: pathname,
    headline: description,
    inLanguage: 'siteLanguage',
    mainEntityOfPage: pathname,
    description,
    name: title,
    author: {
      '@type': 'Person',
      name: author,
    },
    copyrightHolder: {
      '@type': 'Person',
      name: author,
    },
    copyrightYear,
    creator: {
      '@type': 'Person',
      name: author,
    },
    publisher: {
      '@type': 'Person',
      name: author,
    },
    datePublished,
    dateModified,
    image: {
      '@type': 'ImageObject',
      url: `${image}`,
    },
  };

  // Initial breadcrumb list
  const breadcrumb = {
    '@context': 'http://schema.org',
    '@type': 'BreadcrumbList',
    description: 'Breadcrumbs list',
    name: 'Breadcrumbs',
    itemListElement: [
      {
        '@type': 'ListItem',
        item: {
          '@id': appEnvironment.baseUrl,
          name: 'Homepage',
        },
        position: 1,
      },
      {
        '@type': 'ListItem',
        item: {
          '@id': appEnvironment.baseUrl + routeNames.searchJobs,
          name: 'Jobs',
        },
        position: 2,
      },
      {
        '@type': 'ListItem',
        item: {
          '@id': pathname,
          name: title,
        },
        position: 3,
      },
    ],
  };

  return (
    <>
      <script type="application/ld+json">{JSON.stringify(breadcrumb)}</script>
      <script type="application/ld+json">
        {JSON.stringify(schemaOrgWebPage)}
      </script>
    </>
  );
};

export default JobDetailsSeo;
