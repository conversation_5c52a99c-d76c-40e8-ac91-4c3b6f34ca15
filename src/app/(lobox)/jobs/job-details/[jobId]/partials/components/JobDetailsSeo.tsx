import React from 'react';
// import SEO from 'shared/uikit/SEO';
import textTruncate from 'shared/uikit/utils/textTruncate';
import appEnvironment from 'shared/constants/env';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { routeNames } from 'shared/utils/constants/routeNames';
import { useQuery } from '@tanstack/react-query';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';

const JobDetailsSeo: React.FC = () => {
  // TODO refactor it with next-js

  return null;
  const { pageTitle } = useJobElement();
  const { data: jobId } = useQuery<any>([QueryKeys.currentEntityId]);
  const { data = {} } = useQuery<any>([QueryKeys.jobDetails, jobId]);
  const { id, title, description, category, page } = data;

  return (
    <SEO
      title={title?.label || pageTitle}
      titleTemplate="Lobox"
      description={textTruncate(
        (description || category?.label || '')?.replace(/<[^>]+>/g, ''),
        35
      )}
      pathname={`${appEnvironment.baseUrl}${routeNames.jobDetails.makeRoute(
        id
      )}`}
      image={page?.croppedImageUrl || ''}
    />
  );
};

export default JobDetailsSeo;
