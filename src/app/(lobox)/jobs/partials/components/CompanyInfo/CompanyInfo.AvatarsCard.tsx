import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import AvatarsCard from 'shared/components/molecules/AvatarsCard';
import UnderlinedButton from 'shared/components/molecules/UnderlinedButton';
import classes from './CompanyInfo.AvatarsCard.module.scss';

export interface CompanyInfoAvatarsCardProps {
  className?: string;
  title: string;
  text?: string;
  avatars: Array<{ id: string; croppedImageUrl: string }>;
  onClick: () => void;
}

const CompanyInfoAvatarsCard: React.FC<CompanyInfoAvatarsCardProps> = ({
  className,
  title,
  avatars,
  text,
  onClick,
}) => {
  const { t } = useTranslation();

  return (
    <Flex className={cnj(classes.companyInfoAvatarsCardRoot, className)}>
      <Flex className={classes.topWrapper}>
        <Typography font="700" size={20} height={23}>
          {title}
        </Typography>
        <UnderlinedButton
          onClick={onClick}
          className={classes.button}
          label={t('see_all')}
        />
      </Flex>
      <Flex className={classes.bottomWrapper}>
        <AvatarsCard
          onClick={onClick}
          avatarProps={{
            size: 'xs',
            className: classes.avatar,
          }}
          avatars={avatars}
        />
        <Typography
          className={classes.text}
          ml={4}
          size={12}
          height={12}
          color="border"
        >
          {text}
        </Typography>
      </Flex>
    </Flex>
  );
};

export default CompanyInfoAvatarsCard;
