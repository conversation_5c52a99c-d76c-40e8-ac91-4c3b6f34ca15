import React from 'react';
import FollowersItem from 'shared/components/molecules/FollowersItem/FollowersItem';
import { type PageApiResponseType } from '@shared/types/page';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import { useQuery } from '@tanstack/react-query';
import useUpdateQueryData from '@shared/utils/hooks/useUpdateQueryData';
import useJobElement from '../../hooks/useJobElement';

interface CompanyFollowButtonProps {
  follow?: boolean;
  className?: string;
  queryKey?: any;
}

const CompanyFollowButton = ({
  follow,
  className,
  queryKey,
}: CompanyFollowButtonProps): JSX.Element => {
  const { getJobPropValue } = useJobElement();
  const username = getJobPropValue('pageUsername');
  const pageObject = {
    isPage: true,
    id: getJobPropValue('pageId'),
    username,
    name: getJobPropValue('pageTitle'),
    croppedImageUrl: getJobPropValue('pageCroppedImageUrl'),
  };
  const { replace } = useUpdateQueryData(queryKey);
  const { data } = useQuery<any>(queryKey);
  const { reFetchAppObject } = useGetAppObject();
  const success = (item: PageApiResponseType, follow: boolean) => () => {
    const newItem = {
      ...item,
      network: {
        ...item?.network,
        follow,
      },
    };
    replace(newItem);
    reFetchAppObject();
  };
  return (
    <FollowersItem
      className={className}
      object={{
        isPage: true,
        id: pageObject.id,
        name: pageObject.name as string,
        username: pageObject.username as string,
        croppedImageUrl: pageObject?.croppedImageUrl || null,
        fullName: pageObject.name,
        isFollowed: follow,
        back: false,
      }}
      onSuccess={success(data, !follow)}
    />
  );
};

export default CompanyFollowButton;
