import React from 'react';
import PopperMenu from 'shared/uikit/PopperMenu';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import classes from './CompanyInfo.Menu.module.scss';

const JobCompanyFollowersInfo = ({ actions }: { actions: any[] }) => (
  <PopperMenu
    placement="bottom-end"
    closeOnScroll
    buttonComponent={
      <IconButton
        type="fas"
        name="ellipsis-h"
        size="md"
        className={classes.withMargin}
      />
    }
  >
    <>
      {actions?.map(({ icon, label, onClick }) => (
        <PopperItem
          key={icon}
          onClick={onClick}
          iconName={icon}
          iconType="far"
          label={label}
        />
      ))}
    </>
  </PopperMenu>
);

export default JobCompanyFollowersInfo;
