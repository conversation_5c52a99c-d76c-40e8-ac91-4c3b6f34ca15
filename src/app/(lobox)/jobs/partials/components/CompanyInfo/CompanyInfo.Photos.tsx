import React from 'react';
import Carousel from 'shared/uikit/Carousel';
import cnj from 'shared/uikit/utils/cnj';
import Image from 'shared/uikit/Image';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './CompanyInfo.Photos.module.scss';

const JobCompanyPhotos = ({
  className,
  companyPhotos,
}: {
  className?: string;
  companyPhotos?: Array<string>;
}) => {
  const { t } = useTranslation();

  return (
    <Carousel
      className={className}
      moveWalkDistance={208}
      title={t('company_imgs')}
      headClassName={classes.carouselHead}
      areaClassName={classes.carouselArea}
      showCenterButtons
    >
      {companyPhotos?.map((imageUrl: string, index: number) => (
        <Image
          key={imageUrl}
          src={imageUrl}
          className={cnj(classes.image, index !== 0 && classes.withMargin)}
        />
      ))}
    </Carousel>
  );
};

export default JobCompanyPhotos;
