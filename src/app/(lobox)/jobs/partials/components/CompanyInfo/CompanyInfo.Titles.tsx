import type { FC } from 'react';
import Flex from 'shared/uikit/Flex';
import ObjectLink from 'shared/uikit/Link/ObjectLink';
import ObjectInfoCard from 'shared/components/molecules/ObjectInfoCard';
import classes from './CompanyInfo.Titles.module.scss';

interface JobCompanyTitlesProps {
  data: {
    firstText?: string;
    secondText?: string;
    thirdText?: string;
    fourthText?: string;
    id: string;
    username: string;
  };
  className?: string;
}

const JobCompanyTitles: FC<JobCompanyTitlesProps> = ({ className, data }) => {
  const { firstText, secondText, thirdText, fourthText } = data;

  const FirstTextWrapper = ({ children }) => (
    <ObjectLink
      className={classes.firstText}
      objectId={data.id}
      username={data.username}
    >
      {children}
    </ObjectLink>
  );

  return (
    <Flex className={className}>
      <ObjectInfoCard
        firstText={firstText}
        FirstTextWrapper={FirstTextWrapper}
        secondText={secondText}
        thirdText={thirdText}
        fourthText={fourthText?.title || fourthText}
        isPage
        withAvatar={false}
        isFirstTextSmall
      />
    </Flex>
  );
};

export default JobCompanyTitles;
