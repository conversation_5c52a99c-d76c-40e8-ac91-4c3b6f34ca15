import { useRef } from 'react';
import Avatar, { type AvatarProps } from 'shared/uikit/Avatar';
import Button from 'shared/uikit/Button';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Media from 'shared/uikit/Media';
import ObjectLink from 'shared/uikit/Link/ObjectLink';
import RichTextView from 'shared/uikit/RichText/RichTextView';
import type { PeopleType } from 'shared/types/people';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { SocialConnectionsTabs } from 'shared/constants/enums';
import HorizontalInfoBox from 'shared/components/molecules/HorizontalInfoBox';
import type { NetworkModelType, PageType } from 'shared/types/page';
import type { UserType } from 'shared/types/user';
import JobCompanyTitles from './CompanyInfo.Titles';
import JobCompanyMenu from './CompanyInfo.Menu';
import JobCompanyFollowButton from './CompanyInfo.FollowButton';
import JobCompanyAvatarsCard from './CompanyInfo.AvatarsCard';
import JobCompanyPhotos from './CompanyInfo.Photos';
import classes from './CompanyInfo.component.module.scss';

export type Action = { icon: string; label: string; onClick?: Function };

interface CompanyInfoProps {
  data: {
    id: string;
    username: string;
    firstText?: string;
    secondText?: string;
    thirdText?: string;
    fourthText?: string;
    image?: any;
    categoryId: string;
    network?: NetworkModelType;
  };
  className?: string;
  followerData?: Array<{ onClick: any; title: string; value: string }>;
  description?: string;
  companyPhotos?: Array<string>;
  actions: Array<Action>;
  follow?: boolean;
  mutuals?: Array<PageType | UserType>;
  workers?: Array<PeopleType>;
  onClickSecondText?: () => void;
  openObjectNetworkModal?: (currentTab: string) => void;
  worksHereCount?: number;
  mutualConnectionsCount?: number;
  queryKey?: any;
  showFollowBtns?: boolean;
  avatarProps?: AvatarProps;
}

const CompanyInfo = ({
  data,
  className,
  followerData = [],
  description,
  companyPhotos,
  actions,
  follow,
  mutuals = [],
  workers = [],
  onClickSecondText,
  mutualConnectionsCount,
  worksHereCount,
  openObjectNetworkModal,
  queryKey,
  showFollowBtns = true,
  avatarProps,
}: CompanyInfoProps): JSX.Element => {
  const { image } = data;
  const { t } = useTranslation();
  const contianerRef = useRef(null);

  const moreMutualCount = mutualConnectionsCount - mutuals?.length;
  const moreWorksHereCount = worksHereCount - workers?.length;

  return (
    <Flex className={cnj(classes.companyInfoRoot, className)}>
      <Flex className={classes.companyInfoContainer} ref={contianerRef}>
        <Flex className={classes.wrapper}>
          <Media greaterThan="tablet">
            <Flex>
              <Flex flexDir="row" className={classes.leftWrapper}>
                <ObjectLink username={data.username} objectId={data.id}>
                  <Avatar
                    {...avatarProps}
                    imgSrc={image}
                    isCompany
                    size={avatarProps?.size ?? 'vlg'}
                  />
                </ObjectLink>
                <Flex className={classes.topWrapper}>
                  <Flex flexDir="row" className={classes.firstRow}>
                    <JobCompanyTitles
                      className={classes.jobCompanyTitles}
                      data={data}
                    />
                    <Flex flexDir="row">
                      <JobCompanyMenu actions={actions} />
                    </Flex>
                  </Flex>
                  {!!followerData?.length && (
                    <Flex flexDir="row" className={classes.followersWrapper}>
                      <HorizontalInfoBox direction="row" data={followerData} />
                    </Flex>
                  )}
                </Flex>
              </Flex>
            </Flex>
          </Media>
          <Media lessThan="midDesktop">
            <Flex>
              <Flex flexDir="row" className={classes.leftWrapper}>
                <ObjectLink objectId={data.id}>
                  <Avatar imgSrc={image} isCompany size="slg" />
                </ObjectLink>
                <Flex className={classes.topWrapper}>
                  <JobCompanyTitles
                    className={classes.jobCompanyTitles}
                    data={data}
                  />
                </Flex>
                <JobCompanyMenu actions={actions} />
              </Flex>
              {!!followerData?.length && (
                <Flex flexDir="row" className={classes.followersWrapper}>
                  <HorizontalInfoBox direction="col" data={followerData} />
                </Flex>
              )}
            </Flex>
          </Media>
          {showFollowBtns && (
            <JobCompanyFollowButton
              className={classes.followBtn}
              follow={follow}
              queryKey={queryKey}
            />
          )}
          {!!description && (
            <RichTextView
              html={description}
              typographyProps={{
                color: 'thirdText',
              }}
              row={3}
              showMore
              className={classes.description}
            />
          )}
          {mutuals?.length ? (
            <JobCompanyAvatarsCard
              onClick={() => {
                openObjectNetworkModal?.(SocialConnectionsTabs.mutuals);
              }}
              title={t('mutuals')}
              avatars={mutuals}
              className={classes.marginTop}
              text={
                moreMutualCount > 0
                  ? `+${moreMutualCount} ${t('more_sm')}`
                  : undefined
              }
            />
          ) : null}
          {workers?.length ? (
            <JobCompanyAvatarsCard
              onClick={() => {
                openObjectNetworkModal?.(SocialConnectionsTabs.worksHere);
              }}
              title={t('associated')}
              className={classes.marginTop}
              avatars={workers}
              text={
                moreWorksHereCount > 0
                  ? `+${moreWorksHereCount} ${t('more_sm')}`
                  : undefined
              }
            />
          ) : null}

          {companyPhotos?.length ? (
            <Flex>
              <JobCompanyPhotos
                className={classes.companyPhotos}
                companyPhotos={companyPhotos}
              />
            </Flex>
          ) : null}
          <ObjectLink username={data.username} objectId={data.id}>
            <Button
              className={classes.seemore}
              label={t('see_details')}
              schema="semi-transparent"
              rightIcon="chevron-right"
            />
          </ObjectLink>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default CompanyInfo;
