import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import type { TextProps } from 'shared/uikit/Typography';
import classes from './JobApplicationsCount.component.module.scss';

export interface JobApplicationsCountProps {
  className?: string;
  count: string;
  textProps?: TextProps;
}

const JobApplicationsCount: React.FC<JobApplicationsCountProps> = ({
  className,
  count,
  textProps,
}) => (
  <Flex className={cnj(classes.jobApplicationsCountRoot, className)}>
    <Typography
      textAlign="center"
      color="success"
      font="700"
      size={16}
      height={20}
      {...textProps}
    >
      {count || 0}
    </Typography>
  </Flex>
);

export default JobApplicationsCount;
