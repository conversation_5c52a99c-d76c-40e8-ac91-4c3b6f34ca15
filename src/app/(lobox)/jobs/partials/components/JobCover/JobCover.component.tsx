import React from 'react';
import Avatar from 'shared/uikit/Avatar';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import ObjectInfoCard from 'shared/components/molecules/ObjectInfoCard';
import type { HorizontalInfoBoxItemProps } from 'shared/components/molecules/HorizontalInfoBox';
import HorizontalInfoBox from 'shared/components/molecules/HorizontalInfoBox';
import Media from 'shared/uikit/Media';
import AvatarCard from 'shared/uikit/AvatarCard';
import UserInfoCardDetailItem from 'shared/components/molecules/ObjectInfoCard/ObjectInfoCard.Item';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { FallbackText } from '@shared/components/atoms/FallbackText';
import classes from './JobCover.component.module.scss';

interface JobCoverProps {
  data: {
    firstText?: string | ReactNode;
    secondText?: string | ReactNode;
    thirdText?: string | ReactNode;
    fourthText?: string | ReactNode;
    image?: any;
    objectId?: string;
    jobId?: string;
  };
  action?: React.ReactNode;
  bottomActions?: React.ReactNode;
  hintActions?: React.ReactNode;
  jobInfo?: Array<HorizontalInfoBoxItemProps>;
  className?: string;
  backButtonProps?: { className?: string; onClick?: () => void };
  isMessageVariant?: boolean;
}

const JobCover = ({
  data,
  action,
  jobInfo,
  className,
  bottomActions,
  backButtonProps,
  hintActions,
  isMessageVariant,
}: JobCoverProps): JSX.Element => {
  const { firstText, secondText, thirdText, fourthText, image } = data;
  const { t } = useTranslation();

  return (
    <Flex className={cnj(classes.jobCoverRoot, className)}>
      <Flex className={cnj(classes.jobCoverContainer)}>
        <Media at="tablet">
          <Flex>
            <Flex flexDir="row">
              <AvatarCard
                data={{ image, title: firstText, subTitle: secondText }}
                avatarProps={{ isCompany: true }}
                withPadding={false}
                noHover
              />
              <Flex flexDir="row" className={classes.actionButtonWrapper}>
                {action}
              </Flex>
            </Flex>
            <UserInfoCardDetailItem
              title={thirdText}
              icon="category"
              className="mr-t-8"
            />
            <UserInfoCardDetailItem
              icon="location-v2"
              title={<FallbackText value={fourthText} />}
              className={classes.marginTop4}
            />
            <HorizontalInfoBox
              direction="col"
              data={jobInfo}
              className="mr-t-16"
              disabled
            />
          </Flex>
        </Media>
        <Media greaterThan="tablet">
          <Flex flexDir="row">
            <Avatar
              imgSrc={image}
              className={classes.avatar}
              isCompany
              size="vlg"
            />
            <Flex className={classes.jobInfoWrapper}>
              <Flex className={classes.topWrapper}>
                <Flex className={classes.jobTitleWrapper}>
                  <ObjectInfoCard
                    firstText={firstText}
                    secondText={secondText}
                    thirdText={thirdText}
                    fourthText={fourthText}
                    isPage
                    isFirstTextSmall
                    withAvatar={false}
                  />
                </Flex>
                <Flex flexDir="row" className={classes.actionButtonWrapper}>
                  {action}
                </Flex>
              </Flex>
              <HorizontalInfoBox
                className={classes.bottomWrapper}
                direction="row"
                data={jobInfo}
                disabled
              />
            </Flex>
          </Flex>
        </Media>
        <Flex className={classes.bottomActionsWrapper}>{bottomActions}</Flex>
        {hintActions}
      </Flex>
    </Flex>
  );
};

export default JobCover;
