import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Divider from 'shared/uikit/Divider';
import Typography from 'shared/uikit/Typography';
import Button from 'shared/uikit/Button';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { ButtonProps } from 'shared/uikit/Button';
import classes from './JobCover.hint.module.scss';

export interface JobCoverHintProps {
  className?: string;
  title: string;
  subTitle?: string;
  primaryActionProps?: ButtonProps;
  secondaryActionProps?: ButtonProps;
}

const JobCoverHint: React.FC<JobCoverHintProps> = ({
  className,
  title,
  subTitle,
  primaryActionProps = {},
  secondaryActionProps = {},
}) => {
  const { t } = useTranslation();

  return (
    <Flex className={cnj(classes.jobCoverHintRoot, className)}>
      <Divider className={classes.externalHintDivider} />
      <Typography font="700" size={15} height={18} color="thirdText">
        {title}
      </Typography>
      {subTitle && (
        <Typography color="secondaryDisabledText" size={15} height={18} mt={4}>
          {subTitle}
        </Typography>
      )}
      <Flex className={classes.jobHintButtonWrapper}>
        <Button
          label={t('no')}
          schema="ghost"
          fullWidth
          {...primaryActionProps}
        />
        <Flex className={classes.buttonsDivider} />
        <Button
          label={t('yes')}
          schema="semi-transparent"
          fullWidth
          {...secondaryActionProps}
        />
      </Flex>
    </Flex>
  );
};

export default JobCoverHint;
