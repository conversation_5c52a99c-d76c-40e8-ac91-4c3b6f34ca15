import React, { useMemo } from 'react';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useGetPageMembers from 'shared/hooks/api-hook/useGetPageMembers';
import { SearchPageMembers } from 'shared/components/molecules/SearchPapeMembers/SearchPageMembers';
import type { CollaboratorType } from 'shared/components/Organism/types';
import classes from './EditJobCollaborators.associate.modal.module.scss';

type Props = {
  isOpen?: boolean;
  closeModal?: Function;
  queryKey?: string[] | string;
  onSelect: (newAssociate: CollaboratorType) => void;
  associate: CollaboratorType;
  collaborators: CollaboratorType[];
};

export const EditAssociateModal: React.FC<Props> = ({
  isOpen,
  closeModal,
  onSelect,
  associate,
  collaborators,
}) => {
  const { t } = useTranslation();
  const { data: members = [] } = useGetPageMembers({
    enabled: true,
  });

  const availableToChooseMembers: CollaboratorType[] = useMemo(
    () => members.filter((member) => member?.user?.id !== associate?.id),
    [members, associate]
  );

  function onChange(newAssociate: any) {
    if (!newAssociate) return;
    if (!(newAssociate?.value || newAssociate?.id)) return;
    const selectedItem = structuredClone(
      availableToChooseMembers?.find(
        (item) => item?.user?.id === newAssociate?.value
      )
    ) as CollaboratorType;
    onSelect({
      ...selectedItem,
      collaboratorRole: getCollaboratorRole(selectedItem?.user?.id),
    });
    closeModal();
  }
  function getCollaboratorRole(
    associateUserId: string
  ): CollaboratorType['collaboratorRole'] {
    const collaborator = collaborators?.find(
      (_collaborator) => _collaborator?.user?.id === associateUserId
    );
    return collaborator && collaborator?.collaboratorRole === 'CREATOR'
      ? 'CREATOR'
      : 'COLLABORATOR';
  }

  return (
    <FixedRightSideModalDialog isOpen={isOpen}>
      <ModalHeaderSimple
        className={classes.header}
        visibleHeaderDivider={false}
        title={t('change_associate')}
        hideBack={false}
        backButtonProps={{ onClick: closeModal }}
        closeButtonProps={{ className: classes.closeButton }}
      />

      <Flex className={classes.wrapper}>
        <SearchPageMembers
          onChange={onChange}
          members={availableToChooseMembers}
        />

        <Flex className={classes.spaceFiller} />
        <Button disabled className={classes.submitButton} label={t('done')} />
      </Flex>
    </FixedRightSideModalDialog>
  );
};
