import React, { useCallback, useEffect, useMemo, useState } from 'react';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import useToast from 'shared/uikit/Toast/useToast';
import useAuthUser from 'shared/utils/hooks/useAuthUser';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useGetPageMembers from 'shared/hooks/api-hook/useGetPageMembers';
import { SearchPageMembers } from 'shared/components/molecules/SearchPapeMembers/SearchPageMembers';
import { JobCollaboratorItem } from 'shared/components/molecules/JobCollaboratorCard/JobCollaborator.Card';
import type { CollaboratorType } from 'shared/components/Organism/types';
import classes from './EditJobCollaborators.collaborators.modal.module.scss';

type Props = {
  isOpen?: boolean;
  closeModal?: Function;
  onSubmit?: (newCollaborators: CollaboratorType[]) => void;
  collaborators: CollaboratorType[];
  associate: CollaboratorType;
};

export const EditCollaborators: React.FC<Props> = ({
  isOpen,
  closeModal,
  onSubmit,
  collaborators,
  associate,
}) => {
  const [tempCollaborators, setTempCollaborators] = useState<
    CollaboratorType[]
  >([]);
  const { t } = useTranslation();
  const { openConfirmDialog, closeConfirm } = useOpenConfirm();
  const toast = useToast();
  const { data: members = [] } = useGetPageMembers({
    enabled: true,
  });
  const { data: thisUser } = useAuthUser();

  const isThereAChange = useMemo(() => {
    const tempCollaboratorIds = tempCollaborators?.map(
      (item) => item?.user?.id
    );
    const collaboratorIds = collaborators?.map((item) => item?.id);
    if (
      tempCollaboratorIds?.every((tempId) =>
        collaboratorIds?.includes(tempId)
      ) &&
      collaboratorIds?.every((collaboratorId) =>
        tempCollaboratorIds?.includes(collaboratorId)
      )
    )
      return false;
    return true;
  }, [collaborators, tempCollaborators]);

  const availableToChooseMembers: CollaboratorType[] = useMemo(() => {
    const tempCollaboratorIds = tempCollaborators?.map((item) => item.user?.id);
    return members.filter(
      (member) => !tempCollaboratorIds.includes(member?.user?.id)
    );
  }, [members, tempCollaborators]);

  function handleSubmit() {
    onSubmit(tempCollaborators);
    closeModal();
  }

  function onAddCollaborator(newCollaborator: any) {
    if (!newCollaborator) return;
    if (
      tempCollaborators?.some(
        (tempCollaborator) =>
          tempCollaborator?.user?.id === newCollaborator?.value
      )
    )
      return;

    const selectedItem = structuredClone(
      availableToChooseMembers?.find(
        (item) => item?.user?.id === newCollaborator?.value
      )
    ) as CollaboratorType;
    if (!selectedItem) return;
    setTempCollaborators((prev) => [
      ...prev,
      { ...selectedItem, collaboratorRole: 'COLLABORATOR' },
    ]);
  }

  function onRemoveCollaborator(removeCollaborator: CollaboratorType) {
    setTempCollaborators((prev) =>
      prev.filter(
        (collaborator) =>
          collaborator?.user?.id !== removeCollaborator?.user?.id
      )
    );
    closeConfirm();
  }

  const onRemoveHandler = useCallback(
    (item: CollaboratorType) =>
      openConfirmDialog({
        title: t('remove_collaborator'),
        message: (
          <Flex className={classes.messageWrap}>
            <Typography>{t('r_y_s_w_remove')}</Typography>
            <Typography font="bold" mr={4} ml={4}>
              {item.user.fullName}
            </Typography>
            <Typography>{t('f_t_collaborator_lst')}</Typography>
          </Flex>
        ),
        confirmButtonText: t('remove'),
        cancelButtonText: t('cancel'),
        confirmCallback: () => {
          onRemoveCollaborator(item);
          toast({
            type: 'success',
            icon: 'check-circle',
            title: t('collaborator_removed'),
            message: () => (
              <>
                <Typography font="bold" mr={3}>
                  {item.user.fullName}
                </Typography>
                <Typography>{t('success_rem_f_col_lis')}</Typography>
              </>
            ),
            actionButton: () => (
              <Button
                onClick={closeConfirm}
                schema="ghost-brand"
                className={classes.actionBtn}
                label={t('edit_collaborators')}
                variant="text"
              />
            ),
          });
        },

        cancelCallback: closeConfirm,
        isAjaxCall: false,
      }),
    []
  );

  useEffect(() => {
    setTempCollaborators([...(collaborators || [])]);
  }, [collaborators]);

  function isRemovable(item: CollaboratorType): boolean {
    if (item?.user?.id === thisUser?.id) return false;
    if (item?.collaboratorRole === 'CREATOR') return false;
    if (item?.user?.id === associate?.user?.id) return false;
    return true;
  }

  return (
    <FixedRightSideModalDialog isOpen={isOpen}>
      <ModalHeaderSimple
        className={classes.header}
        visibleHeaderDivider={false}
        title={t('edit_collaborators')}
        hideBack={false}
        backButtonProps={{ onClick: closeModal }}
        closeButtonProps={{ className: classes.closeButton }}
      />

      <Flex className={classes.wrapper}>
        <SearchPageMembers
          onChange={onAddCollaborator}
          members={availableToChooseMembers}
          helperText={t('add_members_who_you_want_to_add_as_collaborator')}
        />
        <Flex className={classes.itemsWrapper}>
          {tempCollaborators?.map((item) => (
            <Flex className={classes.eachItem}>
              <JobCollaboratorItem
                key={item?.user?.id}
                onRemove={onRemoveHandler}
                item={item}
                isRemovable={isRemovable(item)}
              />
            </Flex>
          ))}
        </Flex>
        <Flex className={classes.spaceFiller} />
        <Button
          disabled={!isThereAChange}
          className={classes.submitButton}
          onClick={handleSubmit}
          label={t('done')}
        />
      </Flex>
    </FixedRightSideModalDialog>
  );
};
