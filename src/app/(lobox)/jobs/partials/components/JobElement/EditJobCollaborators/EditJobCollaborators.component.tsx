import React, { useMemo, useState } from 'react';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import FormGroupHeader from 'shared/uikit/Form/FormGroupHeader';
import Spinner from 'shared/uikit/Spinner';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { JobCollaboratorItem } from 'shared/components/molecules/JobCollaboratorCard/JobCollaborator.Card';
import { useJobCollaborators } from '@app/jobs/partials/hooks/useJobCollaborators';
import type { CollaboratorType } from 'shared/components/Organism/types';
import classes from './EditJobCollaborators.component.module.scss';
import { EditCollaborators } from './EditJobCollaborators.collaborators.modal';
import { EditAssociateModal } from './EditJobCollaborators.associate.modal';

type Props = {
  classes?: any;
  onChange?: (x: {
    collaborators: CollaboratorType[];
    associate: CollaboratorType;
  }) => void;
  value?: any;
};

export const EditJobCollaborators: React.FC<Props> = ({ onChange, value }) => {
  const [isAddCollaboratorModalOpen, setIsAddCollaboratorModalOpen] =
    useState(false);
  const [isChangeAssociateModalOpen, setIsChangeAssociateModalOpen] =
    useState(false);
  const { t } = useTranslation();

  const handleModal = useMemo(
    () => ({
      openAddCollaboratorModal: () => setIsAddCollaboratorModalOpen(true),
      closeAddCollaboratorModal: () => setIsAddCollaboratorModalOpen(false),
      openChangeAssociateModal: () => setIsChangeAssociateModalOpen(true),
      closeChangeAssociateModal: () => setIsChangeAssociateModalOpen(false),
    }),
    [setIsAddCollaboratorModalOpen, setIsChangeAssociateModalOpen]
  );

  const {
    collaborators = [],
    addCollaboratorCache,
    removeCollaboratorCache,
    associate,
    setAssociate,
    isLoading,
  } = useJobCollaborators(onChange);
  const hasMoreThanOneCollaborator = collaborators?.length > 1;

  function onNewCollaborators(newCollaborators: CollaboratorType[]) {
    const { addedUsers, removedUsers } = compareUserObjects(
      newCollaborators,
      collaborators
    );

    addedUsers?.forEach((collaborator) => {
      addCollaboratorCache(collaborator);
    });
    removedUsers?.forEach((collaborator) => {
      removeCollaboratorCache(collaborator);
    });

    onChange({ associate, collaborators: newCollaborators });
  }
  function onChangeAssociate(newAssociate: CollaboratorType) {
    setAssociate(newAssociate);
    onChange({ collaborators, associate: newAssociate });
  }

  return (
    <>
      {isLoading ? (
        <Spinner />
      ) : (
        <Flex>
          <FormGroupHeader icon="coworkers" title={t('collaborators')} />
          <Flex className={classes.itemsWrapper}>
            <Flex className={classes.itemWrapper}>
              {collaborators.map((item) => (
                <JobCollaboratorItem
                  key={item?.id}
                  item={item}
                  isRemovable={false}
                />
              ))}
            </Flex>
          </Flex>
          <Button
            label={t('edit_collaborators')}
            schema="semi-transparent"
            leftIcon={hasMoreThanOneCollaborator ? 'pen' : 'plus'}
            leftType="far"
            onClick={handleModal.openAddCollaboratorModal}
          />
          <Flex className={classes.spacerBetweenSections} />
          <FormGroupHeader icon="boss" title={t('associate')} />
          <Flex className={classes.itemsWrapper}>
            {!!associate && (
              <JobCollaboratorItem
                key={associate?.id}
                item={associate}
                isRemovable={false}
              />
            )}
          </Flex>

          <Button
            label={t('change_associate')}
            schema="semi-transparent"
            leftIcon="pen"
            leftType="far"
            onClick={handleModal.openChangeAssociateModal}
          />
        </Flex>
      )}
      {isAddCollaboratorModalOpen && (
        <EditCollaborators
          isOpen={isAddCollaboratorModalOpen}
          closeModal={handleModal.closeAddCollaboratorModal}
          onSubmit={onNewCollaborators}
          associate={associate}
          collaborators={collaborators}
        />
      )}
      {isChangeAssociateModalOpen && (
        <EditAssociateModal
          isOpen={isChangeAssociateModalOpen}
          closeModal={handleModal.closeChangeAssociateModal}
          onSelect={onChangeAssociate}
          associate={associate}
          collaborators={collaborators}
        />
      )}
    </>
  );
};

type RType<T> = {
  addedUsers: T;
  removedUsers: T;
};

export function compareUserObjects<T extends CollaboratorType[]>(
  newUsers: T,
  oldUsers: T
): RType<T> {
  const addedUsers: T = [] as T;
  const removedUsers: T = [] as T;
  newUsers?.forEach((newUser) => {
    if (oldUsers?.some((oldUser) => oldUser?.id === newUser?.id)) return;
    addedUsers.push(newUser);
  });
  oldUsers?.forEach((oldUser) => {
    if (newUsers?.some((newUser) => oldUser?.id === newUser?.id)) return;
    removedUsers.push(oldUser);
  });

  return { addedUsers, removedUsers };
}
