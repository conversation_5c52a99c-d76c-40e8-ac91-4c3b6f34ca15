import { useRef } from 'react';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateQueryData from 'shared/utils/hooks/useUpdateQueryData';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import type { BeforeCachePageDetailType } from 'shared/types/page';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import { ShareEntities, ShareEntityTab } from 'shared/types/share/entities';
import useOpenBlockConfirmModal from '@shared/hooks/useOpenBlockConfirmModal';

interface IUseJobCompanyInfoActions {
  pageDetail: BeforeCachePageDetailType;
}

const useJobCompanyInfoActions = ({
  pageDetail,
}: IUseJobCompanyInfoActions) => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const { getJobPropValue } = useJobElement();
  const pageId = getJobPropValue('pageId');
  const pageUserName = getJobPropValue('pageUsername');
  const queryKey = [QueryKeys.objectDetail, `${pageUserName}`];
  const { replace } = useUpdateQueryData(queryKey);
  const userHasBlockedThisCompanyRef = useRef(false);

  const onSuccessBlock = () => {
    userHasBlockedThisCompanyRef.current = true;
    replace({
      ...pageDetail,
      network: {
        ...pageDetail.network,
        follow: false,
      },
      youHaveBlocked: true,
    });
  };

  const handleSharePageViaPost = () =>
    appDispatch({
      type: 'SET_CREATE_POST_MODAL',
      payload: {
        isOpenModal: true,
        currentTab: 'main',
        attachment: {
          type: 'page',
          data: pageDetail,
        },
      },
    });

  const sharePage = () => {
    appDispatch({
      type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
      payload: {
        isOpen: true,
        tabs: [
          ShareEntityTab.COPY_LINK,
          ShareEntityTab.SHARE_VIA_POST,
          ShareEntityTab.SHARE_VIA_MESSAGE,
          ShareEntityTab.SHARE_VIA_EMAIL,
        ],
        entityData: {
          attachment: {
            type: ShareEntities.PAGE,
            data: pageDetail,
          },
        },
      },
    });
  };
  const openBlockConfirmModal = useOpenBlockConfirmModal();

  const openBlockConfirmation = () =>
    openBlockConfirmModal(pageDetail, onSuccessBlock);

  const toggleReportModal = () => {
    appDispatch({
      type: 'TOGGLE_REPORT_MODAL',
      payload: {
        isOpen: true,
        data: {
          entityType: 'page',
          entityId: pageId,
        },
      },
    });
  };

  const openInvitePeople = () => {
    openMultiStepForm({ formName: 'invitePeople' });
  };

  const shareViaPost = {
    icon: 'share',
    label: t('share_via_post'),
    onClick: handleSharePageViaPost,
  };
  const shareThePage = {
    icon: 'share',
    label: t('share_profile'),
    onClick: sharePage,
  };
  const invitePeople = {
    icon: 'user-plus',
    label: t('invite_people'),
    onClick: openInvitePeople,
  };
  const block = {
    icon: 'ban',
    label: t('block'),
    onClick: openBlockConfirmation,
  };
  const report = {
    icon: 'pennant',
    label: t('report_job'),
    onClick: toggleReportModal,
  };

  if (pageDetail?.youHaveBlocked) {
    return [shareViaPost, shareThePage, invitePeople, report];
  }

  return [shareViaPost, shareThePage, invitePeople, block, report];
};

export default useJobCompanyInfoActions;
