import React, { useState } from 'react';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import PopperMenu from 'shared/uikit/PopperMenu';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import AdvanceCard from 'shared/components/molecules/AdvanceCard/AdvanceCard.component';
import useObjectActions from 'shared/hooks/useObjectActions';
import ObjectInfoCard from 'shared/components/molecules/ObjectInfoCard';
import ObjectLink from 'shared/uikit/Link/ObjectLink';
import { ShareEntities } from '@shared/types/share/entities';
import JobMeetActionBottom from './JobCreatedBy.actions';
import classes from './JobCreatedBy.component.module.scss';

const JobCreatedBy = (): JSX.Element => {
  const { t } = useTranslation();
  const { getJobPropValue } = useJobElement();
  const pointOfContact = getJobPropValue('pointOfContact') || {};
  const network = getJobPropValue('pointOfContactNetwork');
  const userId = getJobPropValue('userId');
  const visibleAction = userId !== pointOfContact?.id;

  const [poc, setPoc] = useState({ ...pointOfContact, network });

  const onSuccess = (data) => () => {
    setPoc({
      ...pointOfContact,
      network: {
        ...network,
        ...data,
      },
    });
  };

  const actions = useObjectActions({
    objectId: pointOfContact?.id,
    fullName: pointOfContact?.fullName,
    onSuccess,
    shareEntityAttachment: {
      type: ShareEntities.PROFILE,
      data: pointOfContact,
    },
    objectDetail: pointOfContact,
  });
  const FirstTextWrapper = ({ children }) => (
    <ObjectLink
      className={classes.firstText}
      objectId={pointOfContact.id}
      username={pointOfContact.username}
    >
      {children}
    </ObjectLink>
  );
  const SecondTextWrapper = ({ children }) => (
    <ObjectLink
      className={classes.secondText}
      objectId={pointOfContact.id}
      username={pointOfContact.username}
    >
      {children}
    </ObjectLink>
  );

  return (
    <SectionLayout
      title={isBusinessApp ? t('created_by') : t('point_of_contact')}
      classNames={{
        childrenWrap: classes.childrenWrap,
      }}
    >
      <AdvanceCard
        classNames={{
          root: classes.jobCreatedByRoot,
          image: classes.image,
          innerRoot: classes.innerRoot,
          thirdTextWrapperClassName: classes.thirdText,
        }}
        data={{
          username: pointOfContact.username,
          image: pointOfContact.croppedImageUrl,
          objectId: pointOfContact.id,
          firstTextAdditionalProps: {
            objectId: pointOfContact.id,
          },
        }}
        ObjectInfoCard={
          <ObjectInfoCard
            firstText={pointOfContact.fullName}
            secondText={pointOfContact.usernameAtSign}
            thirdText={pointOfContact.occupation?.label}
            fourthText={pointOfContact.location?.title}
            FirstTextWrapper={FirstTextWrapper}
            SecondTextWrapper={SecondTextWrapper}
            isFirstTextSmall
            withAvatar={false}
          />
        }
        avatarProps={{ size: 'flg' }}
        imageDim={80}
        bottomAction={
          visibleAction && (
            <JobMeetActionBottom onSuccess={onSuccess} creator={poc} />
          )
        }
        action={
          visibleAction && (
            <PopperMenu
              placement="bottom-end"
              closeOnScroll
              buttonComponent={
                <IconButton
                  type="fas"
                  name="ellipsis-h"
                  size="md"
                  className={classes.withMargin}
                />
              }
            >
              <>
                {actions?.map(({ icon, label, onClick }) => (
                  <PopperItem
                    key={icon}
                    onClick={onClick}
                    iconName={icon}
                    iconType="far"
                    label={label}
                  />
                ))}
              </>
            </PopperMenu>
          )
        }
        firstTextProps={{
          size: 20,
          height: 22,
          color: 'smoke_coal',
          font: '700',
          isTruncated: true,
          isWordWrap: true,
          lineNumber: 1,
        }}
        secondTextProps={{
          size: 15,
          height: 18,
          font: '400',
          mt: 6,
          color: 'border',
          isTruncated: true,
          isWordWrap: true,
          lineNumber: 1,
        }}
        thirdTextProps={{
          size: 15,
          height: 17,
          font: '400',
          color: 'primaryText',
          isTruncated: true,
          isWordWrap: true,
          lineNumber: 1,
          mt: 8,
        }}
      />
    </SectionLayout>
  );
};

export default JobCreatedBy;
