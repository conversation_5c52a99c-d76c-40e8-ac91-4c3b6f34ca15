import React, { useEffect } from 'react';
import Form from 'shared/uikit/Form';
import useMedia from 'shared/uikit/utils/useMedia';
import useToast from 'shared/uikit/Toast/useToast';
import Flex from 'shared/uikit/Flex';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import formValidator from 'shared/utils/form/formValidator';

import jobsApi from 'shared/utils/api/jobs';
import { routeNames } from 'shared/utils/constants/routeNames';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';

import useTranslation from 'shared/utils/hooks/useTranslation';
import useHistory from 'shared/utils/hooks/useHistory';
import {
  useJobsDispatch,
  useJobsState,
} from 'shared/providers/JobsPorvider/jobs.provider';
import type { IJob } from 'shared/types/job';
import useResponseToast from 'shared/hooks/useResponseToast';
import StepOne from './partials/Steps/StepOne';
import StepTwo from './partials/Steps/StepTwo';
import StepThree from './partials/Steps/StepThree';
import StepFour from './partials/Steps/StepFour';
import { transformJobCreationForm } from './partials/JobCreationForm.utils';
import StepFive from './partials/Steps/StepFive';
import classes from './JobCreationForm.component.module.scss';

const JobCreationForm: React.FC = () => {
  const {
    activeStep,
    getJobPropValue,
    isCreationMode,
    setActiveStep,
    queryResult,
  } = useJobElement();
  const history = useHistory();
  const toast = useToast();
  const { handleError } = useResponseToast();
  const { t } = useTranslation();
  const jobsDispatch = useJobsDispatch();
  const location = getJobPropValue('location');

  const initialValues = isCreationMode
    ? { languageId: getJobPropValue('languageId') || '1' }
    : {
        languageId: getJobPropValue('languageId'),
        id: getJobPropValue('id'),
        title: getJobPropValue(['title']),
        websiteUrl: getJobPropValue('websiteUrl'),
        workPlaceType: getJobPropValue('workPlaceType'),
        isAddExternalJobLink: getJobPropValue('isAddExternalJobLink'),
        category: getJobPropValue('category'),
        description: getJobPropValue('description'),
        employmentType: getJobPropValue('employmentType'),
        experienceLevel: getJobPropValue('experienceLevel'),
        skills: getJobPropValue('skills'),
        location: location ? [{ location }] : [{ location: {} }],
        languages: getJobPropValue('languages'),
        questions: getJobPropValue('questions'),
        salaryCurrency: getJobPropValue('salaryCurrency'),
        salaryRangeMax: getJobPropValue('salaryRangeMax'),
        salaryRangeMin: getJobPropValue('salaryRangeMin'),
        hashtags: getJobPropValue('hashtags'),
        salaryPeriod: getJobPropValue('salaryPeriod'),
      };
  Object.keys(initialValues)?.forEach((key) => {
    const _key = key as keyof typeof initialValues;
    if (initialValues[_key] === null) initialValues[_key] = undefined;
  });

  const onSuccessHandler = (data: IJob) => {
    setActiveStep(1);
    jobsDispatch({ type: 'SET_IS_JOB_FORM_OPEN', payload: false });

    if (isCreationMode) {
      // const owner = getJobPropValue('owner');
      // const status = getJobPropValue('status');
      // const updatedValues = {
      //   ...formRef?.values,
      //   ownerId: owner?.id,
      //   owner,
      //   status,
      // };
      // setJobCreationFormData(updatedValues);
      history.push(routeNames.jobDetails.makeRoute(data?.id));
      toast({
        type: 'success',
        icon: 'check-circle',
        message: t('job_add_success'),
      });
    } else {
      toast({
        type: 'success',
        icon: 'check-circle',
        message: t('job_updated_success'),
      });
    }
    setTimeout(() => {
      queryResult.refetch();
    }, 250);
  };

  const validationSchema: Record<number, any> = {
    1: () =>
      formValidator.object().shape({
        websiteUrl: formValidator.string().when('isAddExternalJobLink', {
          is: (val: boolean) => !!val,
          then: formValidator
            .string()
            .required()
            .matches(
              /((https?):\/\/)?(www.)?[a-z0-9-]+(\.[a-z]{2,}){1,3}(#?\/?[a-zA-Z0-9#-]+)*\/?(\?[a-zA-Z0-9-_]+=[a-zA-Z0-9-%]+&?)?$/,
              'enter_valid_url'
            ),
          otherwise: formValidator.string(),
        }),
        category: formValidator
          .object()
          .test('value', 'select_one_of_sug_cate', (val) => val?.value),
      }),

    2: () =>
      formValidator.object().shape({
        description: formValidator.string().required(),
      }),
    3: () =>
      formValidator
        .object()
        .shape({
          salaryCurrency: formValidator
            .object()
            .test('value', 'this_field_can_not_contain_a_number', (val) =>
              val ? !/\d/.test(val?.label as string) : true
            )
            .test('value', 'select_one_of_sug_currencies', (val) =>
              val ? !!val?.value : true
            )
            .notRequired(),
          salaryRangeMax: formValidator
            .number()
            .positive()
            .when('salaryRangeMin', (salaryRangeMin, field) =>
              salaryRangeMin
                ? field.min(salaryRangeMin, 'max_must_be_more_than_min')
                : field
            ),
          salaryRangeMin: formValidator.number().positive(),
          salaryPeriod: formValidator.object(),
        })
        .test('allTogetherOrNone', '', (values, obj) => {
          const {
            salaryCurrency,
            salaryRangeMax,
            salaryRangeMin,
            salaryPeriod,
          } = values;
          const allFilled = Boolean(
            salaryCurrency && salaryRangeMax && salaryRangeMin && salaryPeriod
          );
          const allEmpty = Boolean(
            !salaryCurrency &&
              !salaryRangeMax &&
              !salaryRangeMin &&
              !salaryPeriod
          );
          if (allFilled || allEmpty) return true;
          return obj.createError({
            path: 'NOT_IMPORTANT',
            message: '',
          });
        }),
  };

  return (
    <>
      <Form
        initialValues={initialValues}
        apiFunc={isCreationMode ? jobsApi.createJob : jobsApi.updateJob}
        onSuccess={onSuccessHandler}
        transform={transformJobCreationForm}
        validationSchema={validationSchema[activeStep]}
        onFailure={handleError}
      >
        {() => (
          <>
            {activeStep === 1 && <StepOne classes={classes} />}
            {activeStep === 2 && <StepTwo classes={classes} />}
            {activeStep === 3 && <StepThree classes={classes} />}
            {activeStep === 4 && <StepFour classes={classes} />}
            {activeStep === 5 && <StepFive classes={classes} />}
          </>
        )}
      </Form>
      {!isCreationMode && <Flex className={classes.layover} />}
    </>
  );
};

export default JobCreationForm;
