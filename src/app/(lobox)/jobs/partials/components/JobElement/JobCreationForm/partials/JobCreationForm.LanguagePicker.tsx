import React from 'react';
import Typography from 'shared/uikit/Typography';
import Flex from 'shared/uikit/Flex';
import HeroIcon from 'shared/uikit/HeroIcon';
import cnj from 'shared/uikit/utils/cnj';
import { jobsDb, } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Dropdown from 'shared/components/molecules/Dropdown';
import classes from './JobCreationForm.LanguagePicker.module.scss';

export interface JobCreationFormLanguagePickerProps {
  className?: string;
  value: string;
  onChange: (val: any) => void;
}

const JobCreationFormLanguagePicker: React.FC<
  JobCreationFormLanguagePickerProps
> = ({ className, value, onChange }) => {
  const { t } = useTranslation();

  return (
    <Flex className={cnj(classes.jobFormLanguagePickerRoot, className)}>
      <HeroIcon
        variant="square"
        className={classes.icon}
        color="brand"
        iconName="globe"
        size={32}
        iconSize={20}
      />
      <Typography mr={8}>{t('choose_lng')}:</Typography>
      <Dropdown onChange={onChange} options={jobsDb.LANGUAGES} value={value} />
    </Flex>
  );
};

export default JobCreationFormLanguagePicker;
