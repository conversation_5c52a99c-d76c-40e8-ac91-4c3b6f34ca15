import React, { useEffect } from 'react';
import Button from 'shared/uikit/Button';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import ProgressBar from 'shared/uikit/ProgressBar';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import useMedia from 'shared/uikit/utils/useMedia';
import useTheme from 'shared/uikit/utils/useTheme';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import { useFormikContext } from 'formik';
import { useGlobalState } from 'shared/contexts/Global/global.provider';
import useHistory from 'shared/utils/hooks/useHistory';
import { motion } from 'framer-motion';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useJobsDispatch } from 'shared/providers/JobsPorvider/jobs.provider';
import type { ActiveStepType } from '@app/jobs/partials/context/JobElement/JobElement.reducer';
import classes from './JobCreationForm.layout.module.scss';

export interface JobCreationFormLayoutProps {
  className?: string;
}

const JobCreationFormLayout: React.FC<JobCreationFormLayoutProps> = ({
  children,
}) => {
  const { t } = useTranslation();
  const formContext = useFormikContext();
  const history = useHistory();
  const jobsDispatch = useJobsDispatch();
  const { isDark } = useTheme();
  const { isTabletAndLess } = useMedia();
  const { values, setErrors, status, setStatus, resetForm } =
    useFormikContext();

  const {
    isCreationMode,
    isPreviewMode,
    activeStep,
    setActiveStep,
    togglePreviewMode,
    setJobCreationFormData,
  } = useJobElement();
  const isAddExternalJobLink = formContext?.values?.isAddExternalJobLink;
  const steps = isAddExternalJobLink ? 2 : 5;
  const isCreateEntityPanelOpen = useGlobalState('isCreateEntityPanelOpen');

  const isNextButtonDisabled = isCreationMode
    ? (!formContext.dirty && status !== 'PREVIOUS') || !formContext.isValid
    : !formContext.isValid;
  const visibleRequiredHint = activeStep === 1 || activeStep === 2;

  useEffect(() => {
    if (isCreationMode) {
      setJobCreationFormData(values);
    }
  }, [values]);

  const handleClose = () => {
    setActiveStep(1);
    jobsDispatch({ type: 'SET_IS_JOB_FORM_OPEN', payload: false });
    if (isCreationMode) {
      history.goBack();
    }
  };
  const onClickNextHandler = () => {
    const formValues = { ...values };
    if (activeStep === 1 && status !== 'PREVIOUS') {
      resetForm({
        values: formValues,
        status: !isAddExternalJobLink ? 'NEXT' : {},
      });
    } else if (activeStep === steps - 1) {
      setStatus({});
    }
    setActiveStep((activeStep + 1) as ActiveStepType);
  };
  const onClickPrevHandler = () => {
    setErrors({});
    setStatus('PREVIOUS');
    setActiveStep((activeStep - 1) as ActiveStepType);
  };
  const onBackHandler = () => {
    if (activeStep !== 1) return onClickPrevHandler();

    return handleClose();
  };
  const previewHandler = () => {
    togglePreviewMode(true);
  };

  return (
    <FixedRightSideModalDialog
      modalClassName={cnj(isPreviewMode && classes.hideWrapper)}
      backdropClassName={cnj(isPreviewMode && classes.hideWrapper)}
      onBack={onBackHandler}
      onClose={handleClose}
      showConfirm={formContext?.dirty}
      // onClickOutside={handleClose}
    >
      <ModalHeaderSimple
        className={classes.header}
        hideBack={!isCreateEntityPanelOpen && activeStep === 1}
        backButtonProps={{ onClick: handleClose }}
        noCloseButton={isCreateEntityPanelOpen && activeStep === 1}
        visibleHeaderDivider={isTabletAndLess}
        title={isCreationMode ? t('create_job') : t('edit_job')}
      />
      <Flex className={classes.topFields}>
        <ProgressBar value={activeStep} steps={steps} />
      </Flex>
      <ModalBody className={classes.modalBody}>
        <motion.div
          id="modalMotion"
          key={isAddExternalJobLink ? '1' : '2'}
          initial={isAddExternalJobLink ? 'hidden' : 'visible'}
          animate={isAddExternalJobLink ? 'visible' : 'hidden'}
          variants={
            isAddExternalJobLink
              ? {
                  visible: { originX: 0.5, opacity: 1, scale: 1 },
                  hidden: { originX: 0, opacity: 0, scale: 0.95 },
                }
              : {
                  visible: { originX: 0, opacity: 0, scale: 0.95 },
                  hidden: { originX: 0.5, opacity: 1, scale: 1 },
                }
          }
          transition={{ duration: 0.2 }}
        >
          {children}
        </motion.div>
      </ModalBody>

      <ModalFooter className={classes.footer}>
        {activeStep !== 1 && (
          <Flex className={classes.prevWrapper}>
            <Button
              fullWidth
              schema={isDark ? 'ghost-black' : 'secondary-light'}
              onClick={onClickPrevHandler}
              label={t('previous')}
            />
          </Flex>
        )}
        {activeStep === steps ? (
          <SubmitButton
            active={!isCreationMode}
            className={cnj(classes.nextWrapper, classes.paddingLeft)}
            label={t('save')}
          />
        ) : (
          <Flex
            className={cnj(
              classes.nextWrapper,
              activeStep !== 1 && classes.paddingLeft
            )}
          >
            <Button
              disabled={isNextButtonDisabled}
              fullWidth
              onClick={onClickNextHandler}
              label={t('next')}
            />
          </Flex>
        )}
      </ModalFooter>
    </FixedRightSideModalDialog>
  );
};

export default JobCreationFormLayout;
