export const transformJobCreationForm = (data: any) => {
  const {
    languages,
    employmentType,
    location,
    skills,
    category,
    experienceLevel,
    salaryCurrency,
    salaryPeriod,
    title,
    workPlaceType,
    owners,
    salaryRangeMax,
    salaryRangeMin,
    ...rest
  } = data;
  const transformed = {
    ...rest,
    workPlaceType: workPlaceType.value,
    title: title?.label,
    titleId: title?.value,
    salaryCurrencyId: salaryCurrency?.value,
    salaryCurrencyName: salaryCurrency?.name,
    salaryCurrencySymbol: salaryCurrency?.symbol,
    salaryCurrencyCode: salaryCurrency?.code,
    salaryPeriod: salaryPeriod?.value,
    experienceLevel: experienceLevel?.value,
    employmentType: employmentType?.value,
    categoryId: category?.value,
    categoryName: category?.label,
    skills: skills?.map((item: any) => ({
      skillId: item.id,
      skillType: item.type,
      skillLevel: item.skillLevel,
      skillName: item.label,
    })),
    location: {
      lang: location?.[0]?.location?.lang,
      placeId: location?.[0]?.location?.value,
    },
    languages: languages?.map((item: any) => ({
      languageId: item.id,
      languageLevel: item.languageLevel,
      languageName: item.label,
    })),
    salaryRangeMax: salaryRangeMax ? Number(salaryRangeMax) : undefined,
    salaryRangeMin: salaryRangeMin ? Number(salaryRangeMin) : undefined,

    owners,
  };
  return transformed;
};
