import React from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import { useFormikContext } from 'formik';
import type { CollaboratorType } from 'shared/components/Organism/types';
import { useRevalidateFormOnComponentMount } from '@app/jobs/partials/hooks/useRevalidateFormOnComponentMount';
import JobCreationFormLayout from '../../JobCreationForm.layout';
import { EditJobCollaborators } from '../../../../EditJobCollaborators/EditJobCollaborators.component';

type Props = {
  classes: any;
};
type OnChangeType = (x: {
  collaborators: CollaboratorType[];
  associate: CollaboratorType;
}) => void;

export const StepFive: React.FC<Props> = ({ classes }) => {
  const { setFieldValue, values } = useFormikContext();

  useRevalidateFormOnComponentMount();

  const onChange: OnChangeType = ({ collaborators, associate }) => {
    setFieldValue(
      'collaboratorUserIds',
      collaborators
        ?.filter((item) => item?.collaboratorRole !== 'CREATOR')
        ?.map((item) => item?.user?.id)
    );
    setFieldValue('associateUserId', associate?.user?.id);
  };

  const groups = [
    {
      name: 'jobCollaborators',
      cp: (props: any) => <EditJobCollaborators {...props} />,
      classes,
      onChange,
    },
  ];

  return (
    <JobCreationFormLayout>
      <DynamicFormBuilder groups={groups} />
    </JobCreationFormLayout>
  );
};
