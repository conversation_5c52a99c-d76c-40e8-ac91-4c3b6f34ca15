import React from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useRevalidateFormOnComponentMount } from '@app/jobs/partials/hooks/useRevalidateFormOnComponentMount';
import JobCreationFormLayout from '../../JobCreationForm.layout';

export interface StepOneComponentProps {
  classes: any;
}

const StepFour: React.FC<StepOneComponentProps> = ({ classes }) => {
  const { t } = useTranslation();

  useRevalidateFormOnComponentMount();

  const groups = [
    {
      formGroup: {
        title: t('questions'),
        icon: 'map-marker-alt',
      },
      name: 'questions',
      cp: 'jobQuestionPicker',
    },
  ];

  return (
    <JobCreationFormLayout>
      <DynamicFormBuilder groups={groups} />
    </JobCreationFormLayout>
  );
};

export default StepFour;
