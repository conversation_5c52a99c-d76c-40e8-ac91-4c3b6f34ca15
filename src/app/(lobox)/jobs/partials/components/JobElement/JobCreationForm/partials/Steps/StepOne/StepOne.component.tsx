import React from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import { db } from 'shared/utils/constants/enums';
import Endpoints from 'shared/utils/constants/endpoints';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import { useFormikContext } from 'formik';
import { useRevalidateFormOnComponentMount } from '@app/jobs/partials/hooks/useRevalidateFormOnComponentMount';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import type { Language } from 'shared/types/language';
import JobCreationFormLayout from '../../JobCreationForm.layout';
import JobCreationFormLanguagePicker from '../../JobCreationForm.LanguagePicker';

export interface StepOneComponentProps {
  classes: any;
}

const StepOne: React.FC<StepOneComponentProps> = ({
  classes: parentClasses,
}) => {
  const { t } = useTranslation();
  const { jobLanguage } = useJobElement();
  const { setFieldValue } = useFormikContext();
  const { authUser } = useGetAppObject();
  const countryCode = authUser.location?.countryCode;

  const onChangeLanguage = (lng: Language) => {
    setFieldValue('languageId', lng?.id);
  };

  useRevalidateFormOnComponentMount();

  const groups = [
    {
      name: 'lng',
      cp: () => (
        <JobCreationFormLanguagePicker
          onChange={onChangeLanguage}
          value={jobLanguage}
        />
      ),
    },
    {
      name: 'websiteUrl',
      cp: 'addExternalJobLink',
      className: 'responsive-margin-top',
    },
    {
      formGroup: {
        title: t('overview'),
        icon: 'address-card',
        className: 'responsive-margin-top',
      },
      name: 'workPlaceType',
      cp: 'jobWorkPlaceType',
      required: true,
    },
    {
      label: t('job_title'),
      required: true,
      name: 'title',
      cp: 'asyncAutoComplete',
      maxLength: 100,
      url: Endpoints.App.Common.getOccupations,
      normalizer: lookupResponseNormalizer,
      wrapStyle: parentClasses.formItem,
    },
    {
      name: 'employmentType',
      cp: 'dropdownSelect',
      label: t('job_type'),
      required: true,
      wrapStyle: parentClasses.formItem,
      options: db.EMPLOYMENT_TYPES,
    },
    {
      name: 'category',
      label: t('category'),
      cp: 'asyncAutoComplete',
      required: true,
      maxLength: 100,
      url: Endpoints.App.Common.getIndustry,
      normalizer: (data: any) =>
        data.map(({ id: value, title: label }: any) => ({
          label,
          value,
        })),
      wrapStyle: parentClasses.formItem,
      visibleRightIcon: true,
      rightIconProps: { name: 'search' },
    },
    {
      formGroup: {
        title: t('location'),
        icon: 'map-marker-alt',
        className: parentClasses.formGroup,
      },
      name: 'location',
      apiParams: { countryCode },
      cp: 'cityPicker',
      required: true,
    },
  ].filter(Boolean);

  return (
    <JobCreationFormLayout>
      <DynamicFormBuilder groups={groups} />
    </JobCreationFormLayout>
  );
};

export default StepOne;
