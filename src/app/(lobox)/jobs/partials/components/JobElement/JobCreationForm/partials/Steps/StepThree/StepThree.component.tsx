import React from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Endpoints from 'shared/utils/constants/endpoints';
import { jobsDb, } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useRevalidateFormOnComponentMount } from '@app/jobs/partials/hooks/useRevalidateFormOnComponentMount';
import JobCreationFormLayout from '../../JobCreationForm.layout';

export interface StepThreeComponentProps {
  classes: any;
}

const StepThree: React.FC<StepThreeComponentProps> = ({ classes }) => {
  const { t } = useTranslation();

  useRevalidateFormOnComponentMount();

  const experienceLevelField = {
    formGroup: {
      title: t('exp_level'),
      icon: 'signal',
    },
    name: 'experienceLevel',
    cp: 'dropdownSelect',
    label: t('exp_level'),
    // required: true,
    // wrapStyle: classes.formItem,
    options: jobsDb.experienceLevels,
  };

  const salaryCurrencyField = {
    formGroup: {
      title: t('salary_rng'),
      icon: 'usd-circle',
      className: classes.formGroup,
    },
    name: 'salaryCurrency',
    cp: 'asyncAutoComplete',
    label: t('currency'),
    visibleRightIcon: true,
    rightIconProps: { name: 'search' },
    url: Endpoints.App.Common.searchCurrency,
    normalizer: (data: any) =>
      data?.map(({ id: value, name, symbol, code }: any) => ({
        label: `${name} (${symbol})`,
        name,
        symbol,
        value,
        code,
      })),
    forceVisibleError: true,
  };
  const salaryRangeMinField = {
    name: 'salaryRangeMin',
    cp: 'input',
    label: t('min_salary'),
    wrapStyle: classes.formItem,
    type: 'number',
    forceVisibleError: true,
  };
  const salaryRangeMaxField = {
    name: 'salaryRangeMax',
    cp: 'input',
    label: t('max_salary'),
    wrapStyle: classes.formItem,
    type: 'number',
    forceVisibleError: true,
  };
  const salaryPeriodField = {
    name: 'salaryPeriod',
    cp: 'dropdownSelect',
    label: t('salary_period'),
    wrapStyle: classes.formItem,
    options: jobsDb.SALARY_PERIOD,
  };

  const groups = [
    experienceLevelField,
    salaryCurrencyField,
    salaryRangeMinField,
    salaryRangeMaxField,
    salaryPeriodField,
    {
      formGroup: {
        title: t('skills'),
        icon: 'medal',
        className: classes.formGroup,
      },
      name: 'skills',
      cp: 'skillPicker',
    },
    {
      formGroup: {
        title: t('languages'),
        icon: 'language',
        className: classes.formGroup,
      },
      name: 'languages',
      cp: 'languagePicker',
    },
  ];

  return (
    <JobCreationFormLayout>
      <DynamicFormBuilder groups={groups} />
    </JobCreationFormLayout>
  );
};

export default StepThree;
