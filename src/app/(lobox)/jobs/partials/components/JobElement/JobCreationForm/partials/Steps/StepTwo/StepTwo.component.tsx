import React, { useEffect } from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import RichTextEditor from 'shared/uikit/RichTextEditor';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import { useFormikContext } from 'formik';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Separator from 'shared/components/molecules/Separator';
import TemplateButton from 'shared/components/molecules/TemplateButton';
import { useRevalidateFormOnComponentMount } from '@app/jobs/partials/hooks/useRevalidateFormOnComponentMount';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import isEqual from 'lodash/isEqual';
import startCase from 'lodash/startCase';
import JobCreationFormLayout from '../../JobCreationForm.layout';
import _classes from './StepTwo.component.module.scss';

export interface StepOneComponentProps {
  classes: any;
}

const forbiddenCharacters = /[^ !@#$%^&*(),.?":{}|<>-]+/giu;

const StepTwo: React.FC<StepOneComponentProps> = ({ classes }) => {
  const { values, setValues } = useFormikContext<any>();
  const { t } = useTranslation();
  const { isCreationMode } = useJobElement();
  const { businessPage } = useGetAppObject();
  useRevalidateFormOnComponentMount();

  const descriptionField = {
    formGroup: {
      title: t('details'),
      icon: 'info-circle',
      className: values?.isAddExternalJobLink ? classes.formGroup : undefined,
    },
    name: 'description',
    cp: RichTextEditor,
    title: t('description'),
    label: t('description'),
    maxLength: DESCRIPTION_MAX_LENGTH,
    visibleCharCounter: true,
    required: true,
    showTitle: false,
    classNames: { wrapper: _classes.editorWrapper },
  };

  const templateModalField = {
    name: 'descriptionButton',
    cp: TemplateButton,
  };

  const SeparatorComponent = () => <Separator title={t('or')} />;
  const separatorField = {
    cp: SeparatorComponent,
    name: 'separator',
  };

  const hashtagField = {
    name: 'hashtags',
    cp: 'hashtagPicker',
    tooltip: t('hashtag_tooltip'),
  };

  const groups = [
    descriptionField,
    separatorField,
    templateModalField,
    hashtagField,
  ];

  useEffect(() => {
    // Set default hashtags
    if (!isCreationMode) return;
    if (typeof values?.hashtags?.length === 'number') return;
    const jobTitle = startCase(
      values?.title?.label?.match(forbiddenCharacters)?.join('') || ''
    );
    const pageTitle = startCase(
      businessPage?.fullName?.match(forbiddenCharacters)?.join('') || ''
    );
    const defaultHashtags = [
      jobTitle,
      pageTitle,
      startCase(jobTitle) + startCase(pageTitle),
    ];
    if (isEqual(defaultHashtags, values?.hashtags)) return;
    setValues({ ...values, hashtags: defaultHashtags });
  }, [values, isCreationMode, businessPage, setValues]);

  return (
    <JobCreationFormLayout>
      <DynamicFormBuilder groups={groups} />
    </JobCreationFormLayout>
  );
};

export default StepTwo;
