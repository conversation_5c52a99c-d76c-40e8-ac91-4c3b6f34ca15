import React from 'react';
import RichTextEditor from 'shared/uikit/RichTextEditor';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Flex from '@shared/uikit/Flex/index';
import OverflowTip from '@shared/uikit/Typography/OverflowTip';
import { useObjectClicks } from '@shared/hooks/useObjectClicks';
import classes from './JobDescription.components.module.scss';

const JobDescription = (): JSX.Element => {
  const { t } = useTranslation();
  const { getJobPropValue, hasEditAccess } = useJobElement();
  const description = getJobPropValue('description');
  const hashtags = getJobPropValue('hashtags');
  const { handleHashtagClick } = useObjectClicks();

  return (
    <SectionLayout
      title={t('details')}
      classNames={{ childrenWrap: classes.childrenWrap }}
      visibleActionButton={false}
    >
      <InfoCard
        disabledHover
        subTitle={hasEditAccess ? t('add_description') : t('no_desc_ent')}
        value={
          description ? (
            <RichTextEditor
              value={description}
              readOnly
              styles={classes.editor}
              classNames={{ wrapper: classes.editorWrapper }}
              jobCreation
            />
          ) : undefined
        }
      />

      {(hashtags?.length || hasEditAccess) && (
        <InfoCard
          disabledHover
          title={t('hashtags')}
          subTitle={t('add_hashtag')}
          wrapperClassName={classes.wrapperClassName}
          value={
            <Flex flexDir="row">
              {hashtags?.map((item: string) => (
                <OverflowTip
                  size={15}
                  height={21}
                  onClick={() => handleHashtagClick(item)}
                  className={classes.singleHashtag}
                  key={item}
                >
                  {`#${item}`}&nbsp;
                </OverflowTip>
              ))}
            </Flex>
          }
          icon="hashtag1"
          iconSize={16}
          valueProps={{
            isWordWrap: true,
          }}
        />
      )}
    </SectionLayout>
  );
};

export default JobDescription;
