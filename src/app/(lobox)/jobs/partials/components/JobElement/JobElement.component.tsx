import React, { useEffect, useRef } from 'react';
import Button from 'shared/uikit/Button';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import useMedia from 'shared/uikit/utils/useMedia';
import { jobStatusKeys } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import useHistory from 'shared/utils/hooks/useHistory';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import { useJobsDispatch } from 'shared/providers/JobsPorvider/jobs.provider';
import type { ScrollViewElementType } from '@shared/types/scrollView';
import ProfileModalsProvider from 'shared/contexts/profileModals.provider';
import { useMultiStepFormState } from 'shared/hooks/useMultiStepForm';
import dynamic from 'next/dynamic';
import Skeleton from 'shared/uikit/Skeleton';
import JobOverview from './JobOverview';
import UnpublishedJobNotice from './UnpublishedJobNotice';
import JobElementHeader from './JobElementHeader';
import classes from './JobElement.component.module.scss';

const JobApplicationForm = dynamic(
  () => import('@app/jobs/partials/components/JobApplicationForm'),
  {
    ssr: false,
  }
);

const JobLocation = dynamic(() => import('./JobLocation'), {
  ssr: false,
  loading: () => <Skeleton className="h-[600px]" />,
});

const JobSkills = dynamic(() => import('./JobSkills'), {
  ssr: false,
});

const JobLanguage = dynamic(() => import('./JobLanguages'), {
  ssr: false,
});

const JobDescription = dynamic(() => import('./JobDescription'), {
  ssr: false,
  loading: () => <Skeleton className="!h-[600px]" />,
});

const JobCompanyInfo = dynamic(() => import('./JobCompanyInfo'), {
  ssr: false,
  loading: () => <Skeleton className="h-[300px]" />,
});

const JobCreatedBy = dynamic(() => import('./JobCreatedBy'), {
  ssr: false,
});

const JobCreationForm = dynamic(() => import('./JobCreationForm'), {
  ssr: false,
});

const JobIdSection = dynamic(() => import('./JobIdSection'), {
  ssr: false,
});

const JobElement: React.FC = () => {
  const { t } = useTranslation();
  const { isMoreThanTablet, isMidDesktop } = useMedia();
  const history = useHistory();
  const scrollRef = useRef<ScrollViewElementType>(null);
  const jobsDispatch = useJobsDispatch();
  const isJobApplicationFormOpen =
    useMultiStepFormState('jobApplication').isOpen;
  const {
    isJobCreationFormOpen,
    isCreationMode,
    togglePreviewMode,
    getJobPropValue,
  } = useJobElement();

  const visibleUnPublishNotice =
    getJobPropValue('status')?.value === jobStatusKeys.unpublished;
  const isRightColumnHidden =
    isMidDesktop && (isJobApplicationFormOpen || isJobCreationFormOpen);
  const isAddExternalJobLink = getJobPropValue('isAddExternalJobLink');

  useEffect(() => {
    jobsDispatch({
      type: 'SET_SCROLL_REF',
      payload: scrollRef,
    });
  }, [scrollRef]);

  const toggleJobCreationForm = (value: boolean) => {
    jobsDispatch({ type: 'SET_IS_JOB_FORM_OPEN', payload: value });
  };

  useEffect(() => {
    if (isCreationMode) {
      toggleJobCreationForm(true);
    }
  }, [isCreationMode]);

  const editJobHandler = () => {
    togglePreviewMode(false);
    toggleJobCreationForm(true);
  };
  const onBackHandler = () => {
    history.goBack();
  };

  return (
    <>
      <ModalHeaderSimple
        visibleHeaderDivider
        className={classes.mobileHeader}
        title={
          (isCreationMode && t('create_job')) ||
          (isJobApplicationFormOpen && t('application')) ||
          t('job_details')
        }
        backButtonProps={{
          onClick: onBackHandler,
        }}
        rightContent={() =>
          isBusinessApp &&
          isCreationMode && (
            <Button
              className={classes.rightContent}
              schema="ghost-brand"
              onClick={editJobHandler}
              leftIcon="pen"
              leftType="far"
              label={t('edit')}
            />
          )
        }
      />
      {visibleUnPublishNotice && <UnpublishedJobNotice />}
      <Flex className={classes.profileRoot} ref={scrollRef}>
        <JobElementHeader
          backButtonProps={{
            className: classes.hideBack,
            onClick: onBackHandler,
          }}
        />
        <Flex className={classes.jobElementRoot}>
          <Flex className={classes.wrapper}>
            <Flex
              className={cnj(
                classes.left,
                isRightColumnHidden && classes.rightColumnHiddenLeft
              )}
            >
              {(!isMoreThanTablet || isRightColumnHidden) && <JobOverview />}
              <JobDescription />
              {!isJobApplicationFormOpen && !isAddExternalJobLink && (
                <JobSkills />
              )}
              {!isJobApplicationFormOpen && !isAddExternalJobLink && (
                <JobLanguage />
              )}
              <ProfileModalsProvider>
                <JobCreatedBy />
              </ProfileModalsProvider>
              <JobLocation />

              {!isCreationMode && !isBusinessApp && <JobCompanyInfo />}
              {!isCreationMode && isBusinessApp && <JobIdSection />}
            </Flex>
            {!isRightColumnHidden && (
              <Flex className={classes.right}>
                {isMoreThanTablet && <JobOverview />}
              </Flex>
            )}
          </Flex>
        </Flex>
        {isJobCreationFormOpen && <JobCreationForm />}
        {isJobApplicationFormOpen && <JobApplicationForm />}
      </Flex>
    </>
  );
};

export default JobElement;
