import React, { useState } from 'react';
import Button from 'shared/uikit/Button';
import Divider from 'shared/uikit/Divider';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import PopperMenu from 'shared/uikit/PopperMenu';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import dateFromNow from 'shared/utils/toolkit/dateFromNow';
import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import urls from 'shared/constants/urls';
import type { JobStatusType } from 'shared/types/job';
import {
  JobsSaveButton,
  JobApplyButton,
  JobStatusButton,
  JobTrackButton,
} from '@shared/components/molecules/JobButtons';
import useMedia from 'shared/uikit/utils/useMedia';
import { FallbackText } from '@shared/components/atoms/FallbackText';
import useJobElementHeaderActions from './JobElementHeader.useActions';
import JobElementHeaderSkeleton from './JobElementHeader.skeleton';
import JobCover from '../../JobCover';
import classes from './JobElementHeader.component.module.scss';
import JobTrackingModal from './JobTrackingModal';

interface JobElementHeaderProps {
  className?: string;
  backButtonProps?: { className?: string; onClick?: () => void };
  disableActions?: boolean;
}

const JobElementHeader: React.FC<JobElementHeaderProps> = ({
  className,
  backButtonProps,
  disableActions = false,
}) => {
  const { t } = useTranslation();
  const {
    isCreationMode,
    getJobPropValue,
    updateJobData,
    hasEditAccess,
    isPublished,
    queryResult,
    jobElement: job,
  } = useJobElement();

  const { isMoreThanTablet } = useMedia();
  const jobId = getJobPropValue('id');
  const visibleApplied = getJobPropValue('isApplied');
  const applicantsCount = getJobPropValue('applicantsCount');
  const skills = getJobPropValue('skills');
  const isNew = getJobPropValue('isNew');
  const viewCount = getJobPropValue('viewCount');
  const actions = useJobElementHeaderActions();
  const { getAppObjectPropValue } = useGetAppObject();
  const location = getJobPropValue('location');
  const fullName = getAppObjectPropValue({
    pageKey: 'title',
    userKey: 'fullName',
  });
  const imgSrc = getAppObjectPropValue({
    userKey: 'croppedImageUrl',
    pageKey: 'croppedImageUrl',
  });
  const date = dateFromNow(getJobPropValue('createdDate'), false, t);
  const createdDate = date?.match(/^(\d+)\s*(.*)$/);
  const pageTitle = getJobPropValue('pageTitle');
  const secondText = isCreationMode && !pageTitle ? fullName : pageTitle;
  const pageImage = getJobPropValue('pageCroppedImageUrl');
  const firstText = getJobPropValue(['title', 'label']);
  const image = isCreationMode && !pageImage ? imgSrc : pageImage;
  const isLoggedIn = useAuthState('isLoggedIn');
  const thirdText = getJobPropValue('category')?.label;

  const [openTrackingModal, setOpenTrackingModal] = useState(false);

  const onStatusUpdate = (_, status: JobStatusType) => {
    updateJobData({ status });
  };

  if (queryResult.isFetching) {
    return <JobElementHeaderSkeleton className={className} />;
  }

  return (
    <>
      <JobCover
        data={{
          firstText: firstText || t('no_title_entered'),
          secondText,
          thirdText,
          fourthText: <FallbackText value={location?.label} />,
          image,
          jobId,
        }}
        className={className}
        backButtonProps={backButtonProps}
        bottomActions={
          isPublished && !isCreationMode ? (
            isBusinessApp ? (
              <>
                {hasEditAccess && (
                  <JobStatusButton
                    className={classes.button}
                    value={getJobPropValue('status')}
                    jobId={jobId}
                    onSuccess={onStatusUpdate}
                  />
                )}
                <Button label={t('track')} className={classes.button} />
              </>
            ) : visibleApplied ? (
              <>
                <JobsSaveButton job={job} variant="button" />
                <JobTrackButton
                  className={classes.button}
                  onClick={() => setOpenTrackingModal(true)}
                />
              </>
            ) : isLoggedIn ? (
              <>
                <JobsSaveButton job={job} variant="button" />
                <JobApplyButton fullWidth className={classes.button} />
              </>
            ) : (
              <Button
                className={classes.button}
                label={t('login_to_apply')}
                leftIcon="sign-in-alt"
                href={`${urls.login}?redirect=${window?.location?.href.replace(
                  window?.location?.origin,
                  ''
                )}`}
              />
            )
          ) : undefined
        }
        jobInfo={[
          { title: t('applicants'), value: applicantsCount || 0 },
          { title: t('views_cap'), value: viewCount || 0 },
          { title: t('skills'), value: skills?.length || 0 },
          date && {
            title: isMoreThanTablet ? undefined : t('created'),
            value: date,
            valueProps: isNew
              ? { color: 'success', font: isMoreThanTablet ? '700' : '400' }
              : { font: isMoreThanTablet ? '700' : '400' },
            titleProps:
              isNew && isMoreThanTablet ? { color: 'success' } : undefined,
          },
        ].filter(Boolean)}
        action={
          !disableActions &&
          !isCreationMode && (
            <PopperMenu
              placement="bottom-end"
              menuClassName={classes.menuContainer}
              closeOnScroll
              buttonComponent={
                <IconButton
                  type="fas"
                  name="ellipsis-h"
                  size="md"
                  className={classes.moreButton}
                />
              }
            >
              <>
                {actions.map(({ icon, label, onClick }) =>
                  icon === 'divider' ? (
                    <Divider key={icon} className={classes.divider} />
                  ) : (
                    <PopperItem
                      key={icon}
                      onClick={onClick}
                      iconName={icon}
                      iconType="far"
                      label={label}
                    />
                  )
                )}
              </>
            </PopperMenu>
          )
        }
      />
      {openTrackingModal && (
        <JobTrackingModal
          onClose={() => setOpenTrackingModal(false)}
          isOpen={openTrackingModal}
          job={job}
        />
      )}
    </>
  );
};

export default JobElementHeader;
