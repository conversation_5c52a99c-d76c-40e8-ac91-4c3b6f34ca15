import React from 'react';
import Skeleton from 'shared/uikit/Skeleton';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import classes from './JobElementHeader.skeleton.module.scss';

interface JobElementHeaderProps {
  className?: string;
  isUser?: boolean;
  isJob?: boolean;
}

const JobElementHeaderSkeleton: React.FC<JobElementHeaderProps> = ({
  isUser,
  isJob,
}) => (
  <Flex>
    <Flex className={cnj(classes.root)}>
      {!isJob && <Skeleton className={classes.header} />}
      <Flex className={classes.wrapper}>
        <Skeleton
          className={cnj(
            classes.avatar,
            isUser && classes.isUserAvatar,
            isJob && classes.jobAvatar
          )}
        />
        <Flex className={classes.fullWidth}>
          <Skeleton
            className={cnj(classes.wrapper__1, isJob && classes.wrapper__1_job)}
          />
          <Skeleton className={classes.wrapper__2} />
        </Flex>
      </Flex>
      <Flex className={classes.bottomWrapper}>
        <Skeleton className={classes.bottomWrapper__1} />
        <Skeleton className={classes.bottomWrapper__1} />
      </Flex>
    </Flex>
  </Flex>
);

export default JobElementHeaderSkeleton;
