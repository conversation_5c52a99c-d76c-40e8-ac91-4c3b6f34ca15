import React from 'react';
import jobsApi from 'shared/utils/api/jobs';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateQueryData from 'shared/utils/hooks/useUpdateQueryData';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import useHistory from 'shared/utils/hooks/useHistory';
import Typography from 'shared/uikit/Typography';
import useMedia from 'shared/uikit/utils/useMedia';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import useToast from 'shared/uikit/Toast/useToast';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import useOpenShareWindow from 'shared/hooks/useOpenShareWindow';
import { useJobsDispatch } from 'shared/providers/JobsPorvider/jobs.provider';
import { getEntityShareUrl } from 'shared/utils/getJobShareUrl';
import type { IJob } from 'shared/types/job';
import { ShareEntities, ShareEntityTab } from 'shared/types/share/entities';
import useWithdrawJobs from '../../../hooks/useWithdrawJobs';
import useUnSaveJobs from '../../../hooks/useUnSaveJobs';
import useSaveJobs from '../../../hooks/useSaveJobs';

const useJobElementHeaderActions = () => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const history = useHistory();
  const { openConfirmDialog } = useOpenConfirm();
  const toast = useToast();
  const { isTabletAndLess } = useMedia();
  const openShareWindow = useOpenShareWindow();
  const { saveJob } = useSaveJobs();
  const { unSaveJob } = useUnSaveJobs();
  const { withdrawJob } = useWithdrawJobs();
  const {
    getJobPropValue,
    isPublished,
    hasEditAccess,
    jobElement,
    updateJobData,
    queryKey,
    setActiveStep: setJobDetailsFormStep,
  } = useJobElement();
  const jobsDispatch = useJobsDispatch();
  const { replace } = useUpdateQueryData<IJob>(queryKey);
  const jobId = getJobPropValue('id');
  const isSaved = getJobPropValue('isSaved');
  const visibleApplied = getJobPropValue('isApplied');
  const applicantsCount = getJobPropValue('applicantsCount');
  const jobTitle = getJobPropValue(['title', 'label']);

  const { mutate: deleteJob } = useReactMutation({
    apiFunc: jobsApi.deleteJob,
  });

  const onSuccess = async () => {
    history.goBack();
  };

  const handleReportJob = () => {
    appDispatch({
      type: 'TOGGLE_REPORT_MODAL',
      payload: {
        isOpen: true,
        data: {
          entityType: 'job',
          entityId: jobId,
        },
      },
    });
  };

  const onSuccessWithDraw = () => {
    const updatedDate = {
      applicantsCount: applicantsCount - 1,
      isApplied: false,
    };
    updateJobData(updatedDate);
    if (queryKey) {
      replace({
        ...jobElement,
        ...updatedDate,
      });
    }
    toast({
      type: 'success',
      icon: 'check-circle',
      title: t('job_withdrawn'),
      message: () => (
        <>
          <Typography font="700" color="thirdText">
            {jobTitle}
          </Typography>
          <Typography>
            &nbsp;
            {t('success_withdrawn')}
          </Typography>
        </>
      ),
    });
  };

  const changeOwnerHandler = () => {
    jobsDispatch({ type: 'SET_IS_JOB_FORM_OPEN', payload: true });
    setJobDetailsFormStep(5);
  };

  const saveJobHandler = () => {
    saveJob({
      job: jobElement,
      onSuccess: (res) => {
        updateJobData({ isSaved: true, jobSaveId: res?.id });
      },
    });
  };

  const unSaveJobHandler = () => {
    unSaveJob({
      job: jobElement,
      onSuccess: () => {
        updateJobData({ isSaved: false, jobSaveId: undefined });
      },
    });
  };

  const openConfirm = () => {
    openConfirmDialog({
      title: t('delete'),
      message: t('delete_job_helper'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func: deleteJob,
        variables: { id: jobId },
        onSuccess,
      },
    });
  };

  const onSetAlertHandler = () => {};
  const onEditHandler = () => {
    setJobDetailsFormStep(1);
    jobsDispatch({ type: 'SET_IS_JOB_FORM_OPEN', payload: true });
  };
  const handleShare = () => {
    appDispatch({
      type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
      payload: {
        isOpen: true,
        tabs: [
          ShareEntityTab.COPY_LINK,
          ShareEntityTab.SHARE_VIA_POST,
          ShareEntityTab.SHARE_VIA_MESSAGE,
          ShareEntityTab.SHARE_VIA_EMAIL,
        ],
        entityData: {
          attachment: {
            type: ShareEntities.JOB,
            data: jobElement,
          },
        },
      },
    });
  };

  const handleWithDraw = () =>
    withdrawJob({ job: jobElement, onSuccess: onSuccessWithDraw });

  const setAlert = {
    icon: 'bell',
    label: t('set_alert'),
    onClick: onSetAlertHandler,
  };

  const saveJobs = {
    icon: isSaved ? 'bookmark' : 'bookmark',
    label: isSaved ? t('unsave_job') : t('save_job'),
    onClick: isSaved ? unSaveJobHandler : saveJobHandler,
  };
  const share = {
    icon: 'share',
    label: t('share_job'),
    onClick: handleShare,
  };

  const shareJob = {
    icon: 'share',
    label: t('share_job'),
    onClick: () => {
      appDispatch({
        type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
        payload: {
          isOpen: true,
          tabs: [
            ShareEntityTab.COPY_LINK,
            ShareEntityTab.SHARE_VIA_POST,
            ShareEntityTab.SHARE_VIA_MESSAGE,
            ShareEntityTab.SHARE_VIA_EMAIL,
          ],
          entityData: {
            attachment: {
              type: ShareEntities.JOB,
              data: jobElement,
            },
          },
        },
      });
    },
  };
  const withdrawApp = {
    icon: 'times',
    label: t('withdraw_app'),
    onClick: handleWithDraw,
  };
  const edit = {
    icon: 'pen',
    label: t('edit_job_details'),
    onClick: onEditHandler,
  };
  const deleteIcon = {
    icon: 'trash',
    label: t('delete_job'),
    onClick: openConfirm,
  };
  const changeOwner = {
    icon: 'user',
    label: t('edit_collaborators'),
    onClick: changeOwnerHandler,
  };

  const shareViaTwitter = {
    icon: 'twitter',
    label: t('shar_via_twit'),
    onClick: () => {
      openShareWindow('https://twitter.com/share', {
        url: getEntityShareUrl('job'),
      });
    },
  };
  const shareViaFacebook = {
    icon: 'facebook',
    label: t('shar_via_face'),
    onClick: () => {
      const params = { u: getEntityShareUrl('job') };

      openShareWindow('https://www.facebook.com/sharer/sharer.php', params);
    },
  };

  const shareViaLinkedin = {
    icon: 'linkedin',
    label: t('shar_via_linkdin'),
    onClick: () => {
      const params = { url: getEntityShareUrl('job') };
      openShareWindow('https://linkedin.com/shareArticle', params);
    },
  };

  const reportJob = {
    icon: 'report',
    label: t('report_this_job'),
    onClick: handleReportJob,
  };

  const socials = [shareViaTwitter, shareViaFacebook, shareViaLinkedin];

  const divider = { icon: 'divider' };
  if (isBusinessApp && !isPublished && hasEditAccess) {
    return [edit, shareJob, deleteIcon, changeOwner];
  }
  if (isBusinessApp && isPublished && hasEditAccess) {
    return [edit, share, , deleteIcon, changeOwner, divider, ...socials];
  }
  if (isBusinessApp && !isPublished && !hasEditAccess) {
    return [shareJob, reportJob];
  }
  if (isBusinessApp && isPublished && hasEditAccess) {
    return [share, divider, ...socials];
  }

  return [
    setAlert,
    isTabletAndLess && saveJobs,
    share,
    visibleApplied && withdrawApp,
    reportJob,
    divider,
    ...socials,
  ].filter(Boolean);
};

export default useJobElementHeaderActions;
