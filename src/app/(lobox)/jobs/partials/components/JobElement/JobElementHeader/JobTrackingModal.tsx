import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import type { FixedRightSideModalDialogProps } from '@shared/uikit/Modal/FixedRightSideModalDialog/FixedRightSideModalDialog.component';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { IJob } from '@shared/types/job';
// eslint-disable-next-line import/no-cycle
import JobTrackingModalBody from './JobTrackingModal/JobTrackingModalBody';
import TrackingJobProvider from './JobTrackingModal/TrackingJobContext';

interface JobTrackingModalProps extends FixedRightSideModalDialogProps {
  job: IJob;
}

const JobTrackingModal: React.FC<JobTrackingModalProps> = (props) => {
  const { job, ...rest } = props;

  const { t } = useTranslation();
  return (
    <FixedRightSideModal isOpen wide isOpenAnimation {...rest}>
      <ModalHeaderSimple title={t('track_application')} />
      <ModalBody className="!pt-0">
        <TrackingJobProvider job={job} onCloseModal={rest.onClose}>
          <JobTrackingModalBody />
        </TrackingJobProvider>
      </ModalBody>
    </FixedRightSideModal>
  );
};

export default JobTrackingModal;
