import Flex from '@shared/uikit/Flex';
import dynamic from 'next/dynamic';
import { useMemo, useState } from 'react';
import Tabs from 'shared/components/Organism/Tabs';
import useTranslation from 'shared/utils/hooks/useTranslation';

const JobTrackingModalDetails = dynamic(
  // eslint-disable-next-line import/no-cycle
  () => import('./JobTrackingModalBody/JobTrackingModalDetails'),
  { ssr: false }
);
const JobTrackingModalMeetings = dynamic(
  () => import('./JobTrackingModalBody/JobTrackingModalMeetings'),
  { ssr: false }
);
const JobTrackingModalAssessments = dynamic(
  () => import('./JobTrackingModalBody/JobTrackingModalAssessments'),
  { ssr: false }
);
const JobTrackingEmails = dynamic(
  () => import('./JobTrackingModalBody/JobTrackingEmails'),
  { ssr: false }
);
const JobTrackingModalMessages = dynamic(
  () => import('./JobTrackingModalBody/JobTrackingModalMessages'),
  { ssr: false }
);
const JobTrackingModalQuestionnaires = dynamic(
  () => import('./JobTrackingModalBody/JobTrackingModalQuestionnaires'),
  { ssr: false }
);
const JobTrackingModalBody = () => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState('details');
  const TabPanels = useMemo(() => {
    switch (selectedTab) {
      case 'details':
        return <JobTrackingModalDetails />;
      case 'meetings':
        return <JobTrackingModalMeetings />;
      case 'questionnaires':
        return <JobTrackingModalQuestionnaires />;
      case 'assessments':
        return <JobTrackingModalAssessments />;
      case 'emails':
        return <JobTrackingEmails />;
      case 'messages':
        return <JobTrackingModalMessages />;
      default:
        return null;
    }
  }, [selectedTab]);
  return (
    <>
      <Flex className="border-b border-techGray_10 border-solid -mx-20">
        <Tabs
          activePath={selectedTab}
          onChangeTab={setSelectedTab}
          styles={{
            tabsRoot: 'h-[44px]',
            linksRoot: '',
          }}
          tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
        />
      </Flex>
      {TabPanels}
    </>
  );
};

export default JobTrackingModalBody;

const tabs = [
  {
    title: 'details',
    path: 'details',
  },
  {
    title: 'meetings',
    path: 'meetings',
  },
  {
    title: 'questionnaires',
    path: 'questionnaires',
  },
  {
    title: 'assessments',
    path: 'assessments',
  },
  {
    title: 'emails',
    path: 'emails',
  },
  {
    title: 'messages',
    path: 'messages',
  },
];
