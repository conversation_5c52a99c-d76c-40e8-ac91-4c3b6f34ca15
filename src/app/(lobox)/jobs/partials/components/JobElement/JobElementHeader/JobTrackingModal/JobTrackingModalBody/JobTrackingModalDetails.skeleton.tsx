import Skeleton from '@shared/uikit/Skeleton';
import Flex from '@shared/uikit/Flex';
import classes from './JobTrackingModalDetails.skeleton.module.scss';

const JobTrackingModalDetailsSkeleton: React.FC = () => (
  <Flex>
    <Flex className={classes.root}>
      <Flex className={classes.header}>
        <Skeleton className={classes.avatar} />
        <Flex className={classes.meta}>
          <Skeleton className={classes.title} />
          <Skeleton className={classes.handle} />
          <Skeleton className={classes.field} />
          <Skeleton className={classes.location} />
        </Flex>
      </Flex>

      {/* Application date */}
      <Flex className={classes.section}>
        <Skeleton className={classes.sectionTitle} />
        <Skeleton className={classes.sectionText} />
      </Flex>

      {/* Current stage */}
      <Flex className={classes.section}>
        <Skeleton className={classes.sectionTitle} />
        <Skeleton className={classes.currentStageBox} />
      </Flex>

      {/* Previous stages */}
      <Flex className={classes.section}>
        <Skeleton className={classes.sectionTitle} />
        {Array.from({ length: 3 }).map((_, i) => (
          <Skeleton key={i} className={classes.stageItem} />
        ))}
      </Flex>
    </Flex>
  </Flex>
);

export default JobTrackingModalDetailsSkeleton;
