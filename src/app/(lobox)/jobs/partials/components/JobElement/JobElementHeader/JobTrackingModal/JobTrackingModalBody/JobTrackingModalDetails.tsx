import { useCallback } from 'react';
import formatDate from '@shared/utils/toolkit/formatDate';
import Info from '@shared/components/molecules/Info/Info';
import { getTrackingJob } from '@shared/utils/api/jobs';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useQuery } from '@tanstack/react-query';
import type { PeopleType } from '@shared/types/people';
import type { PipelineItemProps } from '@shared/uikit/Pipelines';
import type { JobAPIProps } from '@shared/types/jobsProps';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import SearchCard from '@shared/components/Organism/SearchCard';
import ObjectInfoCard from '@shared/components/molecules/ObjectInfoCard';
import { FallbackText } from '@shared/components/atoms/FallbackText';
import JobTrackingStage from './JobTrackingModalDetails/JobTrackingStage';
import JobTrackingDetailsMessageButton from './JobTrackingModalDetails/JobTrackingDetailsMessageButton';
import { useTrackingJob } from '../TrackingJobContext';
import JobTrackingModalDetailsSkeleton from './JobTrackingModalDetails.skeleton';

interface JobTrackingProps {
  job: JobAPIProps;
  dateTime: string;
  stage: PipelineItemProps;
  stages: PipelineItemProps[];
  pointOfContact: PeopleType;
}

const JobTrackingModalDetails = () => {
  const { t } = useTranslation();
  const { job } = useTrackingJob();
  const { data, isLoading } = useQuery<JobTrackingProps>({
    queryKey: ['job', job.applicationId],
    queryFn: () => getTrackingJob(job.applicationId || ''),
  });

  const MemoizedMessageButton = useCallback(
    () => (
      <JobTrackingDetailsMessageButton
        user={data?.pointOfContact as PeopleType}
      />
    ),
    [data?.pointOfContact]
  );
  if (isLoading) {
    return <JobTrackingModalDetailsSkeleton />;
  }

  if (!data) {
    return null;
  }

  return (
    <Flex className="mt-20 gap-32">
      <ObjectInfoCard
        isPage
        avatar={data.job.pageCroppedImageUrl}
        firstText={data.job.title}
        secondText={data.job.pageUsername}
        fourthText={data.job.location}
        thirdText={data.job.categoryName}
        isFirstTextSmall
      />
      <Flex className="gap-4">
        <Typography color="smoke_coal" font="700" height={18}>
          {t('application_date')}
        </Typography>
        <Typography color="secondaryDisabledText" size={14} height={18}>
          {formatDate(data.dateTime, 'LL')}
        </Typography>
      </Flex>
      {data.stage.type === 'ON_BOARDED' ? (
        <Info
          text={t('user_hired_text')}
          color="success"
          textColor="smoke_coal"
          className="!bg-success_10"
        />
      ) : (
        <Flex className="gap-12">
          <Typography color="smoke_coal" font="700" height={18}>
            {t('current_stage')}
          </Typography>
          <JobTrackingStage stage={data.stage} />
        </Flex>
      )}
      {data.stages.length > 0 && (
        <Flex>
          <Typography
            color="smoke_coal"
            font="700"
            height={18}
            className="mb-12"
          >
            {t('previous_stages')}
          </Typography>
          {data.stages.map((stage, i) => (
            <>
              <JobTrackingStage key={stage.id} stage={stage} noColor />
              {data.stages.length !== i + 1 && (
                <Flex className="w-[25px] h-16 border-solid border-techGray_20 border-r-[2px]" />
              )}
            </>
          ))}
        </Flex>
      )}
      <Flex className="gap-12">
        <Typography color="smoke_coal" font="700" height={18}>
          {t('point_of_contact')}
        </Typography>
        <SearchCard
          imgSrc={data.pointOfContact?.croppedImageUrl ?? ''}
          firstText={`${data.pointOfContact?.name} ${data.pointOfContact?.surname}`}
          secondText={data.pointOfContact?.username}
          fourthText={
            <FallbackText value={data.pointOfContact?.locationTitle} />
          }
          thirdText={data.pointOfContact?.occupationName ?? ''}
          isHoverAble={false}
          classNames={{ container: '!p-0', bottomWrapper: '!mt-20' }}
          bottomComponent={MemoizedMessageButton}
        />
      </Flex>
      <Info
        text={t('job_tracking_description')}
        color="secondaryDisabledText"
      />
    </Flex>
  );
};

export default JobTrackingModalDetails;
