import useOpenMessage from '@shared/hooks/useOpenMessage';
import type { PeopleType } from '@shared/types/people';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { roomMemberRoles } from '@shared/components/Organism/Message/constants';
import Button from '@shared/uikit/Button';
import { useTrackingJob } from '../../TrackingJobContext';

const JobTrackingDetailsMessageButton = ({ user }: { user: PeopleType }) => {
  const { t } = useTranslation();
  const { onCloseModal } = useTrackingJob();
  const openMessage = useOpenMessage();
  const openMessageHandler = () => {
    onCloseModal?.();
    openMessage({
      isGroupChat: false,
      id: user.id,
      icon: user.croppedImageUrl,
      name: `${user.name} ${user.surname}`,
      owner: user.id,
      username: user.username,
      createdAt: new Date(),
      role: roomMemberRoles.Owner,
      isPage: false,
    });
  };

  return (
    <Button
      label={t('message')}
      fullWidth
      schema="semi-transparent"
      leftIcon="envelope"
      onClick={openMessageHandler}
    />
  );
};

export default JobTrackingDetailsMessageButton;
