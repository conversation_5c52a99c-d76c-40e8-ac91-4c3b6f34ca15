import formatDate from '@shared/utils/toolkit/formatDate';
import type { PipelineItemProps } from '@shared/uikit/Pipelines';
import Flex from '@shared/uikit/Flex';
import cnj from '@shared/uikit/utils/cnj';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';

interface StageProps {
  stage: PipelineItemProps;
  noColor?: boolean;
}

const JobTrackingStage = (props: StageProps) => {
  const { stage, noColor } = props;
  return (
    <Flex
      style={
        noColor
          ? {}
          : {
              borderColor: `var(--${stage.color ?? 'brand'})`,
              backgroundColor: `var(--${stage.color ?? 'brand'}_10)`,
            }
      }
      className={cnj(
        '!flex-row items-center p-12 gap-12 rounded-[4px] border border-solid',
        noColor && '!border-techGray_20 !bg-hoverPrimary'
      )}
    >
      <Flex
        style={
          noColor
            ? {}
            : { backgroundColor: `var(--${stage.color ?? 'brand'}_10)` }
        }
        className={cnj(
          'w-24 h-24 rounded-[4px] items-center justify-center',
          noColor && '!bg-techGray_10'
        )}
      >
        <Icon
          name="circle"
          color={noColor ? 'secondaryDisabledText' : (stage.color ?? 'brand')}
          size={13}
        />
      </Flex>
      <Flex>
        <Typography color="smoke_coal" font="700" height={18}>
          {stage.title}
        </Typography>
        <Typography color="secondaryDisabledText" size={14} height={18}>
          {formatDate(stage.dateTime, 'LL')}
        </Typography>
      </Flex>
      {noColor && (
        <Flex className="ml-auto">
          <Icon name="check" color="smoke_coal" size={18} />
        </Flex>
      )}
    </Flex>
  );
};

export default JobTrackingStage;
