import ViewPortList from '@shared/uikit/ViewPortList';
import { useQuery } from '@tanstack/react-query';
import { getJobTrackingMeetings } from '@shared/utils/api/jobs';
import ActivityItem from '@shared/components/molecules/ActivityItem';
import React, { useCallback } from 'react';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import type { ActivityProps } from '@shared/types/activityProps';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useTrackingJob } from '../TrackingJobContext';

const JobTrackingModalMeetings = () => {
  const { job } = useTrackingJob();
  const { t } = useTranslation();

  const { data, isLoading } = useQuery({
    queryKey: [QueryKeys.participationMettingsActivities, job.applicationId],
    queryFn: () => getJobTrackingMeetings(job.applicationId || ''),
  });

  const MemoizedMeetingItem = useCallback(
    (index: number, meeting: ActivityProps) => <ActivityItem item={meeting} />,
    []
  );

  if (!isLoading && !data?.length) {
    return (
      <EmptySearchResult
        title={t('no_meetings_found')}
        sectionMessage={t('no_meetings_found_desc')}
        className="flex-1"
      />
    );
  }

  return (
    <ViewPortList
      useRelativeScroller
      style={{ height: '100%' }}
      data={data}
      increaseViewportBy={200}
      itemContent={MemoizedMeetingItem}
      className="mt-20"
      components={{
        List,
      }}
    />
  );
};

export default JobTrackingModalMeetings;

const List = (props: React.HTMLProps<HTMLDivElement>) => (
  <div {...props} className="flex flex-col gap-20" />
);
List.displayName = 'List';
