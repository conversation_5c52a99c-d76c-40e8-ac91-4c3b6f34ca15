import Icon from '@shared/uikit/Icon';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import ParseTextStringCP from 'shared/components/molecules/TranslateReplacer';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import Avatar from '@shared/uikit/Avatar';

const JobTrackingModalMessages = () => {
  const { t } = useTranslation();
  return (
    <Flex className="gap-4 -m-8 pt-20 mx-[-16px] sm:mx-[-10px]">
      {fakeMessages.map((message) => (
        <InfoCard
          key={message.id}
          onClick={() => alert('TODO: Needs to be implemented!')}
          avatar={
            <Avatar imgSrc={message.image} size="smd" className="mr-12" />
          }
          titleProps={{
            font: '700',
            color: 'smoke_coal',
            size: 15,
            height: 18,
            lineNumber: 1,
          }}
          title={
            <ParseTextStringCP
              textProps={{ className: '!h-[18px]' }}
              textString={translateReplacer(
                '<b>{name}</b> messaged <b>{name}</b>',
                [message.name, 'You']
              )}
              tagComponentMap={{
                // eslint-disable-next-line react/no-unstable-nested-components
                0: (text) => <Typography font="700">{text}</Typography>,
                // eslint-disable-next-line react/no-unstable-nested-components
                1: (text) => <Typography font="700">{t(text)}</Typography>,
              }}
            />
          }
          value={
            <Typography
              color="secondaryDisabledText"
              size={12}
              height={18}
              lineNumber={1}
              className="flex-1"
            >
              {message.time}
            </Typography>
          }
          valueProps={{ color: 'primaryText' }}
        >
          <Flex className="items-center justify-center h-full ml-12">
            <Icon name="chevron-right" type="far" size={13} />
          </Flex>
        </InfoCard>
      ))}
    </Flex>
  );
};

export default JobTrackingModalMessages;

// TODO: Remove it when BE is ready
const fakeMessages = [
  {
    id: 1,
    name: 'Ahmad Khani',
    message: 'Hello, how are you?',
    time: 'May 17, 2024 as 02:00',
    image:
      'https://storage.googleapis.com/lobox_public_images/image/original/********************************.jpeg',
  },
  {
    id: 2,
    name: 'Sograb Niroo',
    time: 'June 05, 2024 as 12:15',
    image:
      'https://storage.googleapis.com/lobox_public_images/image/original/********************************.png',
  },
];
