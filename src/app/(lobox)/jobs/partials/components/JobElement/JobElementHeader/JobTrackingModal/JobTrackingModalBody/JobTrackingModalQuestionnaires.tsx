import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import Icon from '@shared/uikit/Icon';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import useTranslation from 'shared/utils/hooks/useTranslation';

const JobTrackingModalQuestionnaires = () => {
  const { t } = useTranslation();
  return (
    <Flex className="gap-20 mt-20">
      {fakeQuestionnaires.map((questionnaire) => (
        <Flex
          key={questionnaire.id}
          className="p-12 !flex-row items-center bg-gray_5 rounded-[4px] border border-solid border-techGray_20"
        >
          <Flex className="w-32 h-32 rounded-full bg-darkSecondary_hover items-center justify-center mr-12">
            <Icon
              name="question"
              size={16}
              type="far"
              color="secondaryDisabledText"
            />
          </Flex>
          <Flex className="gap-4 flex-1">
            <Typography height={18} color="smoke_coal">
              {questionnaire.title}
            </Typography>
            <Flex className="!flex-row items-center gap-4">
              <Icon name="circle" color={questionnaire.stage.color} size={7} />
              <Typography size={13} height={15} color="secondaryDisabledText">
                {translateReplacer(t('on_stage'), [
                  t(questionnaire.stage.type.toLowerCase()),
                ])}
              </Typography>
            </Flex>
          </Flex>
          <Typography size={14} height={16} color="secondaryDisabledText">
            {translateReplacer(t('num_questions_answered'), [
              questionnaire.answered.toString(),
              questionnaire.questions.toString(),
            ])}
          </Typography>
        </Flex>
      ))}
    </Flex>
  );
};

export default JobTrackingModalQuestionnaires;

// TODO: Remove it when BE is ready
const fakeQuestionnaires = [
  {
    id: 1,
    title: 'Questionnaire 1',
    stage: {
      type: 'REVIEW',
      color: 'brand',
    },
    questions: 20,
    answered: 10,
  },
  {
    id: 2,
    title: 'Questionnaire 2',
    stage: {
      type: 'REVIEW',
      color: 'pendingOrange',
    },
    questions: 20,
    answered: 10,
  },
  {
    id: 3,
    title: 'Questionnaire 3',
    stage: {
      type: 'REVIEW',
      color: 'success',
    },
    questions: 20,
    answered: 10,
  },
];
