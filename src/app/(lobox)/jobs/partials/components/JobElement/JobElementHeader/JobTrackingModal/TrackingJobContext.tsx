import type { IJ<PERSON> } from '@shared/types/job';
import { createContext, useContext, useMemo } from 'react';

interface TrackingJobStateProps {
  job: IJob;
}

interface TrackingJobActionsProps {
  onCloseModal?: () => void;
}

const TrackingJobStateContext = createContext<TrackingJobStateProps>({
  job: {} as IJob,
});

const TrackingJobActionsContext = createContext<TrackingJobActionsProps>({
  onCloseModal: () => {},
});

interface TrackingJobProviderProps {
  children: React.ReactNode;
  job: IJob;
  onCloseModal?: () => void;
}

const TrackingJobProvider: React.FC<TrackingJobProviderProps> = ({
  children,
  job,
  onCloseModal,
}) => {
  const memoizedStateValue = useMemo(() => ({ job }), [job]);

  const memoizedActionsValue = useMemo(
    () => ({ onCloseModal }),
    [onCloseModal]
  );

  return (
    <TrackingJobStateContext.Provider value={memoizedStateValue}>
      <TrackingJobActionsContext.Provider value={memoizedActionsValue}>
        {children}
      </TrackingJobActionsContext.Provider>
    </TrackingJobStateContext.Provider>
  );
};

export default TrackingJobProvider;

export const useTrackingJobState = () => useContext(TrackingJobStateContext);
export const useTrackingJobActions = () =>
  useContext(TrackingJobActionsContext);

export const useTrackingJob = () => {
  const state = useTrackingJobState();
  const actions = useTrackingJobActions();

  return {
    ...state,
    ...actions,
  };
};
