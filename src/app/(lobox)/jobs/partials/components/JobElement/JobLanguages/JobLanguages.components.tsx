import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import LanguageList from 'shared/uikit/LanguagePicker/LanguageList.component';
import Typography from 'shared/uikit/Typography';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './JobLanguages.components.module.scss';

const JobLanguages: React.FC = () => {
  const { t } = useTranslation();
  const { getJobPropValue, hasEditAccess } = useJobElement();
  const languages = getJobPropValue('languages');

  const onClick = () => {};
  const isEmpty = !languages?.length;
  if (isEmpty && !hasEditAccess) return null;

  return (
    <SectionLayout
      title={t('languages')}
      classNames={{
        childrenWrap: cnj(classes.childrenWrap, isEmpty && classes.empty),
      }}
      onClick={onClick}
    >
      {isEmpty ? (
        <Typography color="colorIconForth2">
          {hasEditAccess ? t('add_lngs') : t('no_lng_ent')}
        </Typography>
      ) : (
        <LanguageList
          languages={languages}
          visibleAction={false}
          className={classes.languagesWrap}
        />
      )}
    </SectionLayout>
  );
};

export default JobLanguages;
