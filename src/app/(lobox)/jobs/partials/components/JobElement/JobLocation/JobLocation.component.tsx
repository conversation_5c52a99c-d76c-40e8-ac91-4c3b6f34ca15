import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import GoogleMap from 'shared/uikit/GoogleMap';
import { locationZoomLevelValues } from 'shared/utils/constants/location';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import classes from './JobLocation.component.module.scss';

const JobLocation: React.FC = () => {
  const { t } = useTranslation();
  const { getJobPropValue, hasEditAccess } = useJobElement();
  const location = getJobPropValue('location');
  const zoomLevel = locationZoomLevelValues?.[location?.category] || 10;
  const position = {
    lat: location?.position?.lat,
    lon: location?.position?.lon,
  };
  const onClickAction = () => {};

  if (!hasEditAccess && !location) return null;
  return (
    <SectionLayout
      title={t('location')}
      classNames={{ childrenWrap: classes.childrenWrap }}
      onClick={onClickAction}
      visibleActionButton={false}
    >
      {position?.lat && position?.lon ? (
        <GoogleMap
          // isClickAble={false}
          value={position}
          defaultZoom={zoomLevel}
          center={[position.lat, position.lon]}
          className={classes.map}
          zoomAble
          draggable={hasEditAccess}
          animated
        />
      ) : null}

      <InfoCard
        {...{
          disabledHover: true,
          title: t('location'),
          subTitle: hasEditAccess ? t('add_location') : t('no_location_ent'),
          value: location?.label,
          icon: 'map-marker-alt',
          iconSize: 16,
          wrapperClassName: cnj(classes.itemStyle, classes.noTopBorderRadius),
          valueProps: {
            isWordWrap: true,
          },
        }}
      />
    </SectionLayout>
  );
};

export default JobLocation;
