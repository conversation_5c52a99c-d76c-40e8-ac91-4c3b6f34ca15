import React from 'react';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Skeleton from '@shared/uikit/Skeleton';
import classes from './JobOverview.components.module.scss';
import useJobOverviewData from './jobOverview.useData';

interface Props {
  isLoading?: boolean;
}

const JobOverview: React.FC<Props> = ({ isLoading }) => {
  const { t } = useTranslation();
  const data = useJobOverviewData();

  if (isLoading)
    return (
      <SectionLayout
        title={<Skeleton className="!w-[137px] !h-[24px]" />}
        classNames={{ childrenWrap: '!p-0 !rounded-md' }}
      >
        <Skeleton className="h-[300px] w-full rounded-md" />
      </SectionLayout>
    );
  return (
    <SectionLayout
      title={t('overview')}
      classNames={{ childrenWrap: classes.childrenWrap }}
    >
      {data.map(({ title, subTitle, value, icon }) => (
        <InfoCard
          key={title}
          {...{
            disabledHover: true,
            title,
            subTitle,
            value,
            icon,
            iconSize: 16,
            valueProps: {
              isWordWrap: true,
            },
          }}
        />
      ))}
    </SectionLayout>
  );
};

export default JobOverview;
