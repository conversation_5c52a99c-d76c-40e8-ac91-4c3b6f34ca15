import React from 'react';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Skeleton from 'shared/uikit/Skeleton';
import classes from './jobOverview.useData.module.scss';

type ContactInfoType = {
  id: string;
  title: string;
  subTitle?: string;
  value?: any;
  icon: string;
  to?: string;
};

const useJobOverviewData = (): Array<ContactInfoType> => {
  const { getJobPropValue, hasEditAccess, queryResult } = useJobElement();
  const isLoading = queryResult.isFetching || queryResult.isLoading;
  const category = getJobPropValue('category');
  const { t } = useTranslation();
  const employmentType = getJobPropValue('employmentType');
  const experienceLevel = getJobPropValue('experienceLevel');
  const salaryRangeMin = getJobPropValue('salaryRangeMin');
  const salaryCurrency = getJobPropValue('salaryCurrency');
  const symbol = salaryCurrency?.symbol || '';
  const salaryRangeMax = getJobPropValue('salaryRangeMax');
  const salaryPeriod = getJobPropValue('salaryPeriod');
  const workPlaceType = getJobPropValue('workPlaceType');
  const salaryValue =
    salaryRangeMin || salaryRangeMax
      ? `${salaryRangeMin || ''}${symbol} - ${salaryRangeMax || ''}${symbol}`
      : undefined;

  return [
    (hasEditAccess || workPlaceType?.label || isLoading) && {
      id: 'job_model',
      title: t('job_model'),
      subTitle: t('no_job_model_en'),
      icon: 'user-model',
      value: isLoading ? (
        <Skeleton className={classes.skeleton} />
      ) : (
        t(workPlaceType?.label)
      ),
    },
    (hasEditAccess || category?.label || isLoading) && {
      id: 'category',
      title: t('category'),
      subTitle: t('add_category'),
      icon: 'list',
      value: isLoading ? (
        <Skeleton className={classes.skeleton} />
      ) : (
        t(category?.label)
      ),
    },
    (hasEditAccess || employmentType?.label || isLoading) && {
      id: 'job_type',
      title: t('job_type'),
      subTitle: t('add_job_type'),
      icon: 'briefcase',
      value: isLoading ? (
        <Skeleton className={classes.skeleton} />
      ) : (
        t(employmentType?.label)
      ),
    },
    (experienceLevel?.label || hasEditAccess || isLoading) && {
      id: 'exp_lvl',
      title: t('exp_level'),
      subTitle: t('add_exp_level'),
      icon: 'signal-bar',
      value: isLoading ? (
        <Skeleton className={classes.skeleton} />
      ) : (
        t(experienceLevel?.label)
      ),
    },
    (hasEditAccess || salaryValue) && {
      id: 'salary_rng',
      title: salaryPeriod
        ? `${t('salary_rng')} (${salaryPeriod?.label})`
        : `${t('salary_rng')}`,
      subTitle: t('add_sal_rng'),
      icon: 'usd-circle',
      value: salaryValue,
    },
  ].filter(Boolean);
};

export default useJobOverviewData;
