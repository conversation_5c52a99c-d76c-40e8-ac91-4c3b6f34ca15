import React, { useCallback } from 'react';
import Flex from 'shared/uikit/Flex';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import Typography from 'shared/uikit/Typography';
import Spinner from 'shared/uikit/Spinner';
import Form from 'shared/uikit/Form';
import Button from 'shared/uikit/Button';
import useToast from 'shared/uikit/Toast/useToast';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalDialog from 'shared/uikit/Modal/ModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import formValidator from 'shared/utils/form/formValidator';
import jobsApi from 'shared/utils/api/jobs';
import jobsNormalizer from 'shared/utils/jobsNormalizer';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useAuthUser from 'shared/utils/hooks/useAuthUser';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateQueryData from 'shared/utils/hooks/useUpdateQueryData';
import type { IJob } from 'shared/types/job';
import type { UserType } from 'shared/types/user';
import classes from './JobOwnerModal.component.module.scss';
import JobOwnerModalItem from './JobOwnerModal.item';
import JobOwnerForm from './JobOwnerModal.form';

export interface JobOwnerModalProps {
  className?: string;
  toggleJobOwnersModal: (val: boolean) => void;
  jobId: string;
  updateJobList?: (job: IJob, isRemove?: boolean) => void;
}

const JobOwnerModal: React.FC<JobOwnerModalProps> = ({
  jobId,
  toggleJobOwnersModal,
  updateJobList,
}) => {
  const { t } = useTranslation();
  const { openConfirmDialog } = useOpenConfirm();
  const queryKey = [QueryKeys.getJobOwnersLis, jobId];
  const toast = useToast();
  const { data: thisUser } = useAuthUser();

  const { remove: removeOwner, add: addOwner } = useUpdateQueryData(queryKey);

  const { data: owners = [], isLoading } = useReactQuery<Array<UserType>>({
    action: {
      apiFunc: jobsApi.getOwnersLis,
      key: queryKey,
      params: {
        id: jobId,
      },
    },
    // config: {
    //   select: (data: unknown) =>
    //     (data as Array<UserType>)?.filter(
    //       (owner) => owner?.userId !== thisUser?.id
    //     ),
    // },
  });

  const { mutate: removeOwnerApi } = useReactMutation({
    apiFunc: jobsApi.removeOwner,
  });

  const onCloseHandler = () => {
    toggleJobOwnersModal(false);
  };

  const onRemoveHandler = useCallback(
    (item: any) =>
      openConfirmDialog({
        title: t('remove_owner'),
        message: (
          <Flex className={classes.messageWrap}>
            <Typography>{t('r_y_s_w_remove')}</Typography>
            <Typography font="bold" mr={4} ml={4}>
              {item.fullName}
            </Typography>
            <Typography>{t('f_t_owner_lst')}</Typography>
          </Flex>
        ),
        confirmButtonText: t('remove'),
        cancelButtonText: t('cancel'),
        isAjaxCall: true,
        apiProps: {
          func: removeOwnerApi,
          variables: { jobId, userId: item.userId },
          onSuccess: () => {
            removeOwner(item.id);
            onCloseHandler();
            toast({
              type: 'success',
              icon: 'check-circle',
              title: t('owner_removed'),
              message: () => (
                <>
                  <Typography font="bold" mr={3}>
                    {item.fullName}
                  </Typography>
                  <Typography>{t('success_rem_f_ow_lis')}</Typography>
                </>
              ),
              actionButton: () => (
                <Button
                  onClick={() => toggleJobOwnersModal(true)}
                  schema="ghost-brand"
                  className={classes.actionBtn}
                  label={t('edit_owners')}
                  variant="text"
                />
              ),
            });
            updateJobList?.(item.id, true);
          },
        },
      }),
    []
  );
  const onSuccessAddHandler = (newUser: any, _, formRef) => {
    const newOwner = jobsNormalizer.getOwners([newUser])?.[0];
    addOwner(newOwner);
    formRef.resetForm();
    onCloseHandler();
    toast({
      type: 'success',
      icon: 'check-circle',
      title: t('owner_added'),
      message: () => (
        <>
          <Typography font="bold" mr={3}>
            {newOwner.fullName}
          </Typography>
          <Typography>{t('success_added_f_ow_lis')}</Typography>
        </>
      ),
      actionButton: () => (
        <Button
          onClick={() => toggleJobOwnersModal(true)}
          schema="ghost-brand"
          className={classes.actionBtn}
          label={t('edit_owners')}
          variant="text"
        />
      ),
    });
    updateJobList?.(newUser);
  };

  return (
    <ModalDialog onBack={onCloseHandler} onClose={onCloseHandler} isOpen>
      <ModalHeaderSimple
        title={t('job_owners')}
        backButtonProps={{
          className: classes.backButton,
          onClick: onCloseHandler,
        }}
        hideBack={false}
        closeButtonProps={{
          onClick: onCloseHandler,
        }}
      />
      <Form
        initialValues={{ owner: undefined }}
        transform={({ owner }: any) => ({
          userId: owner?.value,
          jobId,
        })}
        apiFunc={jobsApi.postOwner}
        onSuccess={onSuccessAddHandler}
        validationSchema={() =>
          formValidator.object().shape({
            owner: formValidator
              .object()
              .test('value', 'select_one_of_sug_cate', (val) => val?.value),
          })
        }
      >
        {({ handleSubmit, isSubmitting, dirty }) => (
          <JobOwnerForm
            owners={owners}
            onSubmit={handleSubmit}
            disabled={isSubmitting || !dirty}
          />
        )}
      </Form>
      <ModalBody>
        {isLoading ? (
          <Spinner />
        ) : (
          owners.map((item) => (
            <JobOwnerModalItem
              onRemove={onRemoveHandler}
              key={item.userId}
              item={{
                image: item.croppedImageUrl,
                fullName: item.fullName,
                usernameAtSign: item.usernameAtSign,
                userId: item.userId,
                id: item.id,
                role: item.pageRole,
              }}
            />
          ))
        )}
      </ModalBody>
    </ModalDialog>
  );
};

export default JobOwnerModal;
