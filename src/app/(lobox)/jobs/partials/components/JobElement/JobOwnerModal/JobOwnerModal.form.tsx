import React from 'react';
import AvatarCard from 'shared/uikit/AvatarCard';
import Button from 'shared/uikit/Button';
import FieldWrapper from 'shared/uikit/Form/FieldWrapper';
import Flex from 'shared/uikit/Flex';
import FuseAutoComplete from 'shared/uikit/AutoComplete/FuseAutoComplete';
import Icon from 'shared/uikit/Icon';
import Typography from 'shared/uikit/Typography';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import collectionToObjectByKey from 'shared/utils/toolkit/collectionToObjectByKey';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useGetPageMembers from 'shared/hooks/api-hook/useGetPageMembers';
import classes from './JobOwnerModal.form.module.scss';

interface JobOwnerFormProps {
  owners: Array<any>;
  onSubmit: () => void;
  disabled?: boolean;
}

const JobOwnerForm: React.FC<JobOwnerFormProps> = ({
  owners,
  disabled,
  onSubmit,
}) => {
  const { t } = useTranslation();
  const { openConfirmDialog } = useOpenConfirm();
  const { data, isLoading } = useGetPageMembers({
    enabled: true,
  });
  const ownersObject = collectionToObjectByKey(owners, 'userId');

  const peoples = React.useMemo(
    () =>
      data?.reduce((prev: Array<any>, cur: any) => {
        const isOwner = ownersObject?.[cur?.user?.id];
        if (cur.hideIt || cur.status !== 'ACCEPTED' || isOwner) {
          return prev;
        }
        return [
          ...prev,
          {
            label: cur?.user?.fullName,
            value: cur?.user?.id,
            image: cur.user?.image,
            job: cur.user?.job,
            username: cur?.user?.username,
          },
        ];
      }, []),
    [isLoading, ownersObject]
  );

  const handleConfirmation = (name: string) => () => {
    openConfirmDialog({
      title: t('add_owner'),
      message: (
        <Flex className={classes.messageWrap}>
          <Typography>{t('r_y_s_w_add')}</Typography>
          <Typography font="bold" mr={4} ml={4}>
            {name}
          </Typography>
          <Typography>{t('as_owner_q')}</Typography>
        </Flex>
      ),
      confirmButtonText: t('add'),
      cancelButtonText: t('cancel'),
      confirmCallback: onSubmit,
    });
  };

  return (
    <FieldWrapper
      name="owner"
      component={({ value, onChange }: any) => (
        <Flex className={classes.jobOwnerModalFormRoot}>
          <Flex className={classes.jobOwnerModal}>
            <FuseAutoComplete
              keys={['label', 'username']}
              editable
              value={value}
              onChange={onChange}
              inputWrapClassName={classes.asyncInputWrap}
              className={classes.asyncWrapper}
              inputStyle={classes.asyncInputStyle}
              helperText={t('add_members_who_ad_own')}
              name="owner"
              options={peoples}
              placeholder={t('type_mem_name')}
              rightIcon={
                <Icon color="primaryText" size={15} type="far" name="search" />
              }
              renderItem={({ item }: any) => (
                <AvatarCard
                  data={{
                    title: item.label,
                    image: item.image,
                    subTitle: item.username,
                  }}
                />
              )}
            />
            <Button
              className={classes.addBtn}
              label={t('add')}
              onClick={handleConfirmation(value?.label)}
              disabled={disabled}
            />
          </Flex>
        </Flex>
      )}
    />
  );
};

export default JobOwnerForm;
