import React from 'react';
import AvatarCard from 'shared/uikit/AvatarCard';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import useObjectLink from 'shared/uikit/utils/useObjectLink';
import { PAGE_ROLES } from 'shared/utils/constants/enums';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type RoleType from 'shared/utils/RoleType';
import classes from './JobOwnerModal.item.module.scss';

type ItemType = {
  userId: string;
  fullName: string;
  usernameAtSign: string;
  image: string;
  id: string;
  role: RoleType;
};

export interface JobOwnerModalItemProps {
  item: ItemType;
  onRemove: (item: ItemType) => void;
}

const JobOwnerModalItem: React.FC<JobOwnerModalItemProps> = ({
  item,
  onRemove,
}) => {
  const { t } = useTranslation();

  const onRemoveHandler = (event: React.MouseEvent<HTMLElement>) => {
    preventClickHandler(event);
    onRemove(item);
  };

  const onClickAvatar = useObjectLink({ objectId: item.userId });

  return (
    <AvatarCard
      key={item.userId}
      data={{
        image: item.image,
        title: item.fullName,
        subTitle: item.usernameAtSign,
        titleHelper: t(PAGE_ROLES[item.role]?.label),
      }}
      noHover
      action={
        <Flex className={classes.actions}>
          {/* <Button leftIcon="plus" label={t('add_as_owner')} /> */}
          <Button
            onClick={onRemoveHandler}
            schema="gray"
            leftIcon="times"
            label={t('remove_owner')}
            className={classes.desktopButton}
          />
          <IconButton
            onClick={onRemoveHandler}
            className={classes.mobileButton}
            colorSchema="graySecondary"
            size="md"
            name="times"
          />
        </Flex>
      }
      avatarProps={{
        className: classes.avatar,
        onClick: onClickAvatar,
      }}
    />
  );
};

export default JobOwnerModalItem;
