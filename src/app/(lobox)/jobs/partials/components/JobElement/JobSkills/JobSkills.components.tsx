import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import SkillList from 'shared/uikit/SkillPicker/SkillList.component';
import Typography from 'shared/uikit/Typography';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './JobSkills.components.module.scss';

const JobSkills: React.FC = () => {
  const { t } = useTranslation();
  const { getJobPropValue, hasEditAccess } = useJobElement();
  const skills = getJobPropValue('skills');

  const onClick = () => {};
  const isEmpty = !skills?.length;

  if (isEmpty && !hasEditAccess) return null;

  return (
    <SectionLayout
      title={t('skills')}
      classNames={{
        childrenWrap: cnj(classes.childrenWrap, isEmpty && classes.empty),
      }}
      onClick={onClick}
      visibleActionButton={false}
    >
      {isEmpty ? (
        <Typography color="colorIconForth2">
          {hasEditAccess ? t('add_skills') : t('no_skill_ent')}
        </Typography>
      ) : (
        <SkillList
          skills={skills}
          visibleAction={false}
          className={classes.skillWrap}
        />
      )}
    </SectionLayout>
  );
};

export default JobSkills;
