import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import useToast from 'shared/uikit/Toast/useToast';
import jobsApi from 'shared/utils/api/jobs';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { jobStatusKeys, jobStatuses } from 'shared/utils/constants/enums';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import UnpublishedNotice from 'shared/components/Organism/UnpublishedNotice';
import classes from './UnpublishedJobNotice.component.module.scss';

const UnpublishedJobNotice: React.FC = () => {
  const { t } = useTranslation();
  const { openConfirmDialog } = useOpenConfirm();
  const toast = useToast();
  const {
    isCreationMode,
    getJobPropValue,
    isJobCreationFormOpen,
    updateJobData,
  } = useJobElement();

  const { mutate: publishJob } = useReactMutation({
    apiFunc: jobsApi.setStatus,
  });

  const openConfirm = () => {
    openConfirmDialog({
      title: t('publish_job'),
      message: t('publish_job_confirm_helper'),
      confirmButtonText: t('publish'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func: publishJob,
        variables: {
          status: jobStatusKeys.open,
          id: getJobPropValue('id'),
        },
        onSuccess: () => {
          toast({
            type: 'success',
            icon: 'check-circle',
            message: t('job_published_successfully'),
          });
          updateJobData({
            status: jobStatuses.find((i) => i.value === jobStatusKeys.open),
          });
        },
      },
    });
  };

  return (
    <UnpublishedNotice
      title={t('unpublished_job')}
      subTitle={t('unpublished_job_hint')}
      onPublishClick={openConfirm}
      className={cnj(isJobCreationFormOpen && classes.wrapper)}
    />
  );
};

export default UnpublishedJobNotice;
