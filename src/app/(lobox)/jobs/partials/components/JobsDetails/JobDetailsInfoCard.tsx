import React, { useCallback, useState } from 'react';
import Typography from 'shared/uikit/Typography';
import InfoCard from 'shared/uikit/InfoCard';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import useTranslation from 'shared/utils/hooks/useTranslation';
import {
  searchGroupTypes,
  searchFilterQueryParams,
} from 'shared/constants/search';
import { useSearchParams } from 'next/navigation';
import classes from './JobDetailsInfoCard.module.scss';
import useSaveJobs from '../../hooks/useSaveJobs';

const JobDetailsInfoCard = () => {
  const [closedInfoCardIds, setClosedInfoCardIds] = useState<string[]>([]);
  const { getJobPropValue, searchGroupType, jobElement, updateJobData } =
    useJobElement();
  const searchParams = useSearchParams();
  const queryJobId = searchParams.get(searchFilterQueryParams.currentEntityId);
  const closeInfoCard = useCallback(
    () => setClosedInfoCardIds((prev) => [...prev, jobElement?.id]),
    [setClosedInfoCardIds, jobElement]
  );
  const { t } = useTranslation();
  const isApplied = getJobPropValue('isApplied');
  const isSaved = getJobPropValue('isSaved');
  const { saveJob } = useSaveJobs();
  const saveJobHandler = () => {
    saveJob({
      job: jobElement,
      onSuccess: (res) => {
        closeInfoCard();
        updateJobData({ isSaved: true, jobSaveId: res?.id });
      },
    });
  };

  const isJobInfoCardClosed = closedInfoCardIds?.includes(jobElement?.id);
  const infoCardLabel =
    searchGroupType === searchGroupTypes.APPLIED &&
    !isApplied &&
    !!queryJobId ? (
      t('you_have_not_applied_to_this_job')
    ) : searchGroupType === searchGroupTypes.SAVED &&
      !isSaved &&
      !!queryJobId ? (
      <>
        {t('you_have_not_saved_this_job')}&nbsp;
        <Typography
          color="brand"
          className={classes.saveButton}
          onClick={saveJobHandler}
        >
          {t('save')}
        </Typography>
        &nbsp;
        {t('this_job_too')}
      </>
    ) : (
      ''
    );

  return (
    jobElement?.id &&
    !isJobInfoCardClosed &&
    infoCardLabel && (
      <InfoCard
        classNames={{
          wrapper: classes.infoCardWrapper,
          label: classes.label,
        }}
        rightIconProps={{
          onClick: closeInfoCard,
        }}
      >
        {infoCardLabel}
      </InfoCard>
    )
  );
};

export default JobDetailsInfoCard;
