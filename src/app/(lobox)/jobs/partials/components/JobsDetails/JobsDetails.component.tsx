import React, { useEffect, useMemo } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import { useQueryClient } from '@tanstack/react-query';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import type { IJob } from 'shared/types/job';
import type { QueryKeyType } from 'shared/types/general';
import { useMultiStepFormState } from 'shared/hooks/useMultiStepForm';
import dynamic from 'next/dynamic';
import { useInView } from 'react-intersection-observer';
import Skeleton from '@shared/uikit/Skeleton';
import JobOverview from '../JobElement/JobOverview';
import JobElementHeader from '../JobElement/JobElementHeader';
import classes from './JobsDetails.component.module.scss';

const JobLocation = dynamic(() => import('../JobElement/JobLocation'), {
  ssr: false,
  loading: () => <Skeleton className="h-[600px]" />,
});
const JobApplicationForm = dynamic(() => import('../JobApplicationForm'), {
  ssr: false,
});
const JobSkills = dynamic(() => import('../JobElement/JobSkills'), {
  ssr: false,
});
const JobLanguage = dynamic(() => import('../JobElement/JobLanguages'), {
  ssr: false,
});

const JobDescription = dynamic(() => import('../JobElement/JobDescription'), {
  ssr: false,
  loading: () => <Skeleton className="!h-[600px]" />,
});
const JobCompanyInfo = dynamic(() => import('../JobElement/JobCompanyInfo'), {
  ssr: false,
  loading: () => <Skeleton className="h-[300px]" />,
});
const JobDetailsInfoCard = dynamic(() => import('./JobDetailsInfoCard'), {
  ssr: false,
  loading: () => <Skeleton className="h-[500px]" />,
});
const JobCreatedBy = dynamic(() => import('../JobElement/JobCreatedBy'), {
  ssr: false,
});
const JobCreationForm = dynamic(() => import('../JobElement/JobCreationForm'), {
  ssr: false,
});
const JobIdSection = dynamic(
  () => import('@app/jobs/partials/components/JobElement/JobIdSection'),
  {
    ssr: false,
  }
);

export interface JobDetailsProps {
  className?: string;
  queryKey?: QueryKeyType;
  isLoggedIn?: boolean;
  content?: IJob[];
}

const JobDetails = ({ className, isLoggedIn, content }: JobDetailsProps) => {
  const { getJobPropValue, isJobCreationFormOpen, jobElement } =
    useJobElement();
  const queryClient = useQueryClient();
  const [ref, inView, entry] = useInView({
    triggerOnce: true,
    fallbackInView: true,
    root: document.getElementById(PROFILE_SCROLL_WRAPPER),
    rootMargin: `20px 0px 0px 0px`,
    threshold: 0.1,
  });

  const isJobApplicationFormOpen =
    useMultiStepFormState('jobApplication').isOpen;
  const isAddExternalJobLink = getJobPropValue('isAddExternalJobLink');

  const jobId = jobElement?.id;

  const isInfoCardActive = useMemo(() => {
    const unsavedJobs =
      queryClient.getQueryData<string[]>([
        QueryKeys.unsavedJobsDuringThisSession,
      ]) || [];
    return content?.[0]?.id === jobId && !unsavedJobs?.includes(jobId);
  }, [queryClient, jobElement, content]);

  useEffect(() => {
    const element = document.getElementById(PROFILE_SCROLL_WRAPPER);
    element?.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, [jobId]);

  return (
    <Flex className={cnj(className)}>
      {isLoggedIn && isInfoCardActive && <JobDetailsInfoCard />}
      <JobElementHeader
        className={classes.jobHeader}
        disableActions={!isLoggedIn}
      />
      <JobOverview />
      <JobDescription />
      <JobLocation />
      {!isAddExternalJobLink && <JobSkills />}
      {!isAddExternalJobLink && <JobLanguage />}
      {isLoggedIn && <JobCreatedBy />}
      <div ref={ref} style={{ minHeight: 10 }}>
        {inView && <JobCompanyInfo />}
      </div>
      {isBusinessApp && <JobIdSection />}
      {isJobApplicationFormOpen && <JobApplicationForm />}
      {isJobCreationFormOpen && <JobCreationForm />}
    </Flex>
  );
};

export default JobDetails;
