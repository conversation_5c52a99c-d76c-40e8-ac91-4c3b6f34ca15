import React from 'react';
import Svg from 'shared/svg/SvgExperience';
import SavedJobsIcon from 'shared/svg/SavedJobsIcon';
import { routeNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import useHistory from 'shared/utils/hooks/useHistory';
import type { EmptyStateProps } from 'shared/components/Organism/EmptyState/EmptyState';
import classes from './JobsEmptyState.component.module.scss';
import JobsEmptyStateLayout from './JobsEmptyState.layout';

export interface JobsEmptyStateProps {
  className?: string;
  visibleFooter?: boolean;
  emptyStateProps?: Omit<EmptyStateProps, 'image'>;
  image?: React.ReactNode;
}

const JobsEmptyState: React.FC<JobsEmptyStateProps> = ({
  className,
  visibleFooter = true,
  emptyStateProps,
  image,
}) => {
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();
  const history = useHistory();

  return isBusinessApp ? (
    <JobsEmptyStateLayout
      className={className}
      image={image || <Svg />}
      visibleFooter={visibleFooter}
      emptyStateProps={emptyStateProps}
    />
  ) : (
    <JobsEmptyStateLayout
      className={className}
      visibleFooter={false}
      image={<SavedJobsIcon />}
      emptyStateProps={{
        caption: `${t('hi')}, ${authUser?.fullName}!`,
        message: t('empty_saved_jobs'),
        messageProps: {
          color: 'muteMidGray_coal',
          className: classes.text,
        },
        captionProps: {
          color: 'thirdText',
        },
        action: {
          title: t('discover_jobs'),
          onClick: () => history.push(routeNames.discoverJobs),
        },
        actionProps: {
          schema: 'primary-blue',
          className: classes.text,
        },
        ...emptyStateProps,
      }}
    />
  );
};

export default JobsEmptyState;
