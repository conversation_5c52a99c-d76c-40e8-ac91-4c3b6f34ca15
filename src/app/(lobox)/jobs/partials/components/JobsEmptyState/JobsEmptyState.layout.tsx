import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import FooterDesktop from 'shared/uikit/Footer/FooterDesktop';
import EmptyState from 'shared/components/Organism/EmptyState/EmptyState';
import Svg from 'shared/svg/SvgExperience';
import urls from 'shared/constants/urls';
import type { EmptyStateProps } from 'shared/components/Organism/EmptyState/EmptyState';
import classes from './JobsEmptyState.layout.module.scss';

export interface JobsEmptyStateProps {
  className?: string;
  visibleFooter?: boolean;
  emptyStateProps?: Omit<EmptyStateProps, 'image'>;
  image?: React.ReactNode;
}

const JobsEmptyStateLayout: React.FC<JobsEmptyStateProps> = ({
  className,
  visibleFooter = true,
  emptyStateProps,
  image,
}) => (
  <Flex className={cnj(classes.jobsEmptyStateRoot, className)}>
    <Flex className={classes.jobsEmptyState}>
      <EmptyState
        image={image || <Svg />}
        className={classes.emptyClassName}
        contentClassName={classes.contentClassName}
        {...emptyStateProps}
      />
    </Flex>
    {visibleFooter && <FooterDesktop baseUrl={urls.base} logoGray />}
  </Flex>
);

export default JobsEmptyStateLayout;
