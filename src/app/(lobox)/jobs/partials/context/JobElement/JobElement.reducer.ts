import type { <PERSON><PERSON><PERSON> } from 'shared/types/job';
import type { QueryKeyType } from 'shared/types/general';

export type JobLanguageType = 'en' | 'tr' | 'es';
export type JobElementModeType = 'create' | 'edit';
export type ActiveStepType = 1 | 2 | 3 | 4 | 5;

type InitialStateProps = {
  jobId: undefined;
  mode: JobElementModeType;
  activeStep: ActiveStepType;
  job: IJob;
  queryKey: QueryKeyType;
  jobLanguage: JobLanguageType;
  isPreviewMode?: boolean;
  isJobOwnersModalOpen: boolean;
};

export const initialState: InitialStateProps = {
  mode: 'create',
  jobId: undefined,
  queryKey: undefined,
  activeStep: 1,
  jobLanguage: 'en',
  isPreviewMode: false,
  isJobOwnersModalOpen: false,
};

type ActionType =
  | { type: 'SET_ACTIVE_STEP'; payload: ActiveStepType }
  | { type: 'SET_JOB_LANGUAGE'; payload: JobLanguageType }
  | { type: 'SET_IS_JOB_FORM_OPEN'; payload: boolean }
  | { type: 'SET_IS_JOB_OWNER_MODAL_OPEN'; payload: boolean }
  | { type: 'SET_PREVIEW_MODE'; payload: boolean };

type StateType = typeof initialState;

export function jobElementReducer(
  state: StateType,
  action: ActionType
): StateType {
  switch (action.type) {
    case 'SET_ACTIVE_STEP':
      return {
        ...state,
        activeStep: action.payload,
      };
    case 'SET_JOB_LANGUAGE':
      return {
        ...state,
        jobLanguage: action.payload,
      };
    case 'SET_IS_JOB_OWNER_MODAL_OPEN':
      return {
        ...state,
        isJobOwnersModalOpen: action.payload,
      };
    case 'SET_PREVIEW_MODE':
      return {
        ...state,
        isPreviewMode: action.payload,
      };

    default: {
      throw new Error(`Unsupported action type at APP Reducer`);
    }
  }
}
