import { useCallback, useEffect, useMemo } from 'react';
import useAuthUser from 'shared/utils/hooks/useAuthUser';
import { useQueryClient } from '@tanstack/react-query';
import useGetPageMembers from 'shared/hooks/api-hook/useGetPageMembers';
import type { CollaboratorType } from 'shared/components/Organism/types';
import useJobElement from './useJobElement';

const TEMP_COLLABORATORS_KEY = 'TEMP_COLLABORATORS_KEY';

type ReturnType = {
  collaborators: CollaboratorType[];
  addCollaboratorCache: (user: CollaboratorType) => void;
  removeCollaboratorCache: (user: CollaboratorType) => void;
  associate: CollaboratorType;
  setAssociate: (user: CollaboratorType) => void;
  isLoading: boolean;
};

export function useJobCollaborators(
  onInitialData: (x: {
    collaborators: CollaboratorType[];
    associate: CollaboratorType;
  }) => void
): ReturnType {
  const { data: thisUser } = useAuthUser();
  const { getJobPropValue, isCreationMode, jobElement, queryResult } =
    useJobElement();
  const isLoadingJob = queryResult?.isLoading;
  const jobId = jobElement?.id;
  const creatorUserId = jobElement?.createdUserId;
  const queryKey = useMemo(() => [TEMP_COLLABORATORS_KEY, jobId], [jobId]);

  const queryClient = useQueryClient();
  const initialCollaborators = getJobPropValue('collaborators');
  const initialAssociate = getJobPropValue('associate');
  const { data: members, isLoading: isLoadingMembers } = useGetPageMembers({
    enabled: true,
  });

  const isLoading = isLoadingJob || isLoadingMembers;

  const data = queryClient.getQueryData(queryKey);
  const {
    collaborators = [],
    associate,
  }: { collaborators: CollaboratorType[]; associate: CollaboratorType } =
    (data || {}) as any;

  if (!data && members?.length) {
    // Set the initial Data
    if (isCreationMode) {
      const thisMember: CollaboratorType = {
        ...members?.find((member) => member?.user?.id === thisUser?.id),
      };
      queryClient.setQueryData(queryKey, {
        collaborators: [{ ...thisMember, collaboratorRole: 'CREATOR' }],
        associate: { ...thisMember, collaboratorRole: 'CREATOR' },
      } as {
        collaborators: CollaboratorType[];
        associate: CollaboratorType;
      });
    } else {
      const initialCollaboratorIds: string[] = initialCollaborators?.map(
        (item: any) => item?.id
      );

      let allCollaborators: CollaboratorType[] = members
        ?.filter((member) => initialCollaboratorIds?.includes(member?.user?.id))
        ?.map((item) => ({
          ...item,
          collaboratorRole: 'COLLABORATOR',
        }));
      const creator = members?.find(
        (member) => member?.user?.id === creatorUserId
      );
      allCollaborators = allCollaborators?.filter(
        (collaborator) => collaborator?.user?.id !== creator?.user?.id
      );
      allCollaborators.unshift({ ...creator, collaboratorRole: 'CREATOR' });
      const _associate = members?.find(
        (member) => member?.user?.id === initialAssociate?.id
      );

      // TODO somehow set the associate
      queryClient.setQueryData<{
        collaborators: CollaboratorType[];
        associate: CollaboratorType;
      }>(queryKey, {
        collaborators: allCollaborators,
        associate: {
          ..._associate,
          collaboratorRole:
            _associate?.user?.id === creator?.user?.id
              ? 'CREATOR'
              : 'COLLABORATOR',
        },
      });
    }
    const initialData: {
      collaborators: CollaboratorType[];
      associate: CollaboratorType;
    } = queryClient.getQueryData(queryKey);
    onInitialData({
      collaborators: initialData?.collaborators,
      associate: initialData?.associate,
    });
  }

  const addCollaboratorCache = useCallback(
    (user: CollaboratorType) => {
      queryClient.setQueryData(queryKey, {
        associate,
        collaborators: [...collaborators, user],
      });
    },
    [queryClient, queryKey, collaborators, associate]
  );
  function removeCollaboratorCache(user: CollaboratorType) {
    queryClient.setQueryData(queryKey, {
      associate,
      collaborators: collaborators?.filter((item) => item?.id !== user?.id),
    });
  }
  function setAssociate(user: CollaboratorType) {
    queryClient.setQueriesData(queryKey, {
      collaborators,
      associate: user,
    });
  }

  useEffect(() => {
    if (!associate?.user?.id) return;
    const associateIsInCollaborators = collaborators?.find(
      (collaborator) => collaborator?.user?.id === associate?.user?.id
    );
    if (associateIsInCollaborators) return;

    addCollaboratorCache({ ...associate });
  }, [associate, collaborators, addCollaboratorCache]);

  return {
    collaborators,
    addCollaboratorCache,
    removeCollaboratorCache,
    associate,
    setAssociate,
    isLoading,
  };
}
