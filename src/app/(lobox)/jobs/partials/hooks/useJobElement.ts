import { useMemo } from 'react';
import jobsApi from 'shared/utils/api/jobs';
import { jobsDb, jobStatusKeys } from 'shared/utils/constants/enums';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import get from 'lodash/get';
import isEqual from 'lodash/isEqual';
import { useQueryClient } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import { useJobsState } from 'shared/providers/JobsPorvider/jobs.provider';
import {
  searchGroupTypes,
  searchFilterQueryParams,
} from 'shared/constants/search';
import type { IJob } from 'shared/types/job';
import type { QueryKeyType } from 'shared/types/general';
import type { QueryObserverResult } from '@tanstack/react-query';
import type { JobKeyType } from 'shared/types/search';
import { mutableStore } from 'shared/constants/mutableStore';
import { useSearchState } from 'shared/contexts/search/search.provider';
import { useSearchParams } from 'next/navigation';
import type { ActiveStepType } from '../context/JobElement/JobElement.reducer';
import {
  useJobElementDispatch,
  useJobElementState,
} from '../context/JobElement/JobElement.provider';

type UseJobElement = {
  isCreationMode: boolean;
  isPreviewMode: boolean;
  isPublished: boolean;
  activeStep: number;
  pageTitle: string;
  jobElement: IJob;
  hasEditAccess: boolean;
  isJobCreationFormOpen: boolean;
  isJobOwnersModalOpen: boolean;
  jobLanguage: { value: string; label: string; id: string };
  setJobCreationFormData: (formData: any) => void;
  getJobPropValue: (key: JobKeyType | JobKeyType[]) => any;
  toggleJobOwnersModal: (val: boolean) => void;
  setActiveStep: (val: ActiveStepType) => void;
  togglePreviewMode: (val: boolean) => void;
  updateJobData: (data: Partial<IJob>) => void;
  queryResult: QueryObserverResult<IJob>;
  queryKey?: QueryKeyType;
  searchGroupType: string;
  jobElementQueryKey: QueryKeyType;
  hashtags?: string[];
};

const useJobElement = (): UseJobElement => {
  const { t } = useTranslation();
  const searchJobSelectedItemId = useSearchState('searchJobSelectedItemId');

  const mode = useJobElementState('mode');
  const jobId = searchJobSelectedItemId || useJobElementState('jobId');
  const listQueryKey = useJobElementState('queryKey');
  const isCreationMode = mode === 'create';
  const activeStep = useJobElementState('activeStep');
  const isJobCreationFormOpen = useJobsState('isJobCreationFormOpen');
  const isJobOwnersModalOpen = useJobElementState('isJobOwnersModalOpen');
  const isPreviewMode = useJobElementState('isPreviewMode');
  const jobElementDispatch = useJobElementDispatch();
  const pageTitle = isCreationMode ? t('create_job') : t('job_details');
  const queryKey = [QueryKeys.jobDetails, isCreationMode ? 'create' : jobId];
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();

  const queryResult = useReactQuery<IJob>({
    action: {
      key: queryKey,
      apiFunc: jobsApi.getJobDetails,
      params: { id: jobId, isBusinessApp },
    },
    config: { enabled: !!jobId, refetchOnMount: false },
  });
  const data = queryResult?.data;
  const languageId = '1';

  const setActiveStep = (value: ActiveStepType) => {
    jobElementDispatch({ type: 'SET_ACTIVE_STEP', payload: value });
  };
  const togglePreviewMode = (value: boolean) => {
    jobElementDispatch({ type: 'SET_PREVIEW_MODE', payload: value });
  };
  const toggleJobOwnersModal = (value: boolean) => {
    jobElementDispatch({ type: 'SET_IS_JOB_OWNER_MODAL_OPEN', payload: value });
  };
  const getJobPropValue = (key: JobKeyType | JobKeyType[]) => get(data, key);

  const setJobCreationFormData = debounce((formData: IJob) => {
    if (!isEqual(formData, data)) {
      queryClient.setQueryData(queryKey, formData);
    }
  }, 250);

  const updateJobData = (newData = {}) =>
    queryClient.setQueryData(queryKey, {
      ...data,
      ...newData,
    });

  const jobLanguage = useMemo(
    () =>
      jobsDb.LANGUAGES?.find((i) => i.id === (data?.languageId || languageId)),
    [data?.languageId]
  );

  const hasEditAccess =
    isBusinessApp && (isCreationMode || getJobPropValue('isJobOwner'));
  const isPublished =
    getJobPropValue('status') &&
    getJobPropValue('status')?.value !== jobStatusKeys.unpublished;

  const searchGroupType =
    searchParams.get(searchFilterQueryParams.searchGroupType) ||
    searchGroupTypes.ALL;

  mutableStore.selectedJobQueryKey = queryKey;

  return {
    isCreationMode,
    isPreviewMode,
    isPublished,
    activeStep,
    pageTitle,
    isJobCreationFormOpen,
    jobLanguage,
    setJobCreationFormData,
    hasEditAccess,
    isJobOwnersModalOpen,
    toggleJobOwnersModal,
    getJobPropValue,
    setActiveStep,
    togglePreviewMode,
    updateJobData,
    jobElement: data,
    queryResult,
    queryKey: listQueryKey,
    jobElementQueryKey: queryKey,
    searchGroupType,
  };
};

export default useJobElement;
