import React from 'react';
import jobsApi from 'shared/utils/api/jobs';
import { routeNames } from 'shared/utils/constants/routeNames';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Button from 'shared/uikit/Button';
import Typography from 'shared/uikit/Typography';
import useToast from 'shared/uikit/Toast/useToast';
import useHistory from 'shared/utils/hooks/useHistory';
import type { IJob } from 'shared/types/job';
import type { UseMutationResult } from '@tanstack/react-query';
import { getToasterErrorMessage } from 'shared/utils/toolkit/getToasterErrorMessage';
import classes from './useSaveJobs.module.scss';

type SaveJobParams = {
  job: IJob;
  onSuccess: (res: any) => void;
};

type UseSaveJobsProps<T = unknown, E = unknown> = Omit<
  UseMutationResult<T, E>,
  'mutate'
> & {
  saveJob: (params: SaveJobParams) => void;
};

const useSaveJobs = (): UseSaveJobsProps => {
  const toast = useToast();
  const { t } = useTranslation();
  const history = useHistory();

  const { mutate: saveJobApi, ...rest } = useReactMutation({
    apiFunc: jobsApi.saveJob,
  });

  const viewSavedJobHandler = () => {
    history.push(routeNames.savedJobs);
  };

  const saveJob = ({ job, onSuccess }: SaveJobParams) => {
    saveJobApi(
      { id: job.id },
      {
        onSuccess: (res: any) => {
          onSuccess(res);
          toast({
            type: 'success',
            icon: 'check-circle',
            title: t('job_saved'),
            message: () => (
              <Typography className={classes.inlineText}>
                <Typography font="700" className={classes.inlineText}>
                  {job?.title?.label}&nbsp;
                </Typography>
                {t('toast_save_job')}
              </Typography>
            ),
            actionButton: () => (
              <Button
                onClick={viewSavedJobHandler}
                schema="ghost-brand"
                label={t('view_saved_jbs')}
                variant="text"
              />
            ),
          });
        },
        onError: (err: any) => {
          const { title, message } = getToasterErrorMessage(err, t);
          toast({
            type: 'error',
            icon: 'times-circle',
            message,
            title,
          });
        },
      }
    );
  };

  return { saveJob, ...rest };
};

export default useSaveJobs;
