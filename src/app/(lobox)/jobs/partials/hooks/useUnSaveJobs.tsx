import React from 'react';
import useToast from 'shared/uikit/Toast/useToast';
import Typography from 'shared/uikit/Typography';
import Button from 'shared/uikit/Button';
import jobsApi from 'shared/utils/api/jobs';
import { routeNames } from 'shared/utils/constants/routeNames';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useHistory from 'shared/utils/hooks/useHistory';
import type { IJob } from 'shared/types/job';
import type { UseMutationResult } from '@tanstack/react-query';
import { getToasterErrorMessage } from 'shared/utils/toolkit/getToasterErrorMessage';
import classes from './useUnSaveJobs.module.scss';

type SaveJobParams = {
  job: IJob;
  onSuccess: (res: any) => void;
};

type UseUnSaveJobsProps<T = unknown, E = unknown> = Omit<
  UseMutationResult<T, E>,
  'mutate'
> & {
  unSaveJob: (params: SaveJobParams) => void;
};

const useUnSaveJobs = (): UseUnSaveJobsProps => {
  const toast = useToast();
  const { t } = useTranslation();
  const history = useHistory();
  const { mutate: unSaveJobApi, ...rest } = useReactMutation({
    apiFunc: jobsApi.unSaveJob,
  });
  const viewSavedJobHandler = () => {
    history.push(routeNames.savedJobs);
  };

  const unSaveJob = ({ job, onSuccess }: SaveJobParams) => {
    unSaveJobApi(
      { id: job.jobSaveId },
      {
        onSuccess: (res: any) => {
          onSuccess(res);
          toast({
            type: 'success',
            icon: 'check-circle',
            title: t('job_unsaved'),
            message: () => (
              <Typography className={classes.inlineText}>
                <Typography font="700" className={classes.inlineText}>
                  {job?.title?.label}&nbsp;
                </Typography>
                {t('toast_unsave_job')}
              </Typography>
            ),
            actionButton: () => (
              <Button
                onClick={viewSavedJobHandler}
                schema="ghost-brand"
                label={t('view_saved_jbs')}
                variant="text"
              />
            ),
          });
        },
        onError: (err: any) => {
          const { title, message } = getToasterErrorMessage(err, t);
          toast({
            type: 'error',
            icon: 'times-circle',
            message,
            title,
          });
        },
      }
    );
  };

  return { unSaveJob, ...rest };
};

export default useUnSaveJobs;
