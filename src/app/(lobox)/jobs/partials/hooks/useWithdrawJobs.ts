import jobsApi from 'shared/utils/api/jobs';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import type { IJob } from 'shared/types/job';
import type { UseMutationResult } from '@tanstack/react-query';

type SaveJobParams = {
  job: IJob;
  onSuccess: (res: any) => void;
};

type useWithdrowJobsProps<T = unknown, E = unknown> = Omit<
  UseMutationResult<T, E>,
  'mutate'
> & {
  withdrawJob: (params: SaveJobParams) => void;
};

const useWithdrawJobs = (): useWithdrowJobsProps => {
  const { mutate, ...rest } = useReactMutation({
    apiFunc: jobsApi.withdraw,
  });
  const { openConfirmDialog } = useOpenConfirm();
  const { t } = useTranslation();

  const withdrawJob = ({ job, onSuccess }: SaveJobParams) => {
    openConfirmDialog({
      title: t('withdraw_the_job'),
      message: t('r_y_s_witd_job'),
      confirmButtonText: t('withdraw'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func: mutate,
        variables: { id: job?.applicationId },
        onSuccess,
      },
    });
  };

  return { withdrawJob, ...rest };
};

export default useWithdrawJobs;
