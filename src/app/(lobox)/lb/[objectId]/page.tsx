'use client';

import { getUserNameByObjectId } from 'shared/utils/api/user';
import { routeNames } from 'shared/utils/constants/routeNames';
import { useRouter } from 'next/navigation';
import ProfileLoading from './loading';

export default function Page({ params }) {
  const objectId = params?.objectId;
  const router = useRouter();

  try {
    getUserNameByObjectId({ objectId }).then((data) => {
      const username = data?.username;
      if (username) {
        router.prefetch(`/${username}`);
        router.replace(username ? `/${username}` : routeNames.home);
      } else {
        router.replace(`/404`);
      }
    });
  } catch (e) {
    router.replace(`/404`);
  }
  return <ProfileLoading />;
}
