'use client';

import React from 'react';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import Message from 'shared/components/Organism/Message';

const Messages = (): JSX.Element => (
  <PermissionsGate
    scopes={[SCOPES.canSeeMessagesScreen]}
    renderFallback={<PermissionDeniedAlert />}
  >
    <Message variant="page" />
  </PermissionsGate>
);

export default Messages;
