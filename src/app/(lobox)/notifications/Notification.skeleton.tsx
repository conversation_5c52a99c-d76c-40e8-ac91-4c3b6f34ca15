import React from 'react';
import Flex from 'shared/uikit/Flex';
import Skeleton from 'shared/uikit/Skeleton';
import { NotificationSkeleton } from 'shared/components/Organism/Notification';
import cnj from '@shared/uikit/utils/cnj';
import classes from './Notification.skeleton.module.scss';

const list = Array(16).fill(0);

interface Props {
  visibleHeader?: boolean;
  className?: string;
}

const NotificationScreenSkeleton: React.FC<Props> = ({
  visibleHeader = true,
  className,
}) => (
  <Flex className={cnj(classes.notificationContainer, className)}>
    {visibleHeader && (
      <Flex flexDir="row" className={classes.header}>
        <Skeleton className={classes.btn1} />
        <Skeleton className={classes.btn2} />
        <Skeleton className={classes.btn3} />
      </Flex>
    )}
    {list.map((_, i) => (
      <NotificationSkeleton key={`notif_${i}`} />
    ))}
  </Flex>
);

export default NotificationScreenSkeleton;
