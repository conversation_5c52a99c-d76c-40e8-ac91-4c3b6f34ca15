'use client';

import JobsTabLayout from 'shared/components/layouts/JobsLayouts/JobsTabLayout/JobTabLayout.component';
import type { Filter } from 'shared/components/Organism/NotificationList/partials/components/HeaderActions';
import NotificationHeaderActions from 'shared/components/Organism/NotificationList/partials/components/HeaderActions';
import useGetNotifications from 'shared/hooks/api-hook/useGetNotifications';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useHistory from 'shared/utils/hooks/useHistory';

import { useState } from 'react';
import classes from './page.module.scss';

interface LayoutProps {
  children: (x: any) => ReactNode;
}

export default function NotificationLayout({ children }: LayoutProps) {
  const { t } = useTranslation();
  const history = useHistory();
  const [selectedFilter, setFilter] = useState<Filter>('all');
  const handleFilterClicked = (f: Filter) => {
    setFilter(f);
  };
  const onlyUnseen = selectedFilter === 'unseen';
  const onBackHandler = () => history.goBack();

  const { data = [] } = useGetNotifications({
    onlyUnseen,
  });

  const list = onlyUnseen ? data.filter(({ seen }) => !seen) : data;

  return (
    <JobsTabLayout
      tabs={[]}
      headerProps={{
        visibleHeaderDivider: true,
        title: t('notifications'),
        backButtonProps: {
          className: classes.hideBackButton,
          onClick: onBackHandler,
        },
      }}
      isLoading={false}
      renderHeader={() => (
        <NotificationHeaderActions
          selectedFilter={selectedFilter}
          onFilterClick={handleFilterClicked}
          markAllReadVisibility={!!list?.length}
          className={classes.notifHeader}
          buttonClassName={classes.buttonClassName}
        />
      )}
      linksRootClassName={classes.linksRootClassName}
    >
      {children?.({ setFilter, selectedFilter })}
    </JobsTabLayout>
  );
}
