'use client';

import React, { useState } from 'react';
import Flex from 'shared/uikit/Flex';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import NotificationList from 'shared/components/Organism/NotificationList';
import {
  SwitchNotificationContent,
  SwitchNotificationTrigger,
  useSwitchNotificationModalState,
} from '@shared/components/Organism/SwitchNotification';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import NotificationLayout from './NotificationLayout';
import classes from './page.module.scss';

const Notifications: React.FC = () => {
  const [selectedPortal, setSelectedPortal] = useState<string | undefined>(
    undefined
  );

  const {
    isLoading,
    authUserData,
    avatar,
    businessProfiles,
    isModalOpen,
    onCloseModal,
    setModalOpen,
  } = useSwitchNotificationModalState({
    isLoading: false,
    selected: selectedPortal,
    setSelected: setSelectedPortal,
  });

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeNotificationScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <FixedRightSideModal isOpen={isModalOpen} onClickOutside={onCloseModal}>
        <SwitchNotificationContent
          onClose={onCloseModal}
          onSelect={setSelectedPortal}
          businessProfiles={businessProfiles}
          selected={selectedPortal}
          userData={authUserData}
        />
      </FixedRightSideModal>
      <NotificationLayout>
        {({ setFilter, selectedFilter }) => (
          <Flex className={classes.notificationContainer}>
            <SwitchNotificationTrigger
              setOpen={setModalOpen}
              data={avatar}
              className={classes.SwitchNotificationsTrigger}
            />
            <NotificationList
              userId={selectedPortal}
              userType={selectedPortal ? 'PAGE' : 'PERSON'}
              setFilter={setFilter}
              selectedFilter={selectedFilter}
            />
          </Flex>
        )}
      </NotificationLayout>
    </PermissionsGate>
  );
};

export default Notifications;
