'use client';

import SearchListWithDetailsLayout from '@shared/components/layouts/SearchListWithDetailsLayout';
import { jobsEndpoints, QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import type { JobPiplineData } from '@shared/types/pipelineProps';
import { sortPipelinesNormalizer } from '@shared/utils/normalizers/pipelinesNormalizer';
import PipelineHeader from './PipelinePage/PipelineHeader';
import PipelineStages from './PipelinePage/PipelineStages';
import PipelinePageProvider from './PipelinePageProvider';

interface IPipelinePageProps {
  id: string;
}

const PipelinePage = ({ id }: IPipelinePageProps) => {
  const { data, isLoading, refetch } = useReactQuery<JobPiplineData>({
    action: {
      key: [QueryKeys.getPipeline, id],
      url: jobsEndpoints.getPipeline(id),
      beforeCache: (innerData: any) => sortPipelinesNormalizer(innerData),
    },
  });

  if (!data || isLoading) return null;

  return (
    <PipelinePageProvider pipelinesData={data} refetch={refetch}>
      <SearchListWithDetailsLayout
        groups={[]}
        hasFilterIcon={false}
        classNames={{
          contentRoot: '!max-w-none !pt-20 h-full',
          headerRoot: '!m-0 !max-w-none',
          emptyState: '!m-0 !max-w-none !px-0 !pr-[14px]',
          filtersWrapper: '!w-auto',
          rightWrapper: '!pl-12 flex-1 h-full',
        }}
        headerComponents={<PipelineHeader jobData={data.job} />}
        listComponent={<PipelineStages data={data} />}
      />
    </PipelinePageProvider>
  );
};

export default PipelinePage;
