import useTranslation from '@shared/utils/hooks/useTranslation';
import Typography from '@shared/uikit/Typography';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import Flex from '@shared/uikit/Flex';
import Button from '@shared/uikit/Button';
import { useFormikContext } from 'formik';
import type {
  BulkActionType,
  BulkPipelineFormProps,
} from '@shared/types/pipelineProps';
import SubmitButton from '@shared/uikit/Form/SubmitButton';

interface BulkStageMenuFooterProps {
  selectedCounts: number;
  onDiscard: VoidFunction;
}

const BulkStageMenuFooter: React.FC<BulkStageMenuFooterProps> = ({
  selectedCounts,
  onDiscard,
}) => {
  const { t } = useTranslation();
  const { values, isValid, setFieldValue } =
    useFormikContext<BulkPipelineFormProps>();

  const handleDiscard = () => {
    if (values?.action?.value === 'todo_2') {
      return setFieldValue('action', { value: 'todo_1', label: 'to_do' });
    }
    return onDiscard();
  };

  return (
    <>
      <Typography size={12} height={16} color="secondaryDisabledText">
        {translateReplacer(t('selected_items'), [
          String(selectedCounts),
          '300',
        ])}
      </Typography>
      {!!values.action && (
        <Flex className="!flex-row gap-8">
          <Button
            fullWidth
            label={t(values.action.value === 'todo_2' ? 'back' : 'discard')}
            schema="gray-semi-transparent"
            onClick={handleDiscard}
          />
          {values.action.value === 'todo_1' ? (
            <Button
              fullWidth
              label={t('next')}
              schema="primary-blue"
              onClick={() =>
                setFieldValue('action', { value: 'todo_2', label: 'todo_2' })
              }
              disabled={!isValid}
            />
          ) : (
            <SubmitButton
              label={t(
                buttontitleData[
                  values.action.value as keyof typeof buttontitleData
                ]
              )}
              fullWidth
            />
          )}
        </Flex>
      )}
    </>
  );
};

export default BulkStageMenuFooter;

const buttontitleData: { [key in BulkActionType]: string } = {
  move_to: 'move_all',
  reject: 'reject_all',
  interview: 'send_interview',
  email: 'send_email',
  message: 'send_message',
  note: 'save',
  todo_1: 'next',
  todo_2: 'save',
};
