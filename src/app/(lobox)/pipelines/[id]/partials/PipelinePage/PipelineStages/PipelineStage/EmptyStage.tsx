import type { PipelineModel } from '@shared/types/jobsProps';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';

interface EmptyStageProps {
  stage: PipelineModel;
}

const EmptyStage: React.FC<EmptyStageProps> = ({ stage }) => {
  const { t } = useTranslation();
  return (
    <Flex className="items-center gap-32 mt-12">
      <Typography
        textAlign="center"
        color="secondaryDisabledText"
        className="max-w-[80%]"
      >
        {t('stage_emoty_description')}
      </Typography>
      <Button
        label={t('automate_stage')}
        leftIcon="automate"
        leftType="far"
        schema="semi-transparent"
        onClick={() => alert('Todo: needs to be implemented later!')}
      />
    </Flex>
  );
};

export default EmptyStage;
