import Info from '@shared/components/molecules/Info/Info';
import Button from '@shared/uikit/Button';
import Confirmation from '@shared/uikit/Confirmation/Confirmation';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';

interface DeleteStageModalProps {
  onClose: VoidFunction;
  onDelete: VoidFunction;
  isDisabled?: boolean;
  isDeleteing?: boolean;
  style: React.CSSProperties;
}

const DeleteStageModal: React.FC<DeleteStageModalProps> = (props) => {
  const { onClose, onDelete, isDisabled, isDeleteing, style } = props;
  const { t } = useTranslation();
  return (
    <Confirmation
      title={t('delete_stage')}
      onBackDropClick={onClose}
      variant="wideRightSideModal"
      styles={{
        wrapper: '!bottom-[20px] !w-[375px]',
      }}
      pureStyles={{
        wrapper: style,
      }}
    >
      {isDisabled && (
        <Info
          text={t('delete_stage_alert')}
          icon="exclamation-triangle"
          color="pendingOrange"
          className="!bg-pendingOrange_10 !flex-col items-center gap-8"
          textColor="smoke_coal"
          classNames={{ text: 'text-center' }}
        />
      )}
      <Typography>{t('delete_stage_desc')}</Typography>
      <Flex className="!flex-row gap-8">
        <Button
          fullWidth
          label={t('cancel')}
          schema="gray-semi-transparent"
          onClick={onClose}
        />
        <Button
          fullWidth
          label={t('delete')}
          schema="primary-blue"
          onClick={onDelete}
          disabled={isDeleteing || isDisabled}
        />
      </Flex>
    </Confirmation>
  );
};

export default DeleteStageModal;
