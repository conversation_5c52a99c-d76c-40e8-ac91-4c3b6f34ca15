import Icon from '@shared/uikit/Icon';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { colorsKeys } from 'shared/uikit/helpers/theme';

interface StageChangeColorOptionProps {
  onChangeColor: (color: colorsKeys) => void;
  color: colorsKeys;
}

const StageChangeColorOption: React.FC<StageChangeColorOptionProps> = (
  props
) => {
  const { onChangeColor, color: defaultColor } = props;
  const { t } = useTranslation();
  return (
    <PopperMenu
      placement="right-start"
      closeOnScroll
      showWithHover
      menuClassName="ml-10"
      buttonComponent={
        <div>
          <PopperItem
            onClick={(e: any) => {
              e.stopPropagation();
            }}
            iconName="template"
            iconType="far"
            label={t('change_color')}
            iconSize={20}
            action={
              <Icon
                name="chevron-right"
                type="far"
                size={15}
                className="ml-auto"
              />
            }
            className="min-w-[280px]"
          />
        </div>
      }
    >
      {colorData.map((color) => (
        <PopperItem
          onClick={() => onChangeColor(color.value)}
          label={t(color.label)}
          className="min-w-[156px]"
          isSelected={defaultColor === color.value}
          key={`sort_${color.value}`}
          iconName="circle-s"
          iconType="far"
          iconColor={color.value}
          iconSize={18}
          iconClassName={{
            icon: 'border border-solid border-white rounded-full',
          }}
        />
      ))}
    </PopperMenu>
  );
};

export default StageChangeColorOption;

const colorData: { value: colorsKeys; label: string }[] = [
  {
    value: 'brand',
    label: 'blue',
  },
  {
    value: 'pendingOrange',
    label: 'orange',
  },
  {
    value: 'error',
    label: 'red',
  },
  {
    value: 'success',
    label: 'green',
  },
  {
    value: 'heliotrope',
    label: 'purple',
  },
];
