import Icon from '@shared/uikit/Icon';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { PipelineStageSortBy } from '@shared/types/jobsProps';

interface IStageSortOptionProps {
  onSort: (sort: PipelineStageSortBy) => void;
  sort: PipelineStageSortBy;
}

const StageSortOption: React.FC<IStageSortOptionProps> = (props) => {
  const { onSort, sort } = props;
  const { t } = useTranslation();
  return (
    <PopperMenu
      placement="right-start"
      closeOnScroll
      showWithHover
      menuClassName="ml-10"
      buttonComponent={
        <div>
          <PopperItem
            onClick={(e: any) => {
              e.stopPropagation();
            }}
            iconName="filter"
            iconType="far"
            label={t('sort')}
            iconSize={20}
            action={
              <Icon
                name="chevron-right"
                type="far"
                size={15}
                className="ml-auto"
              />
            }
            className="min-w-[280px]"
          />
        </div>
      }
    >
      {sortData.map((item) => (
        <PopperItem
          onClick={() => onSort(item.value)}
          label={t(item.label)}
          className="min-w-[156px]"
          isSelected={sort === item.value}
          key={`sort_${item.value}`}
        />
      ))}
    </PopperMenu>
  );
};

export default StageSortOption;

const sortData: { value: PipelineStageSortBy; label: string }[] = [
  {
    value: 'DATE',
    label: 'by_date',
  },
  {
    value: 'AVERAGE_SCORE',
    label: 'by_rating',
  },
];
