import type { FC } from 'react';
import React, { memo } from 'react';
import getStepData from 'shared/utils/getStepData';
import type { CreateEntityModalProps } from 'shared/types/generalProps';
import { QueryKeys } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useToast from 'shared/uikit/Toast/useToast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import type { JobAPIProps } from '@shared/types/jobsProps';
import MultiStepForm from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import type { MultiStepFormProps } from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import type { ProjectProps } from '@shared/types/project';
import { batchUpdatePriority, batchUpdateStatus } from '@shared/utils/api/jobs';
import { batchUpdateProjects } from '@shared/utils/api/project';
import { sleep } from '@shared/utils/toolkit/sleep';
import { useBatchActionsPipelines } from './hooks/useBatchActionsPipelines';
import useResponseToast from '@shared/hooks/useResponseToast';

interface BatchActionsPiplinesModalProps extends CreateEntityModalProps<any> {
  selectedPipelines: JobAPIProps[];
  onClose: VoidFunction;
  onFinish: VoidFunction;
}

const BatchActionsPiplinesModal: FC<BatchActionsPiplinesModalProps> = ({
  selectedPipelines,
  onClose,
  onFinish,
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const queryClient = useQueryClient();
  const { handleSuccess, handleError } = useResponseToast();

  const data = useBatchActionsPipelines({ onClose, onFinish });
  const totalSteps = data.length;
  const getHeaderProps = getStepData('getHeaderProps', data);
  const getStepHeaderProps = getStepData('getStepHeaderProps', data);
  const renderBody = getStepData('renderBody', data);
  const renderFooter = getStepData('renderFooter', data);

  const selectedProjects = React.useMemo(
    () =>
      selectedPipelines.reduce((acc, pipeline) => {
        pipeline.projects.forEach((project) => {
          if (!acc.some((p) => p.id === project.id)) {
            acc.push(project);
          }
        });
        return acc;
      }, [] as ProjectProps[]),
    [selectedPipelines]
  );

  const { mutate: mutateBatchUpdateStatus } = useMutation({
    mutationFn: batchUpdateStatus,
  });

  const { mutate: mutateBatchUpdatePriority } = useMutation({
    mutationFn: batchUpdatePriority,
  });

  const { mutate: mutateBatchUpdateProjects } = useMutation({
    mutationFn: batchUpdateProjects,
  });

  const apiFunc: MultiStepFormProps['apiFunc'] = async (values: any) => {
    if (values.action.value === 'status') {
      mutateBatchUpdateStatus({
        ids: values.jobs.map((job: JobAPIProps) => job.id),
        status: values.value.value,
      });
    } else if (values.action.value === 'priority') {
      mutateBatchUpdatePriority({
        ids: values.jobs.map((job: JobAPIProps) => job.id),
        priority: values.value.value,
      });
    } else if (values.action.value === 'projects') {
      mutateBatchUpdateProjects({
        jobIds: values.jobs.map((job: JobAPIProps) => job.id),
        projectIds: values.projects.map((project: ProjectProps) => project.id),
      });
    }
  };

  const onAlert = async (
    message: string,
    type: 'success' | 'error',
    title = ''
  ) => {
    if (type === 'success') {
      handleSuccess({ message, title });
    } else {
      handleError({ message, title: t('error') });
    }
    onClose();
    if (type === 'success') {
      onFinish();
      await sleep(2000);
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getPipelinesList],
        exact: false,
      });
    }
  };

  const onFailure = (error: any) => {
    let message = '';
    if (error?.response?.data?.fieldErrors?.length > 0) {
      message = `${error.response.data.fieldErrors[0].code}: ${error.response.data.fieldErrors[0].defaultMessage}`;
    }
    onAlert(message || t('something_went_wrong'), 'error');
  };

  return (
    <MultiStepForm
      apiFunc={apiFunc}
      totalSteps={totalSteps}
      isOpenAnimation
      onClose={onClose}
      getHeaderProps={getHeaderProps}
      getStepHeaderProps={getStepHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      onSuccess={(
        setStep: (step: number) => void,
        update: any,
        values: { action: { value: keyof typeof alertMessageText } }
      ) => {
        onAlert(
          t(alertMessageText[values.action.value]),
          'success',
          alertTitleText[values.action.value]
        );
      }}
      onFailure={onFailure}
      initialValues={{
        action: {
          value: 'status',
          label: 'change_status',
        },
        jobs: selectedPipelines,
        projects: selectedProjects,
      }}
      enableReinitialize={false}
    />
  );
};

export default memo(BatchActionsPiplinesModal);

const alertTitleText = {
  status: 'status_updated',
  priority: 'priority_updated',
  projects: 'project_links_updated',
};

const alertMessageText = {
  status: 'batch_status_success',
  priority: 'batch_priority_success',
  projects: 'batch_projects_success',
};
