import Flex from '@shared/uikit/Flex';
import IconButton from '@shared/uikit/Button/IconButton';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import Typography from '@shared/uikit/Typography';
import Button from '@shared/uikit/Button';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { JobAPIProps } from '@shared/types/jobsProps';
import { useState } from 'react';
import BatchActionsPiplinesModal from './BatchActionsPiplinesModal';

interface PipelinesListHeaderProps {
  isBulk: boolean;
  setIsBulk: (value: boolean) => void;
  selectedPipelines: JobAPIProps[];
  searchInput: string;
  onSearchChange: (text: string) => void;
  clearSelectedPipelines: () => void;
}

export default function PipelinesListHeader({
  isBulk,
  setIsBulk,
  selectedPipelines,
  searchInput,
  onSearchChange,
  clearSelectedPipelines,
}: PipelinesListHeaderProps) {
  const { t } = useTranslation();
  const [openBatchActionsModal, setOpenBatchActionsModal] = useState(false);
  const switchBulk = () => setIsBulk(!isBulk);

  const onFinish = () => {
    switchBulk();
    clearSelectedPipelines();
  };

  const onCloseBuld = () => {
    switchBulk();
    clearSelectedPipelines();
  };

  return (
    <Flex className={`!flex-row items-center ${isBulk && 'flex-1'}`}>
      {isBulk && (
        <Flex className="flex-1">
          <Flex className="!flex-row items-center gap-20 bg-hoverPrimary rounded-[4px] px-12 py-4 w-fit">
            <Typography>
              {translateReplacer(t('x_of_y_selected'), [
                String(selectedPipelines.length),
                String('300'),
              ])}
            </Typography>
            <Button
              label={t('next')}
              disabled={selectedPipelines.length === 0}
              onClick={() => setOpenBatchActionsModal(true)}
            />
          </Flex>
        </Flex>
      )}
      <IconButton
        color="brand"
        name="bulk"
        onClick={onCloseBuld}
        variant="rectangle"
        colorSchema={isBulk ? 'semi-transparent' : 'smoke_coal'}
        size="md18"
        type="far"
      />
      <DividerVertical className="!mx-12" />
      <SearchInputV2
        onChange={onSearchChange}
        placeholder={t('search_jobs')}
        className="min-w-[300px]"
        value={searchInput}
      />
      {openBatchActionsModal && (
        <BatchActionsPiplinesModal
          selectedPipelines={selectedPipelines}
          onClose={() => setOpenBatchActionsModal(false)}
          onFinish={onFinish}
        />
      )}
    </Flex>
  );
}
