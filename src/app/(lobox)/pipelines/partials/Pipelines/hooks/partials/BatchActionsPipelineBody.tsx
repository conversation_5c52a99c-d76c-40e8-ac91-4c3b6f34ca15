import React from 'react';
import BatchPipelineStepOne from './BatchActionsPipelineBody/BatchPipelineStepOne';
import BatchPipelineStepTwo from './BatchActionsPipelineBody/BatchPipelineStepTwo';
import BatchPipelineOverview from './BatchActionsPipelineBody/BatchPipelineOverview';

interface BatchActionsPipelineBodyProps {
  step: number;
}

export const BatchActionsPipelineBody: React.FC<
  BatchActionsPipelineBodyProps
> = ({ step }) => {
  const renderStepContent = () => {
    switch (step) {
      case 0:
        return <BatchPipelineStepOne />;
      case 1:
        return <BatchPipelineStepTwo />;
      case 2:
        return <BatchPipelineOverview />;
      default:
        return null;
    }
  };

  return <>{renderStepContent()}</>;
};
