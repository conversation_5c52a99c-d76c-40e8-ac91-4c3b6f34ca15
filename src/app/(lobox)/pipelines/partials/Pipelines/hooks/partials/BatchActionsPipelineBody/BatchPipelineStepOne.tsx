import React, { useMemo } from 'react';
import { useFormikContext } from 'formik';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { JobAPIProps } from 'shared/types/jobsProps';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import Flex from '@shared/uikit/Flex';
import LinkJobModalJobItem from '@shared/components/Organism/MultiStepForm/LinkJobForm/LinkJobModal/LinkJobModalBody/LinkJobModalJobsList/LinkJobModalJobItem';
import IconButton from '@shared/uikit/Button/IconButton';

interface FormValues {
  jobs: JobAPIProps[];
  action: string;
}

const JobsList = () => {
  const { values, setFieldValue } = useFormikContext<FormValues>();
  const isDisabled = useMemo(() => values.jobs.length === 1, [values.jobs]);

  const onClickCheckBox = (job: JobAPIProps) => {
    if (isDisabled) return;
    setFieldValue(
      'jobs',
      values.jobs.filter(
        (_job: JobAPIProps) => String(_job.id) !== String(job.id)
      )
    );
  };

  return (
    <Flex>
      {values.jobs.map((job: JobAPIProps) => (
        <LinkJobModalJobItem
          key={job.id}
          job={job}
          onClick={onClickCheckBox}
          selectedJobs={values.jobs as any[]}
          action={
            <Flex className="ml-auto">
              <IconButton
                name="times"
                size="tiny"
                disabled={isDisabled}
                colorSchema="transparent"
              />
            </Flex>
          }
        />
      ))}
    </Flex>
  );
};

const BatchPipelineStepOne: React.FC = () => {
  const { t } = useTranslation();

  return (
    <DynamicFormBuilder
      className="gap-20"
      groups={[
        {
          formGroup: {
            title: t('choose_an_action'),
            formSection: true,
          },
          name: 'action',
          cp: 'dropdownSelect',
          label: t('action'),
          options: [
            { label: t('link_candidates'), value: 'link' },
            { label: t('change_status'), value: 'status' },
            { label: t('change_priority'), value: 'priority' },
            { label: t('change_project_links'), value: 'projects' },
          ],
          required: true,
          className: 'z-[9002]',
        },
        {
          formGroup: {
            title: t('selected_jobs'),
            formSection: true,
          },
          name: 'jobs',
          cp: JobsList,
        },
      ]}
    />
  );
};

export default BatchPipelineStepOne;
