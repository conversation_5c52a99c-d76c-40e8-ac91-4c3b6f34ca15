import React from 'react';
import { useFormikContext } from 'formik';
import type { JobAPIProps } from '@shared/types/jobsProps';
import BatchPipelineOverview from './BatchPipelineOverview';
import BatchPipelineSelectProjects from './BatchPipelineStepTwo/BatchPipelineSelectProjects';

interface FormValues {
  jobs: JobAPIProps[];
  action: {
    value: string;
    label: string;
  };
}

const BatchPipelineStepTwo: React.FC = () => {
  const { values } = useFormikContext<FormValues>();

  const renderContent = () => {
    switch (values.action.value) {
      case 'status':
        return <BatchPipelineOverview />;
      case 'projects':
        return <BatchPipelineSelectProjects />;
      default:
        return <BatchPipelineOverview />;
    }
  };

  return renderContent();
};

export default BatchPipelineStepTwo;
