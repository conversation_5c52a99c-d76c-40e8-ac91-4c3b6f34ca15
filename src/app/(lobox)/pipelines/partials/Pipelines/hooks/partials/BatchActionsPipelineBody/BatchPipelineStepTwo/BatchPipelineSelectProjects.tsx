import React from 'react';
import type { ProjectProps } from 'shared/types/project';
import { QueryKeys } from 'shared/utils/constants';
import { getProjectsList } from 'shared/utils/api/project';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import CreateJobStepOne from '@shared/components/Organism/MultiStepForm/CreateJobForm/CreateJobModal/CreateJobModalBody/CreateJobStepOne';
import ProjectsSkeleton from '@shared/components/Organism/MultiStepForm/CreateJobForm/CreateJobModal/ProjectsSkeleton';
import EmptyCreateJob from '@shared/components/Organism/MultiStepForm/CreateJobForm/CreateJobModal/EmptyCreateJob';

const BatchPipelineSelectProjects: React.FC = () => {
  const { businessPage } = useGetAppObject();

  const {
    data: projectsData,
    fetchNextPage,
    isLoading,
    hasNextPage,
  } = useInfiniteQuery<ProjectProps>(
    [QueryKeys.getProjectsList, businessPage?.username],
    {
      func: getProjectsList,
      size: 10,
    }
  );

  if (isLoading) return <ProjectsSkeleton />;
  if (!projectsData?.length) return <EmptyCreateJob />;

  return (
    <CreateJobStepOne
      list={projectsData}
      loadMore={() => fetchNextPage()}
      hasNextPage={hasNextPage}
    />
  );
};

export default BatchPipelineSelectProjects;
