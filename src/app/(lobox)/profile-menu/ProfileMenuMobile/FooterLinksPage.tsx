import React, { Fragment } from 'react';
import type { FC } from 'react';
import Link from 'shared/uikit/Link';
import Divider from 'shared/uikit/Divider';
import Flex from 'shared/uikit/Flex/index';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import { footerDataMaker } from 'shared/uikit/Footer/FooterDesktop';
import classes from './FooterLinksPage.module.scss';

interface FooterLinksPageProps {
  onGoBack: VoidFunction;
}

const FooterLinksPage: FC<FooterLinksPageProps> = (props) => {
  const { onGoBack } = props;
  const { t } = useTranslation();
  const links = footerDataMaker(t);
  const linksKeysArray = Object.keys(links);

  const onItemClick = (link: string) => {
    onGoBack();
  };

  return (
    <FixedRightSideModalDialog>
      <ModalHeaderSimple
        backButtonProps={{
          onClick: onGoBack,
        }}
        title={t('useful_links')}
      />

      <Flex className={classes.content}>
        {links.map(({ key, title, list, link }, index) => (
          <Fragment key={key}>
            <Link to={link} className={classes.hover}>
              <Typography
                font="500"
                size={19}
                height={24}
                color="secondaryText"
              >
                {title}
              </Typography>
            </Link>
            <Flex className={classes.spacing} />
            {list.map((child) => (
              <Link
                to={child.link}
                onClick={() => onItemClick(child.link)}
                key={child.title + child.link}
              >
                <Typography
                  className={classes.item}
                  font="400"
                  size={14}
                  color="fifthText"
                >
                  {child.title}
                </Typography>
              </Link>
            ))}

            {index !== linksKeysArray.length - 1 && (
              <Divider className={classes.divider} />
            )}
          </Fragment>
        ))}
      </Flex>
    </FixedRightSideModalDialog>
  );
};

export default FooterLinksPage;
