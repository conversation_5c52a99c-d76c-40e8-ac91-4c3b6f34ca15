import BottomSheet from 'shared/uikit/BottomSheet';
import Flex from 'shared/uikit/Flex/index';
import MyPages from 'shared/components/Organism/MyPages';
import React, { useRef } from 'react';
import type { FC } from 'react';
import classes from './MyPagesModal.module.scss';

interface MyPagesModalProps {
  isOpen: boolean;
  onClose: VoidFunction;
}

const MyPagesModal: FC<MyPagesModalProps> = (props) => {
  const { isOpen, onClose } = props;

  const bottomSheetRef = useRef(null);

  return (
    <BottomSheet open={isOpen} ref={bottomSheetRef} onRequestClose={onClose}>
      <Flex className={classes.wrapper}>
        <MyPages handleClose={onClose} />
      </Flex>
    </BottomSheet>
  );
};

export default MyPagesModal;
