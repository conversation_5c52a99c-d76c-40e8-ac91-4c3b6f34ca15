import React, { useEffect, useState } from 'react';
import Avatar from 'shared/uikit/Avatar';
import AvatarCard from 'shared/uikit/AvatarCard';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Divider from 'shared/uikit/Divider';
import Flex from 'shared/uikit/Flex/index';
import IconButton from 'shared/uikit/Button/IconButton';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import {
  landingRouteNames,
  routeNames,
} from 'shared/utils/constants/routeNames';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import useLogout from 'shared/utils/hooks/useLogoutMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useHistory from 'shared/utils/hooks/useHistory';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useGetMyPages from 'shared/hooks/api-hook/useGetMyPages';
import useOpenBusinessApp from '@shared/utils/hooks/useOpenBusinessApp';
import ActionRow from 'shared/components/molecules/ProfileMenuActionRow/ActionRow';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import cnj from 'shared/uikit/utils/cnj';
import Link from 'shared/uikit/Link';
import { openNineDotPanel } from '@shared/stores/nineDotPanelStore';
import useGetPortalMenuItems from 'shared/hooks/useGetPortalMenuItems';
import { useRouter } from 'next/navigation';
import useMedia from 'shared/uikit/utils/useMedia';
import isDev from '@shared/utils/constants/isDev';
import FooterLinksPage from './FooterLinksPage';
import MyPagesModal from './MyPagesModal';
import classes from './index.module.scss';

const ProfilePanelMobile: React.FC = () => {
  const logoutHandler = useLogout();
  const { t } = useTranslation();
  const [showFooterLinks, setShowFooterLinks] = useState(false);
  const [isPagesModalOpen, setIsPagesModalOpen] = useState(false);
  const globalDispatch = useGlobalDispatch();
  const history = useHistory();
  const router = useRouter();
  const openBusinessApp = useOpenBusinessApp();
  const { isTabletAndLess } = useMedia();
  const { isBusinessApp, getAppObjectPropValue, authUser, reFetchAppObject } =
    useGetAppObject();

  useEffect(() => {
    reFetchAppObject();
  }, []);

  const items = useGetPortalMenuItems();
  const { data: myPagesData } = useGetMyPages();

  const username = getAppObjectPropValue({
    userKey: 'username',
    pageKey: 'username',
  });
  const usernameRoute = `/${username}`;

  const pages = [
    {
      page: {
        username: authUser?.username,
        title: authUser?.fullName,
        croppedImageUrl: authUser?.croppedImageUrl,
        id: authUser?.id,
      },
    },
    ...(myPagesData || []),
  ]?.filter((item) => item?.page?.username !== username);

  const recentPage = pages?.[0];

  const title = getAppObjectPropValue({
    userKey: 'fullName',
    pageKey: 'title',
  });

  const croppedImageUrl = getAppObjectPropValue({
    userKey: 'croppedImageUrl',
    pageKey: 'croppedImageUrl',
  });

  const togglePagesModal = () => {
    setIsPagesModalOpen((prev) => !prev);
  };

  const toggleFooterLinksPage = () => {
    setShowFooterLinks((prev) => !prev);
  };

  const onSwitchToRecentPageClick = () => {
    const userName = recentPage.page.username;
    const { id } = recentPage.page;

    if (id === authUser?.id) {
      history.push(usernameRoute);
    } else {
      openBusinessApp(userName);
    }
  };
  useEffect(() => {
    if (!isTabletAndLess) {
      router.replace(routeNames.home);
    } else if (isBusinessApp && !isDev) {
      router.replace(routeNames.switchToDesktop);
    }
  }, [isTabletAndLess]);

  const rowActions = [
    {
      id: 'more',
      label: t('more'),
      icon: 'nine-dot',
      onClick: () => openNineDotPanel({ isOpen: true }),
    },
    {
      id: 'settings',
      label: t('settings'),
      icon: 'gear-light',
      link: routeNames.settings,
      scopes: [SCOPES.canSeeSettingsScreen],
    },
    {
      id: 'help',
      label: t('help'),
      icon: 'square-question-light',
      link: landingRouteNames.helpCenter,
    },
    {
      id: 'feedback',
      label: t('support'),
      icon: 'message-exclamation-light',
      onClick: () => {
        globalDispatch({ type: 'TOGGLE_FEEDBACK_MODAL' });
      },
    },
    {
      id: 'invite',
      label: t('invite'),
      icon: 'invite',
      onClick: () => {
        openMultiStepForm({ formName: 'invitePeople' });
      },
    },
    {
      id: 'links',
      label: t('useful_links'),
      icon: 'link',
      onClick: toggleFooterLinksPage,
    },
  ];

  if (showFooterLinks) {
    return <FooterLinksPage onGoBack={toggleFooterLinksPage} />;
  }

  return (
    <>
      <Flex>
        <Flex className={classes.content}>
          <Flex className={classes.avatarRow}>
            <Link to={`/${username}`}>
              <AvatarCard
                containerProps={{ className: classes.avatarContainerClassName }}
                data={{
                  title,
                  subTitle: `@${username}`,
                  image: croppedImageUrl,
                }}
                titleProps={{
                  size: 16,
                  font: '700',
                  height: 18,
                  isTruncated: true,
                  isWordWrap: true,
                  lineNumber: 1,
                }}
                subTitleProps={{
                  color: 'smoke_coal',
                  height: 18,
                  size: 14,
                  mt: 4,
                  isTruncated: true,
                  isWordWrap: true,
                  lineNumber: 1,
                }}
                avatarProps={{
                  size: 'xmd',
                  isCompany: isBusinessApp,
                }}
              />
            </Link>

            <Flex className={classes.row}>
              {recentPage && (
                <BaseButton
                  onClick={onSwitchToRecentPageClick}
                  className={classes.recentPageContainer}
                >
                  <Avatar
                    size="xxs"
                    className={classes.avatar}
                    isCompany={
                      recentPage?.page?.username !== authUser?.username
                    }
                    imgSrc={recentPage?.page?.croppedImageUrl}
                  />
                </BaseButton>
              )}

              {myPagesData?.length > 1 && (
                <IconButton
                  type="far"
                  className={classes.downIcon}
                  name="chevron-down"
                  onClick={togglePagesModal}
                />
              )}
            </Flex>
          </Flex>
          <Divider className={classes.divider} />
          <Flex className={classes.actionsContainer}>
            {items.map((action) => (
              <ActionRow
                key={action.id}
                className={classes.actionItem}
                to={action.link}
                icon={`${action.icon}-light`}
                label={action.label}
              />
            ))}
          </Flex>
          <Divider className={classes.divider} />
          <Flex className={cnj(classes.actionsContainer, classes.rowActions)}>
            {rowActions.map((action) => (
              <ActionRow
                key={action.id}
                className={cnj(classes.actionItem, classes.fullWidth)}
                to={action.link}
                icon={action.icon}
                label={action.label}
                onClick={() => {
                  action?.onClick?.();
                }}
              />
            ))}
          </Flex>
          <Divider className={classes.divider} />
          <ActionRow
            className={cnj(classes.actionItem, classes.fullWidth)}
            icon="sign-out-alt"
            label={t('logout_cap')}
            onClick={logoutHandler}
          />
        </Flex>
      </Flex>

      <MyPagesModal isOpen={isPagesModalOpen} onClose={togglePagesModal} />
    </>
  );
};

export default ProfilePanelMobile;
