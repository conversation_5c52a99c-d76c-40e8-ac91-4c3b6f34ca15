import Flex from 'shared/uikit/Flex';
import Skeleton from 'shared/uikit/Skeleton';
import Divider from 'shared/uikit/Divider';
import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import classes from './loading.module.scss';

const skeletons = Array.from({ length: 6 }, (_, i) => i);

const Loading = () => (
  <Flex className={classes.content}>
    <Skeleton className={classes.avatar} />
    <Divider className={classes.divider} />
    <Flex className={classes.actionsContainer}>
      {skeletons.map((item) => (
        <Skeleton key={item} className={classes.actionItem} />
      ))}
    </Flex>
    <Divider className={classes.divider} />
    <Flex className={cnj(classes.actionsContainer, classes.rowActions)}>
      {skeletons.map((item) => (
        <Skeleton
          key={item}
          className={cnj(classes.actionItem, classes.fullWidth)}
        />
      ))}
    </Flex>
    <Divider className={classes.divider} />
    <Skeleton className={classes.logOut} />
  </Flex>
);

export default Loading;
