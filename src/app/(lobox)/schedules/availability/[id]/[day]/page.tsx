'use client';

import AvailableHoursPanelContent from '@shared/components/Organism/AvailabilityModule/partials/AvailableHoursPanelContent';
import { useAvailability } from '@shared/components/Organism/AvailabilityModule/partials/useAvailability';
import { dayjs } from '@shared/utils/Time';
import { useParams } from 'next/navigation';
import Loading from './loading';

const AvailabilityDayPage = () => {
  const { id, day } = useParams();
  const { displayingTimesheet, displayingTimezone, isLoadingPreferences } =
    useAvailability({
      timesheetId: id as string,
    });

  return !isLoadingPreferences &&
    displayingTimezone?.code &&
    displayingTimesheet ? (
    <AvailableHoursPanelContent
      timesheet={displayingTimesheet}
      day={dayjs.tz(day as string, displayingTimezone.code)}
      bookedMeetings={[]}
      isHeadless
    />
  ) : (
    <Loading />
  );
};

export default AvailabilityDayPage;
