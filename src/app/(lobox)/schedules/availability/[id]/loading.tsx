import AvatarCardSkeleton from '@shared/uikit/AvatarCard/AvatarCard.Skeleton';
import { Fragment } from 'react';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';

export default function loading({ isInsideBody }: { isInsideBody?: boolean }) {
  const WrapperComponent = isInsideBody ? Fragment : 'div';
  return (
    <WrapperComponent className="p-20">
      <AvatarCardSkeleton className="h-[68px]" />
      <div className="flex flex-col mt-20 gap-y-12">
        <Skeleton className="h-[22px]" />
        <Skeleton className="h-[56px]" />
        <Skeleton className="h-[300.4px]" />
      </div>
    </WrapperComponent>
  );
}
