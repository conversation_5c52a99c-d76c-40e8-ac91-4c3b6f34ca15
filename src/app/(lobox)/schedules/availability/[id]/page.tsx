'use client';

import AvailabilityDetailsPanelContent from '@shared/components/Organism/AvailabilityModule/partials/AvailabilityDetailsPanelContent';
import { useAvailability } from '@shared/components/Organism/AvailabilityModule/partials/useAvailability';
import { useAuthState } from '@shared/contexts/Auth/auth.provider';
import { routeNames } from '@shared/utils/constants/routeNames';
import type { Timesheet } from '@shared/types/preferences/preferences';
import { useParams, useRouter } from 'next/navigation';
import { useCallback } from 'react';
import type { Dayjs } from 'dayjs';
import Loading from './loading';

const AvailabilityDetailsMobilePage = () => {
  const { id } = useParams();
  const userId = useAuthState('userId') as string;

  const {
    isLoadingPreferences,
    deleteTimesheet,
    availabilityUser,
    isLoadingAvailabilityUser,
    displayingTimesheet,
    displayingTimezone,
    setDisplayingTimezone,
  } = useAvailability({
    timesheetId: id as string,
    userId,
  });
  const router = useRouter();
  const openAvailabilityCrEdit = useCallback(
    (ts?: Timesheet) => {
      router.replace(
        ts?.id
          ? routeNames.schedulesAvailabilityCrEdit.makeRoute(ts?.id)
          : routeNames.schedulesAvailabilityCrEdit.main
      );
    },
    [router]
  );

  const onOpenHoursPanel = (day: Dayjs) => {
    router.replace(
      routeNames.schedulesAvailabilityDay.makeRoute(
        id as string,
        day.format('YYYY-MM-DD')
      )
    );
  };
  return !isLoadingPreferences && displayingTimezone ? (
    <AvailabilityDetailsPanelContent
      availabilityUser={availabilityUser}
      isLoadingCreatorUser={isLoadingAvailabilityUser}
      isLoading={isLoadingPreferences}
      onDeleteTimesheet={deleteTimesheet}
      onOpenAvailabilityCrEdit={openAvailabilityCrEdit}
      onOpenHoursPanel={onOpenHoursPanel}
      timesheet={displayingTimesheet}
      timezone={displayingTimezone}
      onSetTimezone={setDisplayingTimezone}
      isHeadless
    />
  ) : (
    <Loading />
  );
};

export default AvailabilityDetailsMobilePage;
