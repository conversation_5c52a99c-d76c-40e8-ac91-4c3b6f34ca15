'use client';

import CrEditAvailabilityPanelContent from '@shared/components/Organism/AvailabilityModule/partials/CrEditAvailabilityPanelContent/CrEditAvailabilityPanelContent';
import { useAvailability } from '@shared/components/Organism/AvailabilityModule/partials/useAvailability';
import { useParams } from 'next/navigation';
import useHistory from '@shared/utils/hooks/useHistory';
import Loading from '../loading';

const AvailabilityEditPage = () => {
  const { id } = useParams();
  const history = useHistory();
  const { timesheet, refetchPreferences, isLoadingPreferences } =
    useAvailability({
      timesheetId: id as string,
    });

  const onCrEditSuccess = () => {
    refetchPreferences();
    history.back();
  };
  return !isLoadingPreferences ? (
    <CrEditAvailabilityPanelContent
      onSuccess={onCrEditSuccess}
      timesheet={timesheet}
      isHeadless
      onClose={history.back}
    />
  ) : (
    <Loading />
  );
};

export default AvailabilityEditPage;
