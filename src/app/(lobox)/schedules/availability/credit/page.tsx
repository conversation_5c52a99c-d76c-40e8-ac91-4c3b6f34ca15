'use client';

import CrEditAvailabilityPanelContent from '@shared/components/Organism/AvailabilityModule/partials/CrEditAvailabilityPanelContent/CrEditAvailabilityPanelContent';
import { useAvailability } from '@shared/components/Organism/AvailabilityModule/partials/useAvailability';
import { useRouter } from 'next/navigation';

const AvailabilityEditPage = () => {
  const router = useRouter();
  const { refetchPreferences } = useAvailability();

  const onCrEditSuccess = () => {
    refetchPreferences();
    router.back();
  };
  return (
    <CrEditAvailabilityPanelContent
      onSuccess={onCrEditSuccess}
      isHeadless
      onClose={router.back}
    />
  );
};

export default AvailabilityEditPage;
