'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import dayjs, { type Dayjs } from 'dayjs';

import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import useIsFirstRender from '@shared/utils/hooks/useIsFirstRender';
import useHistory from 'shared/utils/hooks/useHistory';
import { getUser } from 'shared/utils/api/user';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { routeNames } from 'shared/utils/constants/routeNames';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { weekDaysValueArray } from 'shared/utils/constants/enums/schedulesDb';
import { BE_TIME_FORMAT, Time } from 'shared/utils/Time';
import useMedia from 'shared/uikit/utils/useMedia';
import Typography from 'shared/uikit/Typography';
import IconButton from 'shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import cnj from '@shared/uikit/utils/cnj';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import AvailableHoursList from 'shared/uikit/AvailableHoursList/AvailableHoursList';
import AvailabilityDetails from 'shared/uikit/AvailabilityDetails/AvailabilityDetails';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog/FixedRightSideModalDialog.component';
import EmptySectionInModules from 'shared/uikit/EmptySectionInModules/EmptySectionInModules';
import AuthenticationPanel from 'shared/components/molecules/AuthenticationPanel/AuthenticationPanel';
import { useAvailability } from 'shared/components/Organism/AvailabilityModule/partials/useAvailability';
import AvailableHoursPanelContent from 'shared/components/Organism/AvailabilityModule/partials/AvailableHoursPanelContent';
import { useGetBookedMeetings } from '@shared/components/Organism/AvailabilityModule/partials/useGetBookedMeetings';
import AvailabilityPanelContent from '@shared/components/Organism/AvailabilityModule/partials/AvailabilityPanelContent/AvailabilityPanelContent';
import { type DateType } from '@shared/types/schedules/schedules';
import type { Timesheet } from '@shared/types/preferences/preferences';

import classes from './page.module.scss';

const Availability = () => {
  const router = useRouter();
  const { isMoreThanTablet } = useMedia();
  const isLoggedIn: boolean = useAuthState('isLoggedIn');
  const { isFirstRender } = useIsFirstRender();
  const { t } = useTranslation();

  const { state: urlState, setScheduleAvailabilityPanelData } =
    useSchedulesUrlState();
  const { scheduleAvailabilityPanelData } = urlState;
  const {
    selectedDay,
    selectedHour: rawSelectedHour,
    timeSheetId: availabilityId,
    userId,
  } = scheduleAvailabilityPanelData || {};
  const displayingDate = useMemo(() => dayjs(selectedDay), [selectedDay]);
  const history = useHistory();
  const searchParams = useSearchParams();
  const path = usePathname();

  // Note: The following logic is written the way it is to avoid multiple redirects. Please change with caution.
  useEffect(() => {
    let redirectPath = path;
    let newSearchParams: URLSearchParams | undefined = undefined;
    const paramUserId = searchParams.get('userId');
    const paramAvailabilityId = searchParams.get('availabilityId');
    if (paramUserId && paramAvailabilityId) {
      if (userId !== paramUserId || availabilityId !== paramAvailabilityId) {
        newSearchParams = new URLSearchParams();
        newSearchParams.set(
          'state',
          JSON.stringify({
            scheduleAvailabilityPanelData: {
              ...scheduleAvailabilityPanelData,
              userId: paramUserId,
              timeSheetId: paramAvailabilityId,
            },
          })
        );
      }
    }
    if (isMoreThanTablet && isLoggedIn) {
      redirectPath = routeNames.schedulesCalendarMonth;
      if (!newSearchParams) {
        newSearchParams = new URLSearchParams();
        newSearchParams.set('state', JSON.stringify(urlState));
      }
    }
    if (newSearchParams)
      router.replace(`${redirectPath}?${newSearchParams.toString()}`);
    else if (redirectPath !== path)
      router.replace(`${redirectPath}?${searchParams.toString()}`);
  }, [
    availabilityId,
    history,
    isLoggedIn,
    isMoreThanTablet,
    path,
    router,
    scheduleAvailabilityPanelData,
    searchParams,
    setScheduleAvailabilityPanelData,
    userId,
  ]);

  const [mobileDisplayTimesheets, setMobileDisplayTimesheets] = useState(false);

  const {
    selectedHour,
    setSelectedHour,
    isLoadingOtherUserTimesheets,
    isLoadingAvailabilityUser,
    isLoadingPreferences,
    displayingTimezone,
    setDisplayingTimezone,
    displayingTimesheet,
    deleteTimesheet,
    shareTimesheet,
    timesheets,
    isBooking,
  } = useAvailability({
    userId,
    rawSelectedDay: (displayingDate ?? dayjs())?.format?.(),
    rawSelectedHour: rawSelectedHour as unknown as string,
    timesheetId: availabilityId,
  });
  const today = useMemo(
    () =>
      displayingTimezone?.code
        ? dayjs.tz(undefined, displayingTimezone.code)
        : dayjs(),
    [displayingTimezone]
  );
  const { data: availabilityUser, isLoading: availabilityUserLoading } =
    useReactQuery({
      action: {
        apiFunc: getUser,
        key: [QueryKeys.getUser, userId],
        params: {
          containsCroppedHeaderImageLink: true,
          userId,
        },
      },
      config: {
        enabled: Boolean(userId),
      },
    });
  const isLoading = useMemo(
    () =>
      searchParams.has('userId') ||
      searchParams.has('availabilityId') ||
      isFirstRender ||
      isLoadingOtherUserTimesheets ||
      isLoadingAvailabilityUser ||
      isLoadingPreferences ||
      availabilityUserLoading,
    [
      isLoadingOtherUserTimesheets,
      isLoadingAvailabilityUser,
      isLoadingPreferences,
      availabilityUserLoading,
      isFirstRender,
      searchParams,
    ]
  );
  const displayingDates = useMemo(() => {
    const startingDateOfWeek = Time.getStartingDateOfWeek(
      displayingDate ?? today
    );
    return weekDaysValueArray.map((wk, index) => {
      const weekDayDate = startingDateOfWeek.add(index, 'day');
      return {
        date: weekDayDate,
        monthDate: weekDayDate.date(),
        isToday: Time.areSameDay(today, weekDayDate),
      };
    });
  }, [displayingDate, today]);

  const { bookedTimes } = useGetBookedMeetings({
    startDate: Time.getStartingDateOfWeek(displayingDate ?? today).format(
      BE_TIME_FORMAT
    ),
    endDate: Time.getStartingDateOfWeek(displayingDate ?? today)
      .add(7, 'day')
      .format(BE_TIME_FORMAT),
    userId,
  });

  const nextWeek = () => {
    setDate((displayingDate ?? dayjs()).add(7, 'day'));
  };
  const previuosWeek = () => {
    setDate((displayingDate ?? dayjs()).subtract(7, 'day'));
  };

  const onHourClick = useCallback(
    (time: Dayjs) => {
      setSelectedHour(time);
    },
    [setSelectedHour]
  );
  const onCloseAuthenticationPanel = () => router.back();
  const onCalendarTodayClick = () => {
    setDate(today);
  };
  const setDate = (date: Dayjs) => {
    setScheduleAvailabilityPanelData(
      {
        ...scheduleAvailabilityPanelData,
        selectedDay: date,
      },
      { replace: true }
    );
  };

  const onCellClick = (date: DateType) => {
    setDate(date);
    setMobileDisplayTimesheets(true);
  };
  const onMobileBackFromHoursPanel = () => setMobileDisplayTimesheets(false);

  const onMobileAvailabilityOpenCrEdit = useCallback(
    (ts?: Timesheet) => {
      router.push(
        ts?.id
          ? routeNames.schedulesAvailabilityCrEdit.makeRoute(ts.id)
          : routeNames.schedulesAvailabilityCrEdit.main
      );
    },
    [router]
  );
  const onMobileAvailabilityOpenDetails = useCallback(
    (ts: Timesheet) => {
      router.push(routeNames.schedulesAvailability.makeRoute(ts.id));
    },
    [router]
  );

  if (isFirstRender || isLoading) return null;
  if (isLoggedIn && !isBooking && isMoreThanTablet === false) {
    return (
      <AvailabilityPanelContent
        isHeadless
        isLoading={isLoadingPreferences}
        onDeleteTimesheet={deleteTimesheet}
        onOpenAvailabilityCrEdit={onMobileAvailabilityOpenCrEdit}
        onOpenAvailabilityDetails={onMobileAvailabilityOpenDetails}
        onShareTimesheet={shareTimesheet}
        timesheets={timesheets}
      />
    );
  }

  if (isMoreThanTablet === false) {
    return (
      <div className={classes.mobileContainer}>
        {displayingTimesheet ? (
          <>
            {displayingTimezone && (
              <AvailabilityDetails
                selectedDate={displayingDate}
                timesheet={displayingTimesheet}
                creatorUser={availabilityUser}
                isLoadingCreatorUser={availabilityUserLoading}
                onTodayClick={onCalendarTodayClick}
                onCellClick={onCellClick}
                todayInBottom
                timezone={displayingTimezone}
                onSetTimezone={setDisplayingTimezone}
              />
            )}
            {mobileDisplayTimesheets &&
              displayingDate &&
              displayingTimezone?.code && (
                <FixedRightSideModalDialog onBack={onMobileBackFromHoursPanel}>
                  <AvailableHoursPanelContent
                    timesheet={displayingTimesheet}
                    day={dayjs.tz(displayingDate, displayingTimezone.code)}
                    bookedMeetings={bookedTimes}
                    isSelectMode
                    onHourClick={onHourClick}
                  />
                </FixedRightSideModalDialog>
              )}
            {selectedHour && (
              <AuthenticationPanel
                onClose={onCloseAuthenticationPanel}
                redirectUrl={`${routeNames.schedulesCalendarMonth}?state=${JSON.stringify(urlState)}`}
              />
            )}
          </>
        ) : (
          <EmptySectionInModules
            classNames={{ container: classes.emptySectionContainer }}
            title={t('availability_not_found')}
            text={t('availability_not_found_desc')}
          />
        )}
      </div>
    );
  }

  return (
    <>
      <Flex className={cnj(!isLoggedIn && classes.availabiliyPageContainer)}>
        <Flex className={cnj(classes.contentContainer)}>
          {displayingTimesheet ? (
            <>
              <Flex className={classes.availabilityDetails}>
                <AvailabilityDetails
                  selectedDate={displayingDate}
                  timesheet={displayingTimesheet}
                  creatorUser={availabilityUser}
                  isLoadingCreatorUser={availabilityUserLoading}
                  onTodayClick={onCalendarTodayClick}
                  onCellClick={onCellClick}
                  todayInBottom
                  timezone={displayingTimezone}
                  onSetTimezone={setDisplayingTimezone}
                />
              </Flex>
              <DividerVertical
                distance={0}
                className={classes.verticalDivider}
              />
              <Flex className="w-full p-20">
                <div className="flex flex-row w-full relative">
                  {displayingDates.map((date, index) => (
                    <div
                      className={cnj(
                        'flex flex-col items-center text-center grow'
                      )}
                      key={date.monthDate}
                    >
                      <Typography color="gray" size={10} font="400" height={14}>
                        {weekDaysValueArray[index].slice(0, 3)}
                      </Typography>
                      <Typography
                        font="700"
                        size={16}
                        height={22}
                        color={date.isToday ? 'brand' : 'graphene'}
                        className={cnj(
                          date.isToday && classes.todayDate,
                          classes.weekDay,
                          'rounded-full'
                        )}
                      >
                        {date.monthDate}
                      </Typography>
                    </div>
                  ))}
                  <IconButton
                    className="!absolute right-0 top-1/2 -translate-y-1/2"
                    name="chevron-right"
                    onClick={nextWeek}
                  />
                  <IconButton
                    className="!absolute left-0 top-1/2 -translate-y-1/2"
                    name="chevron-left"
                    onClick={previuosWeek}
                  />
                </div>
                <div className={cnj(classes.hoursContainer, 'flex')}>
                  {displayingDates.map((DD) => (
                    <AvailableHoursList
                      key={`ahphs-${DD.monthDate}`}
                      timesheet={displayingTimesheet}
                      day={DD.date}
                      bookedMeetings={bookedTimes}
                      className="flex-1 !p-0 !bg-transparent !mt-0"
                      isSelectMode
                      onHourClick={onHourClick}
                    />
                  ))}
                </div>
              </Flex>
            </>
          ) : (
            <EmptySectionInModules
              classNames={{ container: classes.emptySectionContainer }}
              title={t('availability_not_found')}
              text={t('availability_not_found_desc')}
            />
          )}
        </Flex>
      </Flex>
      {selectedHour && (
        <AuthenticationPanel
          onClose={onCloseAuthenticationPanel}
          redirectUrl={`${routeNames.schedulesCalendarMonth}?state=${JSON.stringify(urlState)}`}
        />
      )}
    </>
  );
};

export default Availability;
