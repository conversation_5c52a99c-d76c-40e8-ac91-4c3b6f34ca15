import Flex from 'shared/uikit/Flex/Flex.component';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import Divider from 'shared/uikit/Divider';
import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import classes from './loading.module.scss';

interface LoadingProps {
  className?: string;
}

export default function loading({ className }: LoadingProps) {
  return (
    <Flex className={cnj(classes.bookingSkeleton, className)}>
      <Flex className={classes.dateWrapper}>
        <Skeleton style={{ width: 22, height: 14, marginBottom: 2 }} />
        <Skeleton style={{ width: 24, height: 24, borderRadius: 99 }} />
      </Flex>
      <Flex className={classes.timeZoneWrapper}>
        <Skeleton style={{ width: 37, height: 8 }} />
      </Flex>
      {Array(3)
        .fill(0)
        .map((_, i) => (
          <Flex key={`booking_${i}`} className={classes.bookLine}>
            <Skeleton style={{ width: 25, height: 12, marginRight: 8 }} />
            <Divider />
          </Flex>
        ))}
    </Flex>
  );
}
