'use client';

import { useCallback, useMemo, useRef, useState, type MouseEvent } from 'react';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Flex from 'shared/uikit/Flex';
import Icon from 'shared/uikit/Icon';
import Typography from 'shared/uikit/Typography';
import useMedia from 'shared/uikit/utils/useMedia';
import { routeNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { getLabel } from 'shared/utils/getLabel';
import { EventCardDay } from 'shared/components/molecules/EventCard/EventCard.day';
import { getSpecsDay } from 'shared/utils/getSpecs.day';
import { Time } from 'shared/utils/Time';
import { useVisibleEvents } from 'shared/hooks/useVisibleEvents';
import { useSchedulesState } from '@shared/stores/schedulesStore';
import {
  selectFilteredEvents,
  selectViewDate,
} from '@shared/stores/schedulesStore/schedulesStore.selectors';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import { useResizeObserver } from 'shared/hooks/useResizeObserver';
import { useSeparateNormalEventsFromMultipleDayLongEvents } from 'shared/hooks/useSeparateNormalEventsFromMultipleDayLongEvents';
import { DateInfoBadge } from 'shared/components/molecules/DateInfoBadge/DateInfoBadge';
import { usePrefetchRoutes } from 'shared/hooks/usePrefetchRoutes';
import { MobileWeekDayLabels } from '../mobile/MobileWeekDayLabels';
import classes from './page.module.scss';

const NUMBER_OF_SHOWING_ITEMS = 5;

function SchedulesEventBoardDay() {
  const NUMBER_OF_SECTIONS = 24;
  const eventsSectionRef = useRef<HTMLElement>(null);
  const { openCreateEventWithDate } = useSchedulesCalendar();
  const viewDate = useSchedulesState(selectViewDate);
  const savedEvents = useSchedulesState(selectFilteredEvents);
  const startTime = Time.getStartingDateOfDay(viewDate?.start);
  const endTime = Time.incrementDateBy(startTime, 'day');
  const { isMoreThanTablet } = useMedia();
  const { t } = useTranslation();
  const [isShowAll, setIsShowAll] = useState(false);
  const [eventsSectionSpecs, setEventsSectionSpecs] = useState({
    width: 0,
    height: 0,
  });

  const setEventsSectionSpecsCallback = useCallback(() => {
    setEventsSectionSpecs({
      height: eventsSectionRef.current?.clientHeight || 0,
      width: eventsSectionRef.current?.clientWidth || 0,
    });
  }, []);
  useResizeObserver(setEventsSectionSpecsCallback, eventsSectionRef.current);

  const { normalEvents, allDayEvents } = useVisibleEvents({
    events: savedEvents,
    startTime,
    endTime,
  });
  const { singleDayLongEvents, multipleDayLongEvents } =
    useSeparateNormalEventsFromMultipleDayLongEvents(normalEvents);

  usePrefetchRoutes([
    routeNames.schedulesCalendarWeek,
    routeNames.schedulesCalendarMonth,
    routeNames.schedulesCalendarYear,
    routeNames.schedulesMeetings,
    routeNames.schedulesEvents,
    routeNames.schedulesReminders,
    routeNames.schedulesTasks,
  ]);

  const handleClick = (e: MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (!eventsSectionRef?.current?.clientHeight)
      throw new Error(
        'Click handler is called before component initial render.'
      );
    try {
      const cellHeight =
        eventsSectionRef.current.clientHeight / NUMBER_OF_SECTIONS;

      const top =
        e.clientY - eventsSectionRef.current.getBoundingClientRect().top;

      const verticalNumber =
        Math.floor(top / cellHeight) +
        Math.floor((top / cellHeight - Math.floor(top / cellHeight)) / 0.25) /
          4;
      const date = Time.incrementDateBy(startTime, 'hour', verticalNumber);
      openCreateEventWithDate(date);
    } catch (error) {
      console.error(error);
    }
  };

  const topEvents = useMemo(
    () => [...allDayEvents, ...multipleDayLongEvents],
    [allDayEvents, multipleDayLongEvents]
  );

  const toggleShowAll = () => {
    setIsShowAll((prev) => !prev);
  };

  const needsShowAllButton = topEvents?.length > NUMBER_OF_SHOWING_ITEMS;

  return (
    <Flex>
      {isMoreThanTablet ? (
        <DateInfoBadge
          dayLabel={viewDate?.dayOfWeekShortenedLabel}
          dayNumber={viewDate?.dayNumberInMonth}
        />
      ) : (
        <MobileWeekDayLabels calendarBoardType="day" />
      )}
      <Flex className={classes.allDaySection}>
        <Flex className={classes.timeZoneLabel}>
          <Typography color="gray" size={8} height={8} noWrap>
            {Time.getTimeZoneOffsetLabel()}
          </Typography>
        </Flex>
        <Flex className={classes.allDaySectionEvents}>
          {topEvents
            .slice(0, isShowAll ? undefined : NUMBER_OF_SHOWING_ITEMS)
            .map((item) => (
              <Flex className={classes.cardMarginBot} key={item?.uniqueEventId}>
                <EventCardDay
                  eventData={item}
                  isMultipleDay={!item?.isAllDay}
                  isAllDay={item?.isAllDay}
                  dayTime={item?.isAllDay ? undefined : { startTime, endTime }}
                />
              </Flex>
            ))}
          {needsShowAllButton && (
            <BaseButton onClick={toggleShowAll} className={classes.allButton}>
              <Typography color="primaryText" font="700" size={15}>
                {isShowAll ? t('show_less') : t('show_all')}
              </Typography>
              <Icon
                color="primaryText"
                name={isShowAll ? 'chevron-down' : 'chevron-up'}
                size={13}
                className={classes.chevronIcon}
              />
            </BaseButton>
          )}
        </Flex>
      </Flex>
      <Flex className={classes.container}>
        <Flex className={classes.labelSection}>
          {Array(NUMBER_OF_SECTIONS)
            .fill(null)
            .map((_, index) => (
              <Flex key={`label-${index}`} className={classes.labelContainer}>
                <Typography className={classes.label} size={10}>
                  {getLabel(index, t)}
                </Typography>
              </Flex>
            ))}
        </Flex>
        <Flex
          className={classes.eventSection}
          ref={eventsSectionRef}
          onClick={handleClick}
        >
          {Array(NUMBER_OF_SECTIONS)
            .fill(null)
            .map((_, index) => (
              <Flex
                key={`single-line-section-${index}`}
                className={classes.singleSectionLine}
              />
            ))}
          {singleDayLongEvents?.map((event) => (
            <EventCardDay
              key={event?.uniqueEventId}
              eventData={event}
              specs={getSpecsDay({
                event,
                startTime,
                endTime,
                containerSpecs: eventsSectionSpecs,
                events: singleDayLongEvents,
              })}
            />
          ))}
        </Flex>
      </Flex>
    </Flex>
  );
}

export default SchedulesEventBoardDay;
