import BottomSheet from 'shared/uikit/BottomSheet';
import Flex from 'shared/uikit/Flex';
import Icon from 'shared/uikit/Icon';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import React, { useCallback, useMemo, useState } from 'react';
import useHistory from 'shared/utils/hooks/useHistory';
import type { CalendarBoardType } from 'shared/types/schedules/schedules';
import classes from './CalendarTypeDrawer.module.scss';
import { boolean } from 'yup';

type Tab = {
  path: string;
  title: string;
  icon: string;
};

type Props = {
  calendarBoardType: CalendarBoardType;
  tabs: Tab[];
};

export function CalendarTypeDrawer({ calendarBoardType, tabs }: Props) {
  const [isOpen, setIsOpen] = useState(false);
  const bottomSheetRef = React.useRef(null);
  const history = useHistory();

  const toggleDrawer = useCallback(function toggleDrawer() {
    setIsOpen((prev) => !prev);
  }, []);
  const isActive = useCallback(
    (item: Tab) => item?.title?.toLocaleLowerCase() === calendarBoardType,
    [calendarBoardType]
  );
  const TitleButton = useMemo(
    () => (
      <Flex className={classes.titleContainer}>
        <Typography
          className={classes.title}
          size={16}
          font="700"
          color="primaryText"
        >
          {tabs?.find(isActive)?.title}
        </Typography>
        <Icon
          size={14}
          name={isOpen ? 'chevron-down' : 'chevron-up'}
          color="primaryText"
        />
      </Flex>
    ),
    [isOpen, isActive, tabs]
  );

  const handleTabClick = useCallback(
    (path: string) => () => {
      history.push(path);
      toggleDrawer();
    },
    [history, toggleDrawer]
  );

  return isOpen ? (
    <BottomSheet
      open
      ref={bottomSheetRef}
      modalElementClass={classes.modalElementClass}
      containerElementClass={cnj(
        isOpen
          ? classes.containerElementClassActive
          : classes.containerElementClass
      )}
      onRequestClose={toggleDrawer}
    >
      <Flex onClick={toggleDrawer}> {TitleButton}</Flex>
      <Flex className={classes.itemsContainer}>
        {tabs?.map((tab) => (
          <Flex
            key={`ktd-t-${tab.path}`}
            className={cnj(classes.singleItem, isActive(tab) && classes.active)}
            onClick={handleTabClick(tab?.path)}
          >
            <Icon size={14} name={tab?.icon} color="smoke_coal" type="far" />
            <Typography
              className={classes.itemTitle}
              size={15}
              font="400"
              color="primaryText"
            >
              {tab?.title}
            </Typography>
          </Flex>
        ))}
      </Flex>
    </BottomSheet>
  ) : (
    <Flex className={classes.button} onClick={toggleDrawer}>
      {TitleButton}
    </Flex>
  );
}
