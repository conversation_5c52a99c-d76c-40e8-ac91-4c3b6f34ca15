import { useCallback, useState, type MouseEventHandler } from 'react';
import { Carousel } from 'react-responsive-carousel';
import Flex from 'shared/uikit/Flex';
import cnj from 'shared/uikit/utils/cnj';
import useMedia from 'shared/uikit/utils/useMedia';
import type { CalendarBoardType } from 'shared/types/schedules/schedules';
import { Time } from 'shared/utils/Time';
import DatePicker from 'shared/components/Organism/DatePicker/DatePicker';
import { useSwipe } from 'shared/hooks/useSwipe';
import {
  useSchedulesState,
  setDisplayDate,
} from '@shared/stores/schedulesStore';
import { selectDisplayDate } from '@shared/stores/schedulesStore/schedulesStore.selectors';
import classes from './MobileWeekDayLabels.module.scss';

type Props = {
  calendarBoardType: CalendarBoardType;
};

const NUMBER_OF_ITEMS_IN_CAROUSEL = 5;

export function MobileWeekDayLabels({ calendarBoardType }: Props) {
  const { isMoreThanTablet } = useMedia();
  const data = useSchedulesState();
  const viewDate = useSchedulesState(selectDisplayDate);
  const [isMonthView, setIsMonthView] = useState(false);
  const [selectedCarouselIndex, setSelectedCarouselIndex] = useState(1);
  const touchCallbacks = useSwipe(
    () => setIsMonthView(false),
    () => setIsMonthView(true)
  );
  const toggleView: MouseEventHandler<HTMLDivElement> = useCallback((e) => {
    e?.stopPropagation();
    setIsMonthView((prev) => !prev);
  }, []);
  const handleDayChoosing = useCallback(() => {
    setIsMonthView(false);
    setSelectedCarouselIndex(0);
  }, []);

  const increment = (count = 0) => {
    setDisplayDate(viewDate.add(1, 'week'));
  };
  const decrement = (count = 0) => {
    setDisplayDate(viewDate.subtract(1, 'week'));
  };
  const onSwipe = (item: number) => {
    if (item === 0) {
      if (selectedCarouselIndex === 1) decrement();
      else increment();
    } else if (selectedCarouselIndex === 0) {
      if (item === 4) decrement();
      else increment();
    } else if (item - selectedCarouselIndex > 0) increment();
    else decrement();
    setSelectedCarouselIndex(item);
  };

  if (!isMoreThanTablet && ['month', 'year']?.includes(calendarBoardType))
    return null;

  return (
    <Flex className={cnj(classes.container)}>
      <Carousel
        onChange={onSwipe}
        showIndicators={false}
        className={classes.carouselRoot}
        showArrows={false}
        showStatus={false}
        infiniteLoop
        swipeable
        preventMovementUntilSwipeScrollTolerance
        swipeScrollTolerance={50}
        autoPlay={false}
        showThumbs={false}
        selectedItem={1}
      >
        {Array(NUMBER_OF_ITEMS_IN_CAROUSEL)
          .fill(null)
          .map(() => (
            <DatePicker
              key={viewDate.format()}
              data={data}
              localviewDate={Time.getViewDateDetails(viewDate)}
              numberOfCells={isMonthView ? 35 : 7}
              classNames={{
                datePickerContainer: classes.datePickerContainer,
                datePickerCell: classes.datePickerCell,
              }}
              isWeek
              onCellClick={handleDayChoosing}
            />
          ))}
      </Carousel>
      <Flex
        className={classes.button}
        {...touchCallbacks}
        onClick={toggleView}
      />
      <div />
    </Flex>
  );
}
