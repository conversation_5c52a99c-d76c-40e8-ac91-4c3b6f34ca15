'use client';

import { type MouseEvent, useCallback, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { getMonthArray } from '@shared/components/Organism/DatepickerV3/utils';
import CalendarView from '@shared/components/molecules/CalendarView/CalendarView';
import CalendarRow from '@shared/components/molecules/CalendarRow/CalendarRow';
import cnj from '@shared/uikit/utils/cnj';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import { useSwipe } from 'shared/hooks/useSwipe';
import CalendarCell from '@shared/components/molecules/CalendarCell/CalendarCell';
import { useResizeObserver } from '@shared/hooks/useResizeObserver';
import SchedulesCalendarEvents from '../partials/SchedulesCalendarEvents/SchedulesCalendarEvents';
import classes from './page.module.scss';

const weekendDays = [1, 7];

function isStartofMonth(date: string) {
  return dayjs(date).isSame(dayjs(date).startOf('month'), 'day');
}

function SchedulesEventBoardMonth() {
  const { t } = useTranslation();
  const {
    incrementViewDateBy,
    decrementViewDateBy,
    openCreateEventWithDate,
    data,
  } = useSchedulesCalendar();

  const swipCallbacks = useSwipe(
    () => {
      incrementViewDateBy('month');
    },
    () => {
      decrementViewDateBy('month');
    }
  );

  const selectedDateStr = data?.viewDate?.start?.format();
  const month = useMemo(
    () => getMonthArray(data?.viewDate?.start?.format(), 6),
    [data?.viewDate?.start]
  );

  const isInMonth = useCallback(
    (date: string) =>
      dayjs(data?.viewDate?.start?.format()).isSame(dayjs(date), 'month'),
    [data?.viewDate?.start]
  );
  const isToday = (date: string) => dayjs(date).isSame(dayjs(), 'day');

  const isSelected = (date: string) =>
    dayjs(date).isSame(dayjs(selectedDateStr), 'day');
  const isWeekend = (dayOfWeek: number) => weekendDays.includes(dayOfWeek);

  const handleCreateEvent = (date: string) => (e?: MouseEvent<HTMLElement>) => {
    if (isToday(date)) openCreateEventWithDate();
    else openCreateEventWithDate(dayjs(date).set('hour', 8).set('minute', 0));
  };

  const [hoveredEvent, setHoveredEvent] = useState<string | null>(null);

  const calendarWrapperRef = useRef<HTMLDivElement>(null);
  const [numberOfBadges, setNumberOfBadges] = useState(3);
  const resizeObserver = () => {
    const currentHeight = calendarWrapperRef?.current?.clientHeight;
    if (!currentHeight) {
      setNumberOfBadges(3);
      return;
    }
    const cellHeight = currentHeight / 6;
    const minBadges = Math.floor((cellHeight - 30) / 18);

    if (minBadges > 3) setNumberOfBadges(minBadges);
    else setNumberOfBadges(3);
  };
  useResizeObserver(resizeObserver, calendarWrapperRef?.current);

  return (
    <Flex className={classes.outterWrapper}>
      <Flex className={classes.innerWrapper}>
        <Flex className={classes.weekDaysContainer}>
          {dayjs.weekdaysShort().map((item) => (
            <Flex className={classes.eachWeekDayLabelContainer} key={item}>
              <Typography size={10} height={14} font="400" color="gray">
                {t(item)}
              </Typography>
            </Flex>
          ))}
        </Flex>
        <Flex
          ref={calendarWrapperRef}
          className={classes.container}
          {...swipCallbacks}
        >
          <CalendarView className={classes.calendarView}>
            {month.map((week, weekIdx) => (
              <CalendarRow
                className={classes.calendarRow}
                key={`calendar-month-row-${weekIdx}`}
              >
                {week.map((date, dayIdx) => (
                  <CalendarCell
                    key={`calendar-month-cell-${weekIdx}-${dayIdx}`}
                    date={date}
                    transform={dayjs}
                    format={isStartofMonth(date) ? 'MMM D' : 'D'}
                    className={cnj(
                      classes.calendarCell,
                      !isInMonth(date) && classes.isOutMonth,
                      isWeekend(dayIdx + 1) && classes.isWeekend,
                      isToday(date) && classes.isToday,
                      isSelected(date) && classes.isSelected
                    )}
                    typographyProps={{
                      className: classes.dateLabel,
                      onClick: handleCreateEvent(date),
                    }}
                  >
                    <SchedulesCalendarEvents
                      date={date}
                      show={numberOfBadges}
                      hoveredEvent={hoveredEvent}
                      setHoveredEvent={setHoveredEvent}
                      onCreateEvent={handleCreateEvent(date)}
                    />
                  </CalendarCell>
                ))}
              </CalendarRow>
            ))}
          </CalendarView>
        </Flex>
      </Flex>
    </Flex>
  );
}

export default SchedulesEventBoardMonth;
