'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import cnj from '@shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Tabs from 'shared/uikit/Tabs';
import useMedia from 'shared/uikit/utils/useMedia';
import IconButton from 'shared/uikit/Button/IconButton';
import Button from 'shared/uikit/Button';
import Typography from 'shared/uikit/Typography';
import Tooltip from 'shared/uikit/Tooltip';
import { SCHEDULE_CALENDAR_RIGHT_SIDE_WRAPPER } from 'shared/constants/enums';
import { routeNames } from 'shared/utils/constants/routeNames';
import { useGlobalState } from 'shared/contexts/Global/global.provider';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { EventsIntegrationWithModal } from 'shared/components/molecules/EventsIntegration/EventsIntegration.modal';
import { useObserveToAddProvider } from 'shared/components/molecules/EventsIntegration/utils/useObserveToAddProvider';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import { useSetEvents } from 'shared/hooks/useSetEvents';
import useEffectOnlyChange from 'shared/utils/hooks/useEffectOnlyChange';
import {
  setTemporaryEvent,
  useSchedulesState,
} from '@shared/stores/schedulesStore';
import { selectDisplayDate } from '@shared/stores/schedulesStore/schedulesStore.selectors';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import SchedulesCalendarSection from './SchedulesCalendarSection/SchedulesCalendarSection';
import SchedulesCloseEventList from './SchedulesCloseEventLists/SchedulesCloseEventList';
import { CalendarTypeDrawer } from '../mobile/CalendarTypeDrawer';
import classes from './CalendarLayout.module.scss';

type Props = {
  children: ReactNode;
};

enum DateFormats {
  'day' = 'MMMM D, YYYY',
  'month' = 'MMMM YYYY',
  'week' = 'MMMM YYYY',
  'year' = 'YYYY',
}

const SchedulesCalendarTabbedLayout = ({ children }: Props) => {
  const { t } = useTranslation();
  const tabRef = useRef();
  const [isLeftDrawerOpen, setIsLeftDrawerOpen] = useState(true);
  const searchParams = useSearchParams();
  const openTab = searchParams?.get('open') as any;
  const [rightDrawerContent, setRightDrawerContent] = useState<
    '' | 'integration' | 'near'
  >('');
  const {
    state: { scheduleEventsPanelData },
  } = useSchedulesUrlState();
  const { isInCrEdit } = scheduleEventsPanelData || {};

  useEffectOnlyChange(() => {
    if (!openTab) return;
    setRightDrawerContent(openTab);
  }, [openTab]);

  const { isMoreThanTablet } = useMedia();
  const isMoreThanTabletIsLoaded = typeof isMoreThanTablet !== 'undefined';
  const {
    setViewDateToToday,
    handleDecrement,
    handleIncrement,
    calendarBoardType,
  } = useSchedulesCalendar();

  const isOpenRightPanel = useGlobalState('isOpenRightPanel');
  const pushFromRightSide = isOpenRightPanel && !rightDrawerContent;

  const useObserverCallback = useCallback(
    () => setRightDrawerContent('integration'),
    [setRightDrawerContent]
  );

  useObserveToAddProvider({
    onSuccess: useObserverCallback,
  });

  const toggleLeftDrawer = useCallback(
    () => setIsLeftDrawerOpen((prev) => !prev),
    []
  );
  useEffect(() => {
    if (!rightDrawerContent) setRightDrawerContent('near');
  }, []);

  useEffect(() => {
    // Ensures that upon closing creation event form, the temporary event is deleted on the calendar board
    if (isInCrEdit) return;
    setTemporaryEvent(null);
  }, [isInCrEdit]);

  const tabs = [
    {
      path: `${routeNames.schedulesCalendarDay}`,
      title: t('day'),
      icon: 'calendar-rectangle',
    },
    {
      path: `${routeNames.schedulesCalendarWeek}`,
      title: t('week'),
      icon: 'calendar-week',
    },
    {
      path: `${routeNames.schedulesCalendarMonth}`,
      title: t('month'),
      icon: 'calendar-alt',
    },
    {
      path: `${routeNames.schedulesCalendarYear}`,
      title: t('year'),
      icon: 'calendar-year',
    },
  ];

  useSetEvents();

  const date = useSchedulesState(selectDisplayDate);

  const PrecedingButtons = useMemo(
    () => (
      <Flex className={classes.precedingButtonsContainer}>
        <Tooltip
          placement="bottom"
          trigger={
            <IconButton
              name="calendar-days"
              colorSchema={
                isLeftDrawerOpen ? 'tertiary-transparent' : 'secondary'
              }
              type={isLeftDrawerOpen ? 'fas' : 'far'}
              onClick={toggleLeftDrawer}
            />
          }
        >
          <Typography size={13} font="400" height={17} color="tooltipText">
            {t('calendars')}
          </Typography>
        </Tooltip>
        <Flex className={classes.verticalDivider} />
        <Button
          label={t('today_cap')}
          schema="semi-transparent"
          onClick={setViewDateToToday}
        />
        <Typography
          font="700"
          size={16}
          className={classes.date}
          color="primaryText"
        >
          {date.format(DateFormats[calendarBoardType])}
        </Typography>
        <IconButton
          name="chevron-left"
          size="sm"
          colorSchema="secondary-transparent"
          type="fas"
          className={classes.leftChevron}
          onClick={handleDecrement}
        />
        <IconButton
          name="chevron-right"
          size="sm"
          colorSchema="secondary-transparent"
          type="fas"
          onClick={handleIncrement}
        />
      </Flex>
    ),
    [
      isLeftDrawerOpen,
      toggleLeftDrawer,
      t,
      setViewDateToToday,
      date,
      calendarBoardType,
      handleDecrement,
      handleIncrement,
    ]
  );

  const ActionButtons = useMemo(
    () => (
      <Flex className={classes.endActionButtonsContainer}>
        <Tooltip
          placement="bottom"
          trigger={
            <IconButton
              name="sync2"
              colorSchema={
                rightDrawerContent === 'integration'
                  ? 'tertiary-transparent'
                  : 'transparent1'
              }
              type="far"
              onClick={() =>
                setRightDrawerContent((prev) =>
                  prev === 'integration' ? '' : 'integration'
                )
              }
            />
          }
        >
          <Typography size={13} font="400" height={17} color="tooltipText">
            {t('integrations')}
          </Typography>
        </Tooltip>
        <Tooltip
          placement="bottom"
          trigger={
            <IconButton
              name="schedules-show-all"
              colorSchema={
                rightDrawerContent === 'near'
                  ? 'tertiary-transparent'
                  : 'transparent1'
              }
              type={rightDrawerContent === 'near' ? 'fas' : 'far'}
              onClick={() =>
                setRightDrawerContent((prev) => (prev === 'near' ? '' : 'near'))
              }
            />
          }
        >
          <Typography size={13} font="400" height={17} color="tooltipText">
            {t('show_all')}
          </Typography>
        </Tooltip>
      </Flex>
    ),
    [t, setRightDrawerContent, rightDrawerContent]
  );

  return (
    <>
      <Tabs
        linkVariant="badge"
        isLoading={false}
        ref={tabRef}
        isFullWidth={false}
        tabs={tabs}
        contentRootClassName={cnj(classes.contentRoot)}
        className="h-full"
        linkAndActionWrapperClassName={classes.linkAndActionWrapperClassName}
        linksRootClassName={classes.linksRootClassName}
        navLinkClassName={classes.navLinkClassName}
        navLinkActiveClassName={classes.navLinkActiveClassName}
        tabsWrapperClassName={classes.tabsWrapperClassName}
        innerClassName={classes.calendarInnerWrapper}
        actionButton={isMoreThanTablet ? ActionButtons : undefined}
        precedingActionButton={isMoreThanTablet ? PrecedingButtons : undefined}
      >
        {isLeftDrawerOpen && (
          <Flex className={classes.leftDrawer}>
            <SchedulesCalendarSection />
          </Flex>
        )}
        <Flex className={classes.mainContentContainer}>{children}</Flex>
        {!!rightDrawerContent && isMoreThanTablet && (
          <Flex
            className={classes.rightDrawer}
            id={SCHEDULE_CALENDAR_RIGHT_SIDE_WRAPPER}
          >
            {!!rightDrawerContent &&
              (rightDrawerContent === 'near' ? (
                <Flex className={classes.closeEventsWrapper}>
                  <SchedulesCloseEventList />
                </Flex>
              ) : (
                <EventsIntegrationWithModal disablePortal />
              ))}
          </Flex>
        )}
      </Tabs>
      {isMoreThanTabletIsLoaded && !isMoreThanTablet && (
        <CalendarTypeDrawer calendarBoardType={calendarBoardType} tabs={tabs} />
      )}
    </>
  );
};

export default SchedulesCalendarTabbedLayout;
