import { type MouseEvent } from 'react';
import dayjs from 'dayjs';
import CalendarEvent from '@shared/components/molecules/CalendarEvent/CalendarEvent';
import { EventsListPopper } from '@shared/components/molecules/EventsListPopover/EventsListPopper';
import { useSchedulesState } from '@shared/stores/schedulesStore';
import { selectDayEvents } from '@shared/stores/schedulesStore/schedulesStore.selectors';
import Typography from '@shared/uikit/Typography';
import type { SavedEventType } from '@shared/types/schedules/schedules';

export default function SchedulesCalendarEvents({
  date,
  show,
  hoveredEvent,
  setHoveredEvent,
  onCreateEvent,
  filter = (item) => !!item,
}: {
  date: string;
  show: number;
  hoveredEvent?: string | null;
  setHoveredEvent?: (id: string | null) => void;
  onCreateEvent?: (clickEvent: MouseEvent<HTMLElement>) => void;
  filter?: (event: SavedEventType | null) => boolean;
}) {
  const events = useSchedulesState(selectDayEvents(date, true)).filter(filter);

  while (events.length < show) events.push(null);
  return (
    <>
      {events.slice(0, show - 1).map((event, idx) => (
        <CalendarEvent
          key={`event-${idx}`}
          {...{
            hoveredEvent,
            setHoveredEvent,
          }}
          date={date}
          event={event}
          onCreateEvent={onCreateEvent}
        />
      ))}
      {events.length > show ? (
        <EventsListPopper date={dayjs(date)}>
          <Typography
            variant="span"
            size={12}
            height={16}
            textAlign="center"
            className="pointer"
          >
            {`+${events.length - show} more`}
          </Typography>
        </EventsListPopper>
      ) : (
        <CalendarEvent
          date={date}
          event={events?.[show - 1]}
          onCreateEvent={onCreateEvent}
        />
      )}
    </>
  );
}
