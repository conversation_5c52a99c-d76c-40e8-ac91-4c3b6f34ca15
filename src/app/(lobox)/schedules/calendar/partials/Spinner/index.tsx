'use client';

import <PERSON><PERSON> from 'react-lottie';

import React from 'react';
import Flex from 'shared/uikit/Flex/index';
import classes from './index.module.scss';
import loadingAnimation from './loading.json';

const Spinner: React.FC = () => (
  <Flex className={classes.container}>
    <Flex className={classes.centeredBox}>
      <Flex>
        <Lottie
          isClickToPauseDisabled
          options={{
            loop: true,
            autoplay: true,
            animationData: loadingAnimation,
          }}
          width={100}
          height={100}
        />
      </Flex>
    </Flex>
  </Flex>
);

export default Spinner;
