'use client';

import { useCallback, useRef, useState, type MouseEvent } from 'react';
import dayjs from 'dayjs';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import useMedia from 'shared/uikit/utils/useMedia';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useVisibleEvents } from 'shared/hooks/useVisibleEvents';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import { useSeparateNormalEventsFromMultipleDayLongEvents } from 'shared/hooks/useSeparateNormalEventsFromMultipleDayLongEvents';
import { getLabel } from 'shared/utils/getLabel';
import { EventCardWeek } from 'shared/components/molecules/EventCard/EventCard.week';
import { getSpecsWeek } from 'shared/utils/getSpecs.week';
import { weekDay3LetterLabels } from 'shared/utils/consts';
import { makeDynamicStyles } from 'shared/uikit/utils/makeDynamicStyles';
import { getWeekArray } from '@shared/components/Organism/DatepickerV3/utils';
import { useResizeObserver } from 'shared/hooks/useResizeObserver';
import { useSchedulesState } from '@shared/stores/schedulesStore';
import {
  selectFilteredEvents,
  selectViewDate,
} from '@shared/stores/schedulesStore/schedulesStore.selectors';
import { Time } from 'shared/utils/Time';
import CalendarRow from '@shared/components/molecules/CalendarRow/CalendarRow';
import CalendarCell from '@shared/components/molecules/CalendarCell/CalendarCell';
import SchedulesCalendarEvents from '../partials/SchedulesCalendarEvents/SchedulesCalendarEvents';
import { MobileWeekDayLabels } from '../mobile/MobileWeekDayLabels';
import classes from './page.module.scss';

const weekendDays = [1, 7];
const isWeekend = (dayOfWeek: number) => weekendDays.includes(dayOfWeek);

function SchedulesEventBoardWeek(): JSX.Element {
  const NUMBER_OF_SECTIONS = 24;
  const eventsSectionRef = useRef<HTMLElement>(null);
  const { isMoreThanTablet } = useMedia();
  const { t } = useTranslation();

  const [eventsSectionSpecs, setEventsSectionSpecs] = useState({
    width: 0,
    height: 0,
  });

  useResizeObserver(() => {
    setEventsSectionSpecs({
      height: eventsSectionRef.current?.clientHeight || 0,
      width: eventsSectionRef.current?.clientWidth || 0,
    });
  }, eventsSectionRef.current);

  const { openCreateEventWithDate } = useSchedulesCalendar();
  const viewDate = useSchedulesState(selectViewDate);
  const savedEvents = useSchedulesState(selectFilteredEvents);
  const startTime = Time.getStartingDateOfWeek(viewDate?.start);
  const endTime = Time.incrementDateBy(startTime, 'week');

  const { normalEvents } = useVisibleEvents({
    events: savedEvents,
    startTime,
    endTime,
  });
  const { singleDayLongEvents } =
    useSeparateNormalEventsFromMultipleDayLongEvents(normalEvents);

  const handleClick = (e: MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (!eventsSectionRef?.current)
      throw new Error(
        'Click handler is called before component initial render.'
      );
    try {
      const cellWidth =
        eventsSectionRef.current.clientWidth / weekDay3LetterLabels.length;
      const cellHeight =
        eventsSectionRef.current.clientHeight / NUMBER_OF_SECTIONS;

      const left =
        e.clientX - eventsSectionRef.current.getBoundingClientRect().left;
      const top =
        e.clientY - eventsSectionRef.current.getBoundingClientRect().top;

      const horizontalNumber = Math.floor(left / cellWidth);
      const verticalNumber =
        Math.floor(top / cellHeight) +
        Math.floor((top / cellHeight - Math.floor(top / cellHeight)) / 0.25) /
          4;
      const overAllNumber = horizontalNumber * 24 + verticalNumber;
      const date = Time.incrementDateBy(startTime, 'hour', overAllNumber);
      openCreateEventWithDate(date);
    } catch (error) {
      console.error(error);
    }
  };

  const isInMonth = useCallback(
    (date: string) =>
      dayjs(viewDate?.start?.format()).isSame(dayjs(date), 'month'),
    [viewDate?.start]
  );

  const week = getWeekArray(viewDate?.start.format());
  return (
    <Flex>
      <Flex>
        {isMoreThanTablet ? (
          <Flex className={classes.weekDaysContainer}>
            {dayjs.weekdaysShort().map((item, index) => {
              const thisItemTime = Time.incrementDateBy(
                startTime,
                'day',
                index
              );
              const isActive = Time.areSameDay(thisItemTime, Time.getToday());
              const monthDateLabel = thisItemTime?.get('date');
              return (
                <Flex className={classes.eachWeekDayLabelContainer} key={item}>
                  <Typography size={10} font="400" color="gray">
                    {item}
                  </Typography>
                  <Typography
                    size={16}
                    font="700"
                    color={isActive ? 'brand' : 'inherit'}
                    className={cnj(isActive && classes.activeDayNumber)}
                  >
                    {monthDateLabel}
                  </Typography>
                </Flex>
              );
            })}
          </Flex>
        ) : (
          <MobileWeekDayLabels calendarBoardType="week" />
        )}
        <Flex className={cnj(classes.container, classes.upperContainer)}>
          <Flex>
            <Typography
              className={cnj(classes.label, classes.timezoneLabel)}
              size={8}
            >
              GMT
            </Typography>
          </Flex>
          <CalendarRow className={classes.allDaySection}>
            {week.map((date, dayIdx) => (
              <CalendarCell
                key={date}
                date={date}
                transform={dayjs}
                format={undefined}
                className={cnj(
                  classes.calendarCell,
                  !isInMonth(date) && classes.isOutMonth,
                  isWeekend(dayIdx + 1) && classes.isWeekend
                )}
              >
                <SchedulesCalendarEvents
                  date={date}
                  show={3}
                  filter={(event) =>
                    event?.startTime &&
                    event?.endTime &&
                    !event?.startTime.isSame(event?.endTime, 'day')
                  }
                />
              </CalendarCell>
            ))}
          </CalendarRow>
        </Flex>
        <Flex className={cnj(classes.container, classes.lowerContainer)}>
          <Flex className={classes.labelSection}>
            {Array(NUMBER_OF_SECTIONS)
              .fill(null)
              .map((_, index) => (
                <Flex
                  className={classes.labelContainer}
                  key={`section-${index}`}
                >
                  <Typography className={classes.label} size={10}>
                    {getLabel(index, t)}
                  </Typography>
                </Flex>
              ))}
          </Flex>
          <Flex
            className={classes.eventSection}
            ref={eventsSectionRef}
            onClick={handleClick}
          >
            {singleDayLongEvents?.map((event) => (
              <EventCardWeek
                key={`${event?.startTime}-${event?.title}-${eventsSectionSpecs?.width}`}
                eventData={event}
                specs={getSpecsWeek({
                  event,
                  startTime,
                  endTime,
                  containerSpecs: eventsSectionSpecs,
                  events: singleDayLongEvents,
                })}
              />
            ))}
            {Array(weekDay3LetterLabels.length)
              .fill(null)
              .map((_, index) => (
                <Flex
                  key={`section-row-${index}`}
                  className={cnj(
                    classes.singleVerticalSectionLine,
                    [0, 6].includes(index) ? classes.weekendDay : undefined
                  )}
                  {...makeDynamicStyles({
                    width: `${100 / weekDay3LetterLabels.length}%`,
                    left: `${(index * 100) / weekDay3LetterLabels.length}%`,
                  })}
                />
              ))}
            {Array(NUMBER_OF_SECTIONS)
              .fill(null)
              .map((_, idx) => (
                <Flex
                  key={`singe-section-${idx}`}
                  className={classes.singleSectionLine}
                />
              ))}
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
}

export default SchedulesEventBoardWeek;
