'use client';

import { useEffect, useRef, useState, type MouseEvent } from 'react';
import { type VirtuosoHandle, type ListItem } from 'react-virtuoso';
import dayjs from 'dayjs';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useMedia from 'shared/uikit/utils/useMedia';
import ViewPortList from 'shared/uikit/ViewPortList';
import useTranslation from 'shared/utils/hooks/useTranslation';
import preventClickHandler from '@shared/utils/toolkit/preventClickHandler';
import { DatepickerV3 } from '@shared/components/Organism/DatepickerV3';
import {
  setDisplayDate,
  setDate as setViewDate,
  useSchedulesState,
} from '@shared/stores/schedulesStore';
import { getYearArray } from '@shared/components/Organism/DatepickerV3/utils';
import {
  selectDisplayDate,
  selectViewDate,
} from '@shared/stores/schedulesStore/schedulesStore.selectors';
import type { DateType } from 'shared/types/schedules/schedules';
import { Time } from 'shared/utils/Time';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import classes from './page.module.scss';

const SchedulesEventBoardYear = () => {
  const { t } = useTranslation();
  const scrollerRef = useRef<VirtuosoHandle | null>(null);
  const { isMoreThanTablet } = useMedia();

  const { setCalendarBoardType, data } = useSchedulesCalendar();
  const viewDate = useSchedulesState(selectViewDate);
  const displayDate = useSchedulesState(selectDisplayDate);

  const [firstItemIndex, setFirstItemIndex] = useState(0);
  const [yearArray, setYearArray] = useState<string[]>(
    getYearArray(viewDate.start.format(), true)
  );

  useEffect(() => {
    // Reset the state
    const monthIndex = viewDate.start.month();
    scrollerRef.current?.scrollToIndex(monthIndex - firstItemIndex);
  }, [viewDate.start]);

  const handleCellClick = (date: DateType) => {
    setViewDate(date);
    setCalendarBoardType('day');
  };

  const handleMonthLabelClick =
    (date: string) => (event: MouseEvent<HTMLElement>) => {
      preventClickHandler(event);
      setViewDate(dayjs(date));
      setCalendarBoardType('month');
    };

  const appendItems = () => {
    setYearArray((state) => {
      const lastDate = state[state.length - 1];
      const nextYear = getYearArray(
        dayjs(lastDate).add(1, 'month').format(),
        true
      );

      return [...state, ...nextYear];
    });
  };
  const prependItems = () => {
    setYearArray((state) => {
      const firstDate = state[0];
      const prevYear = getYearArray(
        dayjs(firstDate).subtract(1, 'month').format(),
        true
      );

      return [...prevYear, ...state];
    });
    setFirstItemIndex((state) => state - 12);
  };

  const handleViewItemsChange = (items: ListItem<string>[]) => {
    if (!items?.[1]?.data) return;
    const currentTop = dayjs(items[1].data);
    if (!currentTop.isSame(displayDate, 'year')) setDisplayDate(currentTop);
  };

  const itemContent = (_: number, itemData: string) => {
    const itemStartTime = dayjs(itemData);
    const monthLabel = itemStartTime.format('MMMM');
    const localViewDate = itemStartTime.isSame(viewDate.start, 'month')
      ? viewDate
      : Time.getViewDateDetails(itemStartTime);
    return (
      <Flex className={cnj(classes.monthDatePickerWrapper, `calendar`)}>
        <Typography
          color="primaryText"
          font="500"
          size={20}
          className={cnj(classes.monthLabel, classes.clickable)}
          onClick={handleMonthLabelClick(itemStartTime.format())}
        >
          {t(monthLabel)}
        </Typography>
        <DatepickerV3
          hideHeader
          hideToday
          data={data}
          localviewDate={localViewDate}
          doNotShowActivesOfAdjacentMonths
          onCellClick={handleCellClick}
          disableDefaultBehaviour
          hideWeekDayLabels
          showEventsOnHover
        />
      </Flex>
    );
  };

  return (
    <Flex className={cnj(classes.container)}>
      {isMoreThanTablet ? (
        yearArray.map((month, index) => {
          const itemStartTime = Time.incrementDateBy(
            viewDate.start,
            'month',
            index
          );
          const monthLabel = Time.getMonthLabel(itemStartTime);
          const localViewDate = Time.getViewDateDetails(itemStartTime);
          return (
            <Flex key={`monthCalendar-${index}`}>
              <Typography
                color="primaryText"
                font="500"
                size={20}
                className={classes.monthLabel}
                onClick={handleMonthLabelClick(month)}
              >
                {t(monthLabel)}
              </Typography>
              <DatepickerV3
                hideHeader
                hideToday
                data={data}
                localviewDate={localViewDate}
                doNotShowActivesOfAdjacentMonths
                hideWeekDayLabels
                onCellClick={handleCellClick}
                showEventsOnHover
                numberOfCells={7 * 6}
              />
            </Flex>
          );
        })
      ) : typeof document === 'undefined' ? null : (
        <ViewPortList
          ref={scrollerRef}
          data={yearArray}
          itemContent={itemContent}
          style={{ width: '100%' }}
          firstItemIndex={firstItemIndex}
          startReached={prependItems}
          endReached={appendItems}
          itemsRendered={handleViewItemsChange}
        />
      )}
    </Flex>
  );
};

export default SchedulesEventBoardYear;
