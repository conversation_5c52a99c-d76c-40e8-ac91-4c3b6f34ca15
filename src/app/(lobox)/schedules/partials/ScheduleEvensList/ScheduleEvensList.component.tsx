import React, { useId } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import Skeleton from 'shared/uikit/Skeleton';
import ScheduleEventCard from 'shared/components/molecules/ScheduleEventCard';
import type { CalendarEvent } from 'shared/types/schedules/schedules';

import classes from './ScheduleEvensList.component.module.scss';

interface JobListProps {
  data: CalendarEvent[];
  isLoading?: boolean;
  action?: (event: CalendarEvent) => React.ReactElement;
  itemClassName?: string;
}

const skeletons = Array.from({ length: 6 }, (_, i) => i);

const ScheduleEvensList: React.FC<JobListProps> = ({
  data,
  isLoading,
  action,
  itemClassName,
}) => {
  const id = useId();

  // const { setScheduleEventsPanelData: setScheduleCreationModalData } =
  //   useSchedulesUrlState();

  // const openDetailModal = useCallback(
  //   (event: CalendarEvent) => (e: MouseEvent<HTMLElement>) => {
  //     preventClickHandler(e);
  //     setScheduleCreationModalData({
  //       isInCrEdit: false,
  //       schedulesEventType: event.type,
  //       eventId: event.id,
  //     });
  //   },
  //   [setScheduleCreationModalData]
  // );

  if (isLoading) {
    return (
      <Flex className={classes.list}>
        {skeletons?.map((item: number) => (
          <Flex className={classes.item} key={`_${item}_${id}`}>
            <Skeleton className={classes.skeleton} />
          </Flex>
        ))}
      </Flex>
    );
  }

  return (
    <Flex className={classes.list}>
      {data?.map((event: CalendarEvent) => (
        <ScheduleEventCard
          key={event.id}
          className={cnj(classes.item, itemClassName)}
          clickAction="details"
          action={action}
          event={event}
          noUserPopper
        />
      ))}
    </Flex>
  );
};

export default ScheduleEvensList;
