import React from 'react';
import ScheduleEvensList from '@app/schedules/partials/ScheduleEvensList';
import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import Tooltip from 'shared/uikit/Tooltip';
import Typography from 'shared/uikit/Typography';
import useMedia from 'shared/uikit/utils/useMedia';
import { useGlobalState } from 'shared/contexts/Global/global.provider';

import type { CalendarEvent } from 'shared/types/schedules/schedules';
import classes from './ScheduleEventsSectionLayout.component.module.scss';

interface JobsSectionLayoutProps {
  title: string;
  isLoading: boolean;
  data: Array<CalendarEvent>;
  emptyComponent?: (() => React.ReactElement) | React.ReactElement;
  action?: (event: CalendarEvent) => React.ReactElement;
  tooltipText?: string;
  SeeAll?: React.ReactElement | false;
  titleClassName?: string;
  childrenWrapClassName?: string;
}

const ScheduleEventsSectionLayout = ({
  title,
  isLoading,
  data,
  emptyComponent,
  action,
  tooltipText,
  SeeAll,
  titleClassName,
  childrenWrapClassName,
}: JobsSectionLayoutProps) => {
  const isEmpty = !data?.length && !isLoading;
  const { isMidDesktop } = useMedia();
  const isOpenRightPanel = useGlobalState('isOpenRightPanel');

  if (isEmpty && !emptyComponent) {
    return null;
  }

  return (
    <Flex className={classes.scheduleEventsSectionLayoutRoot}>
      <SectionLayout
        classNames={{
          title: cnj(classes.title, titleClassName),
          childrenWrap: cnj(classes.childrenWrap, childrenWrapClassName),
        }}
        title={
          <Flex flexDir="row">
            <Typography font="700" size={20} height={24} mr={4}>
              {title}
            </Typography>
            {tooltipText && (
              <Tooltip
                placement="top"
                trigger={
                  <IconButton
                    colorSchema="secondary-transparent"
                    size="sm"
                    type="far"
                    name="info-circle"
                  />
                }
              >
                <Typography
                  size={13}
                  font="400"
                  height={17}
                  color="tooltipText"
                >
                  {tooltipText}
                </Typography>
              </Tooltip>
            )}
          </Flex>
        }
      >
        {isEmpty ? (
          typeof emptyComponent === 'function' ? (
            emptyComponent()
          ) : (
            emptyComponent
          )
        ) : (
          <>
            <ScheduleEvensList
              isLoading={isLoading}
              data={data}
              action={action}
              itemClassName={
                isOpenRightPanel && isMidDesktop ? classes.itemClassName : ''
              }
            />
            {SeeAll}
          </>
        )}
      </SectionLayout>
    </Flex>
  );
};

export default ScheduleEventsSectionLayout;
