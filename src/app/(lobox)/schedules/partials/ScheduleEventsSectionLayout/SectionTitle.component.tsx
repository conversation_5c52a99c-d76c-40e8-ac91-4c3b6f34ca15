import React from 'react';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import Tooltip from 'shared/uikit/Tooltip';
import Typography from 'shared/uikit/Typography';
import classes from './SectionTitle.component.module.scss';

export interface SectionTitleProps {
  boldTitle: string;
  regularTitle: string;
  tooltipText?: string;
}

const SectionTitle: React.FC<SectionTitleProps> = ({
  boldTitle,
  regularTitle,
  tooltipText,
}) => (
  <Flex flexDir="row">
    <Typography font="700" color="thirdText" className={classes.inlineText}>
      {boldTitle}
    </Typography>
    <Typography
      font="500"
      color="borderSeventh"
      className={classes.inlineText}
      ml={4}
      mr={4}
    >
      {regularTitle}
    </Typography>
    {tooltipText && (
      <Tooltip
        placement="top"
        trigger={
          <IconButton
            colorSchema="secondary-transparent"
            size="sm"
            type="far"
            name="info-circle"
          />
        }
      >
        <Typography size={13} font="400" height={17} color="tooltipText">
          {tooltipText}
        </Typography>
      </Tooltip>
    )}
  </Flex>
);

export default SectionTitle;
