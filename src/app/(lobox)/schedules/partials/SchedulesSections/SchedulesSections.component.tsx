'use client';

import React, { useRef, useState } from 'react';
import Flex from 'shared/uikit/Flex';
import schedulesApi from 'shared/utils/api/schedules';
import { schedulesEventTypes } from 'shared/utils/constants/enums';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import ScheduleEventsSectionLayout from '@app/schedules/partials/ScheduleEventsSectionLayout';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import { useUpcomingAndPastEvents } from 'shared/hooks/useUpcomingAndPastEvents';
import getSchedulesSectionsQueryKey from 'shared/utils/getSchedulesSectionsQueryKey';
import { SeeAllButtonDivider } from 'shared/components/molecules/SeeAllButtonDivider';
import type {
  CalendarEvent,
  CreatableSchedulesEventTypes,
} from '@shared/types/schedules/schedules';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import classes from './SchedulesSections.component.module.scss';

interface SchedulesSectionsProps {
  schedulesEventType: CreatableSchedulesEventTypes;
  firstSectionProps: {
    title: string;
    action?: (event: CalendarEvent) => React.ReactElement;
    emptyComponent: {
      caption: string;
      message: string;
      action?: {
        title: string;
      };
    };
  };
  secondSectionProps: {
    title: string;
    tooltipText?: string;
    action?: (event: CalendarEvent) => React.ReactElement;
    emptyComponent?: {
      caption: string;
      message: string;
      action?: {
        title: string;
      };
    };
  };
  emptyState: {
    caption: string;
    message: string;
    action?: {
      title: string;
    };
  };
}
const DEFAULT_NUMBER_OF_ITEMS = 6;

const SchedulesSections = ({
  schedulesEventType,
  firstSectionProps,
  secondSectionProps,
  emptyState,
}: SchedulesSectionsProps) => {
  const { openCreateEventWithDate } = useSchedulesCalendar();
  const [isShowingAll, setIsShowingAll] = useState<{
    upcoming: boolean;
    past: boolean;
  }>({ upcoming: false, past: false });
  const { events: dataUpcoming, isLoading: isLoadingUpcoming } =
    useUpcomingAndPastEvents('upcoming', (items) =>
      items.filter(
        (item) => item.type && schedulesEventType.includes(item.type)
      )
    );
  const { events: dataPast, isLoading: isLoadingPast } =
    useUpcomingAndPastEvents('past', (items) =>
      items.filter(
        (item) => item.type && schedulesEventType.includes(item.type)
      )
    );

  const isEvent = [
    schedulesEventTypes.HOLIDAY,
    schedulesEventTypes.BIRTHDAY,
  ].includes(schedulesEventType);

  const openCreateEvent = () => {
    openCreateEventWithDate(undefined, {
      schedulesEventType,
    });
  };

  const { pastQueryKey, upComingQueryKey } =
    getSchedulesSectionsQueryKey(schedulesEventType);
  const pages = useRef({ upcoming: 0, past: 0 });

  const {
    data: upComingNonEvent,
    isLoading: upComingLoading,
    fetchNextPage: fetchNextPageUpcoming,
    hasNextPage: upcomingHasNextPage,
  } = useInfiniteQuery<CalendarEvent>(upComingQueryKey, {
    func: schedulesApi.getCalendarUpcoming,
    size: DEFAULT_NUMBER_OF_ITEMS,
    extraProps: {
      types: schedulesEventType,
      enabled: !isEvent,
    },
  });

  const {
    data: pastNonEvent,
    isLoading: pastLoading,
    fetchNextPage: fetchNextPagePast,
    hasNextPage: pastHasNextPage,
  } = useInfiniteQuery<CalendarEvent>(pastQueryKey, {
    func: schedulesApi.getCalendarPast,
    size: DEFAULT_NUMBER_OF_ITEMS,
    extraProps: {
      types: schedulesEventType,
      enabled: !isEvent,
    },
  });

  const handleNextPageUpcoming = () => {
    pages.current.upcoming += 1;
    fetchNextPageUpcoming({ pageParam: pages.current.upcoming });
  };
  const handleNextPagePast = () => {
    pages.current.past += 1;
    fetchNextPagePast({ pageParam: pages.current.past });
  };

  const isLoadingEvent = isLoadingUpcoming || isLoadingPast;
  const isLoadingNonEvent = pastLoading || upComingLoading;
  const isLoading = isEvent ? isLoadingEvent : isLoadingNonEvent;

  const isEmptyEvent = !isLoading && !dataUpcoming?.length && !dataPast?.length;
  const isEmptyNonEvent =
    !isLoading && !upComingNonEvent?.length && !pastNonEvent?.length;
  const isEmpty = isEvent ? isEmptyEvent : isEmptyNonEvent;

  const dUpcoming = isEvent ? dataUpcoming : upComingNonEvent;
  const dPast = isEvent ? dataPast : pastNonEvent;
  const filteredUpcomingData =
    !isEvent || isShowingAll.upcoming
      ? dUpcoming
      : dUpcoming?.slice(0, DEFAULT_NUMBER_OF_ITEMS);
  const filteredPastData =
    !isEvent || isShowingAll.past
      ? dPast
      : dPast?.slice(0, DEFAULT_NUMBER_OF_ITEMS);

  const isShowingSeeAllButtonUpcoming = isEvent
    ? dUpcoming?.length > DEFAULT_NUMBER_OF_ITEMS && !isShowingAll.upcoming
    : upcomingHasNextPage;
  const isShowingSeeAllButtonPast = isEvent
    ? dPast?.length > DEFAULT_NUMBER_OF_ITEMS && !isShowingAll.past
    : pastHasNextPage;

  const handleSeeMore = (type: 'past' | 'upcoming') => () => {
    if (isEvent) {
      setIsShowingAll((prev) => ({ ...prev, [type]: true }));
    } else if (type === 'past') {
      handleNextPagePast();
    } else if (type === 'upcoming') {
      handleNextPageUpcoming();
    }
  };

  if (isEmpty) {
    return (
      <EmptySectionInModules
        isFullHeight
        isInsideTab
        title={emptyState.caption}
        text={emptyState.message}
        buttonProps={
          emptyState.action
            ? {
                title: emptyState.action?.title,
                onClick: openCreateEvent,
              }
            : undefined
        }
        className={classes.emptySectionWrapper}
        classNames={{ container: classes.emptySectionContainer }}
      />
    );
  }

  return (
    <Flex className={classes.container}>
      <ScheduleEventsSectionLayout
        title={firstSectionProps.title}
        data={filteredUpcomingData as any}
        isLoading={isLoading}
        action={firstSectionProps.action}
        emptyComponent={
          firstSectionProps?.emptyComponent ? (
            <EmptySectionInModules
              isInsideTab
              title={firstSectionProps.emptyComponent.caption}
              text={firstSectionProps.emptyComponent.message}
              buttonProps={
                firstSectionProps.emptyComponent.action
                  ? {
                      title: firstSectionProps.emptyComponent.action?.title,
                      onClick: openCreateEvent,
                    }
                  : undefined
              }
            />
          ) : undefined
        }
        SeeAll={
          isShowingSeeAllButtonUpcoming && (
            <SeeAllButtonDivider onClick={handleSeeMore('upcoming')} />
          )
        }
        titleClassName={classes.sectionTitle}
      />
      <ScheduleEventsSectionLayout
        title={secondSectionProps.title}
        action={secondSectionProps.action}
        tooltipText={secondSectionProps.tooltipText}
        data={filteredPastData as any}
        isLoading={isLoading}
        SeeAll={
          isShowingSeeAllButtonPast && (
            <SeeAllButtonDivider onClick={handleSeeMore('past')} />
          )
        }
        titleClassName={classes.sectionTitle}
        emptyComponent={
          secondSectionProps?.emptyComponent ? (
            <EmptySectionInModules
              isInsideTab
              title={secondSectionProps?.emptyComponent?.caption}
              text={secondSectionProps?.emptyComponent?.message}
              buttonProps={
                secondSectionProps?.emptyComponent?.action
                  ? {
                      title: secondSectionProps?.emptyComponent?.action?.title,
                      onClick: openCreateEvent,
                    }
                  : undefined
              }
            />
          ) : undefined
        }
      />
    </Flex>
  );
};

export default SchedulesSections;
