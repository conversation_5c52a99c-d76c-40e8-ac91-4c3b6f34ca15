import { EventsIntegration } from 'shared/components/molecules/EventsIntegration/EventsIntegration.component';
import { PresenceAnimationWrapper } from 'shared/components/molecules/PresenceAnimationWrapper';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import FixedRightSideModal from 'shared/uikit/Modal/FixedRightSideModalDialog';
import useHistory from 'shared/utils/hooks/useHistory';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type {
  EventsIntegrationPanelProps,
  ProviderType,
} from 'shared/components/molecules/EventsIntegration/utils/type';
import { isBusinessApp } from '@shared/utils/getAppEnv';

const IntegrationsPanel = <PT extends ProviderType>({
  providerType,
  handleClose,
}: EventsIntegrationPanelProps<PT>) => {
  const history = useHistory();
  const onBack = handleClose ? handleClose : () => history.back();
  const { t } = useTranslation();
  return (
    <PresenceAnimationWrapper isOpen>
      <FixedRightSideModal
        onBack={onBack}
        onClose={onBack}
        onClickOutside={onBack}
        wide={isBusinessApp}
      >
        <ModalHeaderSimple
          noCloseButton
          title={t('integrations')}
          hideBack={false}
        />
        <ModalBody>
          <EventsIntegration providerType={providerType} />
        </ModalBody>
      </FixedRightSideModal>
    </PresenceAnimationWrapper>
  );
};

export default IntegrationsPanel;
