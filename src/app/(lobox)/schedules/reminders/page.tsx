'use client';

import React from 'react';
import Button from 'shared/uikit/Button';
import { schedulesEventTypes } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SchedulesSections from '@app/schedules/partials/SchedulesSections';
import { detailsLabels } from 'shared/constants/schedules';

const SchedulesReminders = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <SchedulesSections
      schedulesEventType={schedulesEventTypes.REMINDER}
      firstSectionProps={{
        title: t('up_reminders'),
        action: () => (
          <Button
            schema="semi-transparent"
            fullWidth
            label={t(detailsLabels[schedulesEventTypes.REMINDER])}
          />
        ),
        emptyComponent: {
          caption: t('no_scheduled_reminder'),
          message: t('y_dnt_a_reminders_at_m'),
        },
      }}
      secondSectionProps={{
        title: t('past_reminders'),
        tooltipText: t('w_r_go_com_reminders'),
        action: () => (
          <Button
            schema="semi-transparent"
            fullWidth
            label={t(detailsLabels[schedulesEventTypes.REMINDER])}
          />
        ),
        emptyComponent: {
          caption: t('no_scheduled_reminder'),
          message: t('y_dnt_a_reminders_at_m'),
        },
      }}
      emptyState={{
        caption: t('no_reminders_set'),
        message: t('y_dnt_h_a_reminder'),
        action: { title: t('create_reminder') },
      }}
    />
  );
};

export default SchedulesReminders;
