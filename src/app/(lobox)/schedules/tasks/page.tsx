'use client';

import React from 'react';
import Button from 'shared/uikit/Button';
import { schedulesEventTypes } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { detailsLabels } from 'shared/constants/schedules';
import SchedulesSections from '../partials/SchedulesSections';

const SchedulesTasks = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <SchedulesSections
      schedulesEventType={schedulesEventTypes.TASK}
      firstSectionProps={{
        title: t('open_tasks'),
        action: () => (
          <Button
            schema="semi-transparent"
            fullWidth
            label={t(detailsLabels[schedulesEventTypes.TASK])}
          />
        ),
        emptyComponent: {
          caption: t('no_open_tasks'),
          message: t('y_dnt_h_a_open_tsks'),
          action: {
            title: t('create_task'),
          },
        },
      }}
      secondSectionProps={{
        title: t('past_tasks'),
        tooltipText: t('w_r_go_com_tasks'),
        action: () => (
          <Button
            schema="semi-transparent"
            fullWidth
            label={t(detailsLabels[schedulesEventTypes.TASK])}
          />
        ),
        emptyComponent: {
          caption: t('no_open_tasks'),
          message: t('y_dnt_h_a_open_tsks'),
        },
      }}
      emptyState={{
        caption: t('no_tasks'),
        message: t('y_dnt_h_any_task_assi'),
        action: { title: t('create_task') },
      }}
    />
  );
};

export default SchedulesTasks;
