'use client';

import React from 'react';
import Flex from 'shared/uikit/Flex/Flex.component';
import Skeleton from 'shared/uikit/Skeleton';
import SearchResultSkeleton from '@app/search/partials/SearchResultSkeleton';
import classes from './page.module.scss';

const list = Array(5).fill(0);

export default function loading() {
  return (
    <>
      <Flex className={classes.linksRoot}>
        <Flex className={classes.linkAndActionWrapper}>
          <Flex className={classes.buttonsWrapper}>
            {list.map((_, i) => (
              <Skeleton key={`path_${i}`} className={classes.skeleton} />
            ))}
          </Flex>
        </Flex>
      </Flex>
      <Flex className={classes.contentRoot}>
        <SearchResultSkeleton />
      </Flex>
    </>
  );
}
