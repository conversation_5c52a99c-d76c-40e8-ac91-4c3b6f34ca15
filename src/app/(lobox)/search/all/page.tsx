'use client';

import sortBy from 'lodash/sortBy';
import { useSearchParams } from 'next/navigation';
import React, { Fragment, useMemo } from 'react';
import EmptySearchSvg from '@shared/svg/EmptySearchSvg';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import cnj from '@shared/uikit/utils/cnj';
import PageCard from 'shared/components/Organism/PageCard';
import PeopleCard from 'shared/components/Organism/PeopleCard';
import SearchAllSection from 'shared/components/Organism/SearchAllSection';
import {
  searchGroupTypes,
  searchFilterQueryParams,
} from 'shared/constants/search';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import Flex from 'shared/uikit/Flex';
import NavLink from 'shared/uikit/Tabs/NavLink';
import Skeleton from 'shared/uikit/Skeleton';
import jobsApi from 'shared/utils/api/jobs';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { routeNames } from 'shared/utils/constants/routeNames';
import {
  searchPerson,
  searchPages,
  searchPosts,
} from 'shared/utils/api/search';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useHasPermission from 'shared/hooks/useHasPermission';
import useNavigateSearchPage from 'shared/hooks/useNavigateSearchPage';
import SearchPosts from 'shared/components/Organism/SearchAllSection/partials/Posts';
import SearchJobs from 'shared/components/Organism/SearchAllSection/partials/Jobs';
import isRouterAccessibleByPortal from 'shared/utils/isRouterAccessibleByPortal';
import SearchResultSkeleton from '../partials/SearchResultSkeleton';
import classes from './page.module.scss';
import type {
  ISearchPage,
  ISearchPeople,
  ISearchPost,
} from '@shared/types/search';
import type { IJob } from 'shared/types/job';

const INIT_NUMBER_OF_ITEMS = 6;

const SearchAll = (): JSX.Element => {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const searchedQuery = decodeURIComponent(searchParams.get('query') || '');
  const search = useMemo(
    () =>
      searchedQuery
        ? new URLSearchParams({
            [searchFilterQueryParams.query]: searchedQuery,
          }).toString()
        : '',
    [searchedQuery]
  );
  const addSearchToRout = (route: string) => `${route}?${search}`;

  const navigateSearchPage = useNavigateSearchPage();
  const searchQuery = decodeURIComponent(searchParams.get('query') || '');

  const canSeeSearchJobs =
    useHasPermission([SCOPES.canSeeSearchJobs]) &&
    isRouterAccessibleByPortal(routeNames.searchJobs);
  const canSeeSearchPages = useHasPermission([SCOPES.canSeeSearchPages]);
  const canSeeSearchPeople = useHasPermission([SCOPES.canSeeSearchPeople]);
  const canSeeSearchPost = useHasPermission([SCOPES.canSeeSearchPost]);

  const peopleQueryKey = [
    QueryKeys.searchUsers,
    searchQuery,
    `${INIT_NUMBER_OF_ITEMS}`,
  ];
  const pagesQueryKey = [
    QueryKeys.searchPages,
    searchQuery,
    `${INIT_NUMBER_OF_ITEMS}`,
  ];

  const {
    data: peopleData,
    isLoading: peopleIsLoading,
    totalElements: totalPeople,
  } = useInfiniteQuery<ISearchPeople>(
    peopleQueryKey,
    {
      func: searchPerson,
      size: INIT_NUMBER_OF_ITEMS,
      extraProps: {
        text: searchQuery,
      },
    },
    {
      enabled: typeof searchQuery === 'string',
    }
  );

  const {
    data: pagesData,
    isLoading: pageIsLoading,
    totalElements: totalPages,
  } = useInfiniteQuery<ISearchPage>(
    pagesQueryKey,
    {
      func: searchPages,
      size: INIT_NUMBER_OF_ITEMS,
      extraProps: {
        text: searchQuery,
      },
    },
    {
      enabled: typeof searchQuery === 'string',
    }
  );

  const postQueryKey = [
    QueryKeys.searchPosts,
    searchQuery,
    `${INIT_NUMBER_OF_ITEMS}`,
  ];

  const {
    data: postData,
    isLoading: postIsLoading,
    totalElements: totalPosts,
  } = useInfiniteQuery<ISearchPost>(
    postQueryKey,
    {
      func: searchPosts,
      size: INIT_NUMBER_OF_ITEMS,
      extraProps: {
        text: searchQuery,
        sortBy: 'MOST_RECENT',
      },
    },
    {
      enabled: typeof searchQuery === 'string',
    }
  );

  const jobsQueryKey = [
    QueryKeys.searchJobs,
    searchQuery,
    `${INIT_NUMBER_OF_ITEMS}`,
  ];

  const {
    data: jobsData,
    isLoading: jobsIsLoading,
    totalElements: totalJobs,
  } = useInfiniteQuery<IJob>(
    jobsQueryKey,
    {
      func: () =>
        jobsApi.searchJob({
          params: {
            query: searchQuery,
            text: searchQuery,
            size: INIT_NUMBER_OF_ITEMS,
            [searchFilterQueryParams.scope]: searchGroupTypes.ALL,
          },
        }),
    },
    {
      enabled: typeof searchQuery === 'string',
    }
  );
  const onSelectHandler = (item: any, routeName: string) => {
    navigateSearchPage({
      currentEntityId: item.id,
      query: searchQuery,
      pathname: routeName,
    });
  };
  const items: {
    component: JSX.Element;
    isVisible: boolean;
    totalElements: number;
    key: string;
  }[] = useMemo(
    () =>
      sortBy(
        [
          {
            key: 'pages',
            component: (
              <SearchAllSection
                renderItem={(item) => (
                  <PageCard
                    page={item}
                    queryKey={pagesQueryKey}
                    onSelectHandler={(selectedItem) =>
                      onSelectHandler(selectedItem, routeNames.searchPages)
                    }
                  />
                )}
                scopes={[SCOPES.canSeeSearchPages]}
                title={t('pages')}
                onSelectHandler={(item) =>
                  onSelectHandler(item, routeNames.searchPages)
                }
                showAllRouteName={`${routeNames.searchPages}?${search}`}
                routeName={(item) => `/${item.username}`}
                visibleShowAll={totalPages > INIT_NUMBER_OF_ITEMS}
                data={pagesData}
                isLoading={pageIsLoading}
                totalElements={totalPages}
              />
            ),
            isVisible: canSeeSearchPages,
            totalElements: totalPages,
          },
          {
            key: 'people',
            component: (
              <SearchAllSection
                title={t('people')}
                onSelectHandler={(item) =>
                  onSelectHandler(item, routeNames.searchPeople)
                }
                scopes={[SCOPES.canSeeSearchPeople]}
                showAllRouteName={`${routeNames.searchPeople}?${search}`}
                routeName={(item) => `/${item.username}`}
                visibleShowAll={totalPeople > INIT_NUMBER_OF_ITEMS}
                totalElements={totalPeople}
                isLoading={peopleIsLoading}
                data={peopleData}
                renderItem={(item) => (
                  <PeopleCard
                    people={item}
                    queryKey={peopleQueryKey}
                    onSelectHandler={(selectedItem) =>
                      onSelectHandler(selectedItem, routeNames.searchPeople)
                    }
                  />
                )}
              />
            ),
            isVisible: canSeeSearchPeople,
            totalElements: totalPeople,
          },
          {
            key: 'posts',
            component: (
              <SearchPosts
                onSelectHandler={(item) =>
                  onSelectHandler(item, routeNames.searchPosts)
                }
                showAllRouteName={`${routeNames.searchPosts}?${search}`}
                data={postData}
                isLoading={postIsLoading}
                totalElements={totalPosts}
                queryKey={postQueryKey}
              />
            ),
            isVisible: canSeeSearchPost,
            totalElements: totalPosts,
          },
          isRouterAccessibleByPortal(routeNames.searchJobs) && {
            key: 'jobs',
            component: (
              <SearchJobs
                showAllRouteName={`${routeNames.searchJobs}?${search}`}
                onSelectHandler={(selectedItem) =>
                  onSelectHandler(selectedItem, routeNames.searchJobs)
                }
                data={jobsData}
                isLoading={jobsIsLoading}
                totalElements={totalJobs}
                queryKey={jobsQueryKey}
              />
            ),
            isVisible: canSeeSearchJobs,
            totalElements: totalJobs,
          },
        ].filter((item) => !!item),
        'totalElements'
      ).reverse(),
    [
      jobsData,
      peopleData,
      pagesData,
      totalPeople,
      peopleIsLoading,
      totalPages,
      pageIsLoading,
      postIsLoading,
      totalPosts,
      jobsIsLoading,
      totalJobs,
    ]
  );
  const isLoading =
    peopleIsLoading || pageIsLoading || jobsIsLoading || postIsLoading;

  const orders: Record<string, number> = items?.reduce(
    (acc, item, index) => ({ ...acc, [item?.key]: index + 1 }),
    { all: 0 }
  );
  const isEmpty =
    (!totalPeople || !canSeeSearchPeople) &&
    (!totalPages || !canSeeSearchPages) &&
    (!totalJobs || !canSeeSearchJobs) &&
    (!totalPosts || !canSeeSearchPost);

  const searchTabList: Array<{ path: string; title: string; key: string }> = [
    {
      path: addSearchToRout(routeNames.searchAll),
      title: t('all'),
      key: 'all',
    },
    canSeeSearchPost && {
      path: addSearchToRout(routeNames.searchPosts),
      title: t('posts'),
      key: 'posts',
    },
    canSeeSearchPeople && {
      path: addSearchToRout(routeNames.searchPeople),
      title: t('people'),
      key: 'people',
    },
    canSeeSearchPages && {
      path: addSearchToRout(routeNames.searchPages),
      title: t('pages'),
      key: 'pages',
    },
    canSeeSearchJobs && {
      path: addSearchToRout(routeNames.searchJobs),
      title: t('jobs'),
      key: 'jobs',
    },
  ]
    .filter((item) => !!item)
    .sort((a, b) => (orders?.[a.key] < orders?.[b.key] ? -1 : 1));

  return (
    <Flex className={classes.searchAllRoot}>
      <Flex className={classes.linksRoot}>
        <Flex className={classes.linkAndActionWrapper}>
          <Flex className={classes.buttonsWrapper}>
            {searchTabList.map(({ key, title, path }, index) =>
              isLoading ? (
                <Skeleton key={path} className={classes.skeleton} />
              ) : (
                <NavLink
                  className={cnj(
                    classes.navlink,
                    key === 'all' && classes.allClassName
                  )}
                  key={path}
                  title={title}
                  path={path}
                  isBadge
                  isFirstLink={index === 0}
                />
              )
            )}
          </Flex>
        </Flex>
      </Flex>
      <Flex className={classes.contentRoot}>
        {isLoading ? (
          <SearchResultSkeleton />
        ) : isEmpty ? (
          <EmptySectionInModules
            title={t('no_result_f')}
            text={t(`no_all_f_wi_entry`)}
            image={<EmptySearchSvg />}
            classNames={{ container: classes.container }}
            isInsideTab
            isFullWidth
            isFullHeight
          />
        ) : (
          items?.map((item) => (
            <Fragment key={item.key}>
              {item.isVisible && item.component}
            </Fragment>
          ))
        )}
      </Flex>
    </Flex>
  );
};

export default SearchAll;
