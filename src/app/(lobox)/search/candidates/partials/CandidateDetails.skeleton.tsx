import CandidateCardSkeleton from '@shared/components/molecules/CandidateCard/CandidateCardSkeleton';
import Skeleton from '@shared/uikit/Skeleton';
import cnj from '@shared/uikit/utils/cnj';
import { type FC } from 'react';
import Flex from '@shared/uikit/Flex/index';
import classes from './CandidateDetails.module.scss';
import CandidateAboutSkeleton from './tab1.CandidateAbout/CandidateAbout.skeleton';

const CandidateDetailsSkeleton: FC = () => (
  <>
    <CandidateCardSkeleton
      showActions
      showBadges
      showTags
      footer={
        <Flex
          className={cnj(classes.tabsRoot, '!flex-row gap-12 py-[15px] mx-5')}
        >
          <Skeleton className="w-50 flex-1 h-16 rounded" />
          <Skeleton className="w-50 flex-1 h-16 rounded" />
          <Skeleton className="w-50 flex-1 h-16 rounded" />
          <Skeleton className="w-50 flex-1 h-16 rounded" />
          <Skeleton className="w-50 flex-1 h-16 rounded" />
          <Skeleton className="w-50 flex-1 h-16 rounded" />
        </Flex>
      }
    >
      <Flex className="!flex-row gap-12">
        <Skeleton className="w-50 flex-1 h-32 rounded" />
        <Skeleton className="w-50 flex-1 h-32 rounded" />
      </Flex>
    </CandidateCardSkeleton>
    <CandidateAboutSkeleton items={6} />
    <CandidateAboutSkeleton items={2} />
    <CandidateAboutSkeleton items={4} />
  </>
);

export default CandidateDetailsSkeleton;
