import InfoCard from '@shared/components/Organism/Objects/Common/InfoCard';
import Flex from '@shared/uikit/Flex/index';
import type { ComponentProps, FC } from 'react';
import classes from './InfoCardList.module.scss';

export type InfoCardListItem = ComponentProps<typeof InfoCard> & {
  key: string;
};

interface InfoCardListProps {
  items: InfoCardListItem[];
}

const fixedProps: ComponentProps<typeof InfoCard> = {
  iconSize: 20,
  valueProps: {
    color: 'smoke_coal',
  },
  wrapperClassName: '!p-0',
  iconClassName: classes.infoCardIcon,
};

const InfoCardList: FC<InfoCardListProps> = ({ items }) => (
  <Flex className={classes.infoCardList}>
    {items.map(({ key, ...item }, index) =>
      item.value ? (
        <InfoCard key={key} {...fixedProps} {...item} disabledHover />
      ) : null
    )}
  </Flex>
);

export default InfoCardList;
