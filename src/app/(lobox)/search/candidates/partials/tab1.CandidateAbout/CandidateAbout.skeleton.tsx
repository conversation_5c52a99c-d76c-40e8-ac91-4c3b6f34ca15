import { useMemo, type FC } from 'react';
import Skeleton from '@shared/uikit/Skeleton';
import Flex from '@shared/uikit/Flex/index';
import cnj from '@shared/uikit/utils/cnj';
import classes from './CandidateAbout.module.scss';

const CandidateAboutSkeleton: FC<{ items?: number; content?: true }> = ({
  items = 2,
  content = false,
}) => {
  const inner = useMemo(
    () =>
      new Array(items).fill(0).map((_, i) => (
        <Flex key={i} className={classes.skeletonWrapper}>
          <Flex className={classes.skeletonItem}>
            <Skeleton className={cnj(classes.skeletonIcon, 'rounded')} />
            <Flex className="gap-4 flex-1">
              <Skeleton className="h-16 rounded !w-[100px]" />
              <Skeleton className="h-16 rounded !w-[150px]" />
              <Skeleton className="h-16 rounded !w-[200px] mb-3" />
              <Skeleton className="h-16 rounded w-full" />
              <Skeleton className="h-16 rounded w-full" />
              <Skeleton className="h-16 rounded w-full" />
            </Flex>
          </Flex>
          <Flex className="gap-4 flex-1" />
        </Flex>
      )),
    [items]
  );
  return (
    <>
      <Flex className={classes.skeleton}>
        <Skeleton className="h-[16px] !w-[100px] rounded mt-[40px] mb-[28px]" />
        <Flex className={classes.skeletonContent}>
          <Skeleton className="!h-[72px] rounded w-full" />
        </Flex>
      </Flex>
      <Flex className={classes.skeleton}>
        <Skeleton className="h-[16px] !w-[100px] rounded mt-[40px] mb-[28px]" />
        <Flex className={classes.skeletonContent}>{inner}</Flex>
      </Flex>
    </>
  );
};

export default CandidateAboutSkeleton;
