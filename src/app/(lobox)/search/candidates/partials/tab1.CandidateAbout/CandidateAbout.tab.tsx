import { type FC } from 'react';
import { useSearchParams } from 'next/navigation';
import { TextHighlightWrapper } from '@shared/components/molecules/TextHighlight/TextHighlight';
import CandidateBioSection from './CandidateBio.section';
import CandidateAdditionalNoteSection from './CandidateAdditionalNote.section';
import CandidateBackgroundSection from './CandidateBackground.section';
import CandidateContactsSection from './CandidateContacts.section';
import CandidateCreatedBySection from './CandidateCreatedBy.section';
import CandidateEducationsSection from './CandidateEducations.section';
import CandidateLanguagesSection from './CandidateLanguages.section';
import CandidateLegalSection from './CandidateLegal.section';
import CandidateReferralSection from './CandidateReferral.section';
import CandidateSkillsSection from './CandidateSkills.section';
import CandidateSocialSection from './CandidateSocial.section';
import CandidateExperiencesSection from './CandidateExperiences.section';
import CandidateExpectationsSection from './CandidateExpectations.section';
import CandidateIdSection from './CandidateId.section';
import {BaseCandidateSectionProp} from "@shared/types/candidates";

export const CandidateAboutTab: FC<BaseCandidateSectionProp> = (props) => {
  const searchParams = useSearchParams();
  const searchTerm = searchParams.get('query') ?? '';

  return (
    <TextHighlightWrapper searchTerm={searchTerm}>
      <CandidateBioSection {...props} />
      <CandidateExperiencesSection {...props} />
      <CandidateEducationsSection {...props} />
      <CandidateSkillsSection {...props} />
      <CandidateLanguagesSection {...props} />
      <CandidateBackgroundSection {...props} />
      <CandidateLegalSection {...props} />
      <CandidateExpectationsSection {...props} />
      <CandidateContactsSection {...props} />
      <CandidateSocialSection {...props} />
      <CandidateReferralSection {...props} />
      <CandidateAdditionalNoteSection {...props} />
      <CandidateCreatedBySection {...props} />
      <CandidateIdSection {...props} />
    </TextHighlightWrapper>
  );
};
