import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import { useObjectClicks } from '@shared/hooks/useObjectClicks';
import { RichTextView } from '@shared/uikit/RichText';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FC } from 'react';

import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateAdditionalNoteSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();

  const { handleTagClick, handleHashtagClick, hoveredHashtag, onHashtagHover } =
    useObjectClicks();

  if (!candidate.note) return null;
  return (
    <SectionLayout title={t('additional_note')} visibleActionButton>
      <RichTextView
        html={candidate.note ?? ''}
        typographyProps={{
          size: 15,
          color: 'thirdText',
        }}
        showMore
        onMentionClick={handleTagClick}
        onHashtagClick={handleHashtagClick}
        onHashtagHover={onHashtagHover}
        hoveredHashtag={hoveredHashtag}
      />
    </SectionLayout>
  );
};

export default CandidateAdditionalNoteSection;
