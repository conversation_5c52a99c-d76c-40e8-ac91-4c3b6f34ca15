import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import { RichTextView } from '@shared/uikit/RichText';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useMemo, type FC } from 'react';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import type { InfoCardListItem } from '../components/InfoCardsList/InfoCardList';
import InfoCardList from '../components/InfoCardsList/InfoCardList';
import classes from './CandidateAbout.module.scss';
import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateBackgroundSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const fullAddress = useMemo(
    () =>
      candidate.fullAddress ? (
        <RichTextView
          className={classes.richTextView}
          html={candidate.fullAddress ?? ''}
          typographyProps={{
            size: 15,
            color: 'thirdText',
          }}
          showMore
        />
      ) : null,
    [candidate.fullAddress]
  );
  const items = useMemo<InfoCardListItem[]>(
    () => [
      {
        key: 'gender',
        icon: 'gender',
        title: t('gender'),
        value: t(candidate.gender?.label ?? ''),
      },
      {
        key: 'age-range',
        icon: 'age-range',
        title: t('age_range'),
        value: t(candidate.ageRange?.label ?? ''),
      },
      {
        key: 'ethnicity',
        icon: 'ethnicity',
        title: t('race_ethnicity'),
        value: t(candidate.race?.label ?? ''),
      },
      {
        key: 'user-folder',
        icon: 'user-folder',
        title: t('top_secret'),
        value: t(candidate.criminalRecord?.label ?? ''),
      },
      {
        key: 'medal',
        icon: 'medal',
        title: t('veteran_status'),
        value: t(candidate.veteranStatus?.label ?? ''),
      },
      {
        key: 'disability',
        icon: 'disability',
        title: t('disability'),
        value: t(candidate.disabilityStatus?.label ?? ''),
      },
      {
        key: 'location',
        icon: 'location',
        title: t('location'),
        value: cleanRepeatedWords(candidate.profile.location?.title ?? ''),
      },
      {
        key: 'full-address',
        icon: 'full-address',
        title: t('full_address'),
        value: fullAddress,
      },
    ],
    [candidate, fullAddress, t]
  );

  const visibleItems = useMemo(
    () => items.filter((item) => !!item.value),
    [items]
  );

  if (!visibleItems.length) return null;

  return (
    <SectionLayout title={t('background_information')} visibleActionButton>
      <InfoCardList items={visibleItems} />
    </SectionLayout>
  );
};

export default CandidateBackgroundSection;
