import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import { useObjectClicks } from '@shared/hooks/useObjectClicks';
import { RichTextView } from '@shared/uikit/RichText';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FC } from 'react';

import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateBioSection: FC<BaseCandidateSectionProp> = ({ candidate }) => {
  const { t } = useTranslation();

  const { handleTagClick, handleHashtagClick, hoveredHashtag, onHashtagHover } =
    useObjectClicks();

  if (!candidate.profile.bio) return null;
  return (
    <SectionLayout title={t('bio')}>
      <RichTextView
        html={candidate.profile.bio ?? ''}
        typographyProps={{
          size: 15,
          color: 'thirdText',
          font: '400',
          height: 22,
        }}
        showMore
        onMentionClick={handleTagClick}
        onHashtagClick={handleHashtagClick}
        onHashtagHover={onHashtagHover}
        hoveredHashtag={hoveredHashtag}
      />
    </SectionLayout>
  );
};

export default CandidateBioSection;
