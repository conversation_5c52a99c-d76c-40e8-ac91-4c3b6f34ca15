import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useMemo, type FC } from 'react';
import { RichTextView } from '@shared/uikit/RichText';
import type { InfoCardListItem } from '../components/InfoCardsList/InfoCardList';
import InfoCardList from '../components/InfoCardsList/InfoCardList';
import classes from './CandidateAbout.module.scss';
import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateContactsSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const fullAddress = useMemo(
    () =>
      candidate.fullAddress ? (
        <RichTextView
          className={classes.richTextView}
          html={candidate.fullAddress ?? ''}
          typographyProps={{
            size: 15,
            color: 'thirdText',
          }}
          showMore
        />
      ) : null,
    [candidate.fullAddress]
  );
  const items = useMemo<InfoCardListItem[]>(
    () => [
      {
        key: 'envelope',
        icon: 'envelope',
        title: t('email'),
        value: candidate.profile.email?.value,
      },
      {
        key: 'full-address',
        icon: 'full-address',
        title: t('full_address'),
        value: fullAddress,
      },
    ],
    [candidate, fullAddress, t]
  );

  const visibleItems = useMemo(
    () => items.filter((item) => !!item.value),
    [items]
  );

  if (!visibleItems.length) return null;

  return (
    <SectionLayout visibleActionButton title={t('contact')}>
      <InfoCardList items={visibleItems} />
    </SectionLayout>
  );
};

export default CandidateContactsSection;
