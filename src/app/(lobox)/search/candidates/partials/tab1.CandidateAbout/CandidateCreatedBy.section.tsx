import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FC } from 'react';
import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import ObjectInfoCard from '@shared/components/molecules/ObjectInfoCard';
import useGetObjectDetail from '@shared/utils/hooks/useGetObjectDetail';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import type { UserType } from '@shared/types/user';
import { getObjectDetail } from '@shared/utils/api/object';
import { QueryKeys } from '@shared/utils/constants';
import applyResolutionToImageSrc from '@shared/utils/toolkit/applyResolutionToImageSrc';
import Flex from '@shared/uikit/Flex/index';
import Button from '@shared/uikit/Button';
import SendMessageButton from '@shared/components/molecules/SendMessageButton';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import ObjectLink from '@shared/uikit/Link/ObjectLink';
import classes from './CandidateAbout.module.scss';
import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateCreatedBySection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();

  const { data: createdByUser } = useReactQuery<UserType>({
    action: {
      key: [QueryKeys.objectDetail, candidate.creatorUserId],
      apiFunc: () =>
        getObjectDetail({
          params: { objectId: candidate.creatorUserId },
        }),
    },
    config: {
      enabled: !!candidate.creatorUserId,
    },
  });

  const { croppedImageUrl, location, usernameAtSign, fullName, occupation } =
    createdByUser ?? {};

  const image = croppedImageUrl
    ? applyResolutionToImageSrc(croppedImageUrl, 'small')
    : undefined;

  return createdByUser ? (
    <SectionLayout title={t('created_by')}>
      <Flex className={classes.createdByWrapper}>
        <ObjectInfoCard
          withAvatar
          className={classes.createdByObject}
          avatar={image}
          avatarProps={{
            name: fullName,
            isCompany: false,
            size: 'flg',
          }}
          firstText={fullName}
          secondText={usernameAtSign}
          thirdText={occupation?.label}
          fourthText={cleanRepeatedWords(location?.title || '')}
          isFirstTextSmall
        />
        <Flex className="!flex-row gap-12">
          <SendMessageButton
            className="flex-1"
            object={createdByUser}
            fullWidth
          />
          <ObjectLink
            className="flex-1"
            username={createdByUser.username}
            objectId={createdByUser.id}
          >
            <Button label={t('view_details')} fullWidth />
          </ObjectLink>
        </Flex>
      </Flex>
    </SectionLayout>
  ) : null;
};

export default CandidateCreatedBySection;
