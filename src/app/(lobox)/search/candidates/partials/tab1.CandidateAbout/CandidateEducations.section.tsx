import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import AdvancedCardList from '@shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { candidateEducationNormalizer } from '@shared/utils/normalizers/beforeCacheCandidateInfo';
import { useMemo, type FC } from 'react';

import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateEducationsSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();

  const educations = useMemo(
    () => candidateEducationNormalizer(candidate?._educations ?? [], t),
    [t, candidate?._educations]
  );

  if (!educations.length) return null;

  return (
    <AboutSectionLayout data={educations} title={t('educations')}>
      {(props) => <AdvancedCardList {...props} />}
    </AboutSectionLayout>
  );
};

export default CandidateEducationsSection;
