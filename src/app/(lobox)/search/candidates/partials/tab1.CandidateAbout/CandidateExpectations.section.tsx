import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import useTranslation from '@shared/utils/hooks/useTranslation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { useMemo, type FC } from 'react';
import { formatCurrency } from '@shared/utils/toolkit/formatCurrency';
import type { InfoCardListItem } from '../components/InfoCardsList/InfoCardList';
import InfoCardList from '../components/InfoCardsList/InfoCardList';

import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateExpectationsSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();

  const items = useMemo<InfoCardListItem[]>(() => {
    const min = candidate.expectedMinimumSalary;
    const max = candidate.expectedMaximumSalary;
    const code = candidate.expectedCurrency?.code;
    return [
      {
        key: 'job-model',
        icon: 'job-model',
        title: t('workplace_preference'),
        value: t(candidate.preferredWorkPlaceType?.label ?? ''),
      },
      {
        key: 'job',
        icon: 'job',
        title: t('job_type_preference'),
        value: candidate.preferredJob?.label ?? '',
      },
      {
        key: 'signal-bar',
        icon: 'signal-bar',
        title: t('exp_level'),
        value: t(candidate.preferredExperienceLevel?.label ?? ''),
      },
      {
        key: 'location-globe',
        icon: 'location-globe',
        title: t('preferred_location'),
        value: t(candidate.preferredLocation?.title ?? ''),
      },
      {
        key: 'relocation-marker',
        icon: 'relocation-marker',
        title: t('relocation'),
        value: t(candidate.relocation?.label ?? ''),
      },
      {
        key: 'salary-range',
        icon: 'salary-range',
        title: translateReplacer(
          t('expected_salary_range_rangename'),
          t(candidate.expectedSalaryPeriod?.label ?? '')
        ),
        value:
          min || max ? formatCurrency([min ?? 0, max ?? 0], code) : undefined,
      },
      {
        key: 'calculator',
        icon: 'calculator',
        title: t('tax_term'),
        value: t(candidate.expectedTaxTerm?.label ?? ''),
      },
      {
        key: 'money-increase',
        icon: 'money-increase',
        title: t('mark_up_percent'),
        value: candidate.expectedMarkup ? `${candidate.expectedMarkup} %` : '',
      },
    ];
  }, [candidate, t]);

  const visibleItems = useMemo(
    () => items.filter((item) => !!item.value),
    [items]
  );

  if (!visibleItems.length) return null;

  return (
    <SectionLayout title={t('expectations')} visibleActionButton>
      <InfoCardList items={visibleItems} />
    </SectionLayout>
  );
};

export default CandidateExpectationsSection;
