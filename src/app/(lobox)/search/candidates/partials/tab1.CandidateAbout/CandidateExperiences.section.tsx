import { useMemo, type FC } from 'react';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import AdvancedCardList from '@shared/components/Organism/AdvancedCardList/AdvancedCardList.component';
import {
  addDisplayDuration,
  experienceNormalizer,
} from '@shared/utils/experience.utils';
import useTranslation from '@shared/utils/hooks/useTranslation';

import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateExperiencesSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const experiences = useMemo(
    () =>
      candidate?.profile?.experiences?.map((item) => ({
        ...item,
        workPlaceTypeLabel: item.workPlaceType
          ? t(item.workPlaceType)
          : undefined,
        companyPageId:
          item?.companyPageId ||
          `${item.companyName?.trim().toLowerCase()}_temp`,
      })),
    [candidate?.profile?.experiences]
  );

  const professionalExperiences = useMemo(
    () => experiences?.reduce(experienceNormalizer, []).map(addDisplayDuration),
    [experiences]
  );

  if (!experiences?.length) return null;

  return (
    <AboutSectionLayout
      data={professionalExperiences || []}
      title={t('experience')}
    >
      {(props) => <AdvancedCardList {...props} />}
    </AboutSectionLayout>
  );
};

export default CandidateExperiencesSection;
