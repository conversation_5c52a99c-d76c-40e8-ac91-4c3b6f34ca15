import EntityId from '@shared/uikit/EntityId';
import type { FC } from 'react';
import classes from './CandidateAbout.module.scss';
import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateIdSection: FC<BaseCandidateSectionProp> = ({ candidate }) => (
  <EntityId
    className={classes.candidateIdWrap}
    type="candidate"
    id={candidate.id}
    name="profile_id"
    alertText="t_c_i_h_b_c_t_y_c"
  />
);

export default CandidateIdSection;
