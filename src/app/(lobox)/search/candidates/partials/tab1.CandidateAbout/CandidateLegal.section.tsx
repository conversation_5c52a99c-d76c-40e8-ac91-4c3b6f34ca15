import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useMemo, type FC } from 'react';
import Avatar from '@shared/uikit/Avatar';
import formatDate from '@shared/utils/toolkit/formatDate';
import type { InfoCardListItem } from '../components/InfoCardsList/InfoCardList';
import InfoCardList from '../components/InfoCardsList/InfoCardList';
import classes from './CandidateAbout.module.scss';
import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateLegalSection: FC<BaseCandidateSectionProp> = ({ candidate }) => {
  const { t } = useTranslation();
  const items = useMemo<InfoCardListItem[]>(
    () => [
      {
        key: 'user-document',
        icon: 'user-document',
        title: t('work_authorization'),
        value: t(candidate.workAuthorization?.label ?? ''),
      },
      {
        key: 'calendar-briefcase',
        icon: 'calendar-briefcase',
        title: t('work_authorization_expiry'),
        value: candidate.workAuthorizationExpiryDate
          ? formatDate(candidate.workAuthorizationExpiryDate, 'll')
          : '',
      },
      {
        key: 'visa_held_by',
        title: t('visa_held_by'),
        value: candidate.visaHeldByUser?.label,
        avatar: (
          <Avatar
            size="smd"
            imgSrc={candidate.visaHeldByUser?.image}
            className={classes.InfoCardAvatar}
          />
        ),
      },
    ],
    [candidate, t]
  );

  const visibleItems = useMemo(
    () => items.filter((item) => !!item.value),
    [items]
  );

  if (!visibleItems.length) return null;
  return (
    <SectionLayout title={t('legal_details')} visibleActionButton>
      <InfoCardList items={visibleItems} />
    </SectionLayout>
  );
};

export default CandidateLegalSection;
