import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useMemo, type FC } from 'react';
import Avatar from '@shared/uikit/Avatar';
import { formatPhone } from '@shared/utils/toolkit/formatPhone';
import type { InfoCardListItem } from '../components/InfoCardsList/InfoCardList';
import InfoCardList from '../components/InfoCardsList/InfoCardList';
import classes from './CandidateAbout.module.scss';
import {BaseCandidateSectionProp} from "@shared/types/candidates";

const CandidateReferralSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const items = useMemo<InfoCardListItem[]>(
    () => [
      {
        key: 'referred_by',
        title: t('referred_by'),
        value: candidate.referralUser?.label,
        avatar: (
          <Avatar
            size="smd"
            imgSrc={candidate.referralUser?.image}
            className={classes.InfoCardAvatar}
          />
        ),
      },
      {
        key: 'referral_current_company',
        title: t('referral_current_company'),
        value: candidate.referralCompany?.label,
        avatar: (
          <Avatar
            size="smd"
            imgSrc={candidate.referralCompany?.image}
            className={classes.InfoCardAvatar}
          />
        ),
      },
      {
        key: 'envelope',
        icon: 'envelope',
        title: t('referral_employee_email'),
        value: candidate.referralEmiil,
      },
      {
        key: 'phone',
        icon: 'phone',
        title: t('referral_employee_number'),
        value: formatPhone(candidate.referralPhone),
      },
      {
        key: 'link',
        icon: 'link',
        title: t('referring_social_url'),
        value: candidate.referralUrl,
      },
    ],
    [candidate, t]
  );

  const visibleItems = useMemo(
    () => items.filter((item) => !!item.value),
    [items]
  );

  if (!visibleItems.length) return null;

  return (
    <SectionLayout title={t('referral_information')} visibleActionButton>
      <InfoCardList items={visibleItems} />
    </SectionLayout>
  );
};

export default CandidateReferralSection;
