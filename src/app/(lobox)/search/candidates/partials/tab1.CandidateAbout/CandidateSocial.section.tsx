import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { formatPhone } from '@shared/utils/toolkit/formatPhone';
import { useMemo, type FC } from 'react';
import isEmpty from '@shared/utils/toolkit/isEmpty';
import type { InfoCardListItem } from '../components/InfoCardsList/InfoCardList';
import InfoCardList from '../components/InfoCardsList/InfoCardList';
import { BaseCandidateSectionProp } from '@shared/types/candidates';
import TextLink from '@shared/uikit/Link/TextLink';

const skypeDomain = 'https://join.skype.com/invite/';

const HyperLink: FC<{ href: string | undefined }> = ({ href }) => {
  return (
    <TextLink href={href} linkProps={{ target: '_blank' }}>
      {href}
    </TextLink>
  );
};

const CandidateSocialSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const items = useMemo<InfoCardListItem[]>(
    () => [
      {
        key: 'linkedin_profile_url',
        icon: 'linkedin-line',
        title: t('linkedin_profile_url'),
        value: candidate.linkedinUrl ? (
          <HyperLink href={candidate.linkedinUrl} />
        ) : null,
      },
      {
        key: 'facebook_profile_url',
        icon: 'facebook-line',
        title: t('facebook_profile_url'),
        value: candidate.facebookUrl ? (
          <HyperLink href={candidate.facebookUrl} />
        ) : null,
      },
      {
        key: 'x_profile_url',
        icon: 'x-twitter-line',
        title: t('x_profile_url'),
        value: candidate.twitterUrl ? (
          <HyperLink href={candidate.twitterUrl} />
        ) : null,
      },
      ...(Array.isArray(candidate.otherUrls)
        ? candidate.otherUrls
            .filter((i) => !isEmpty(i))
            .map((i, index) => ({
              key: `other_profile_url_${index}`,
              icon: 'link' as const,
              title: t('other_profile_url'),
              value: <HyperLink href={i} />,
            }))
        : []),
      {
        key: 'skype_id',
        icon: 'skype-line',
        title: t('skype_id'),
        value: candidate.skypeId ? (
          <HyperLink href={`${skypeDomain}${candidate.skypeId}`} />
        ) : null,
      },
      {
        key: 'cell_number',
        icon: 'phone',
        title: t('cell_number'),
        value: formatPhone(candidate.cellNumber),
      },
      {
        key: 'work_number',
        icon: 'phone',
        title: t('work_number'),
        value: formatPhone(candidate.workNumber),
      },
      {
        key: 'home_number',
        icon: 'phone',
        title: t('home_number'),
        value: formatPhone(candidate.homeNumber),
      },
    ],
    [candidate, t]
  );

  const visibleItems = useMemo(
    () => items.filter((item) => !isEmpty(item.value)),
    [items]
  );

  if (!visibleItems.length) return null;

  return (
    <SectionLayout title={t('social_information')} visibleActionButton>
      <InfoCardList items={visibleItems} />
    </SectionLayout>
  );
};

export default CandidateSocialSection;
