import BusinessJobCardSkeleton from '@shared/components/molecules/BusinessJobCard/BusinessJobCardSkeleton';
import Flex from '@shared/uikit/Flex/index';
import { type FC } from 'react';
import Skeleton from '@shared/uikit/Skeleton';
import classes from './CandidateJobs.module.scss';

const CandidateJobSkeleton: FC = () => (
  <Flex className={classes.section}>
    <Flex flexDir="row">
      <Skeleton className={classes.btnSkeleton} />
      <Skeleton className={classes.btnSkeleton} />
    </Flex>
    <BusinessJobCardSkeleton showTags />
    <BusinessJobCardSkeleton showTags />
    <BusinessJobCardSkeleton showTags />
  </Flex>
);

export default CandidateJobSkeleton;
