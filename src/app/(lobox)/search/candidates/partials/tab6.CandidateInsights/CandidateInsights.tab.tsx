import { type FC } from 'react';
import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import useTranslation from '@shared/utils/hooks/useTranslation';
import ComingSoon from '@shared/svg/ComingSoon';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import Flex from '@shared/uikit/Flex/index';
import classes from './CandidateInsights.module.scss';
import {BaseCandidateSectionProp} from "@shared/types/candidates";

export const CandidateInsightsTab: FC<BaseCandidateSectionProp> = (props) => {
  const { t } = useTranslation();
  return (
    <Flex className={classes.root}>
      <EmptySectionInModules
        title={t('coming_3dot')}
        text={t('w_r_w_o_it')}
        image={<ComingSoon />}
      />
    </Flex>
  );
};
