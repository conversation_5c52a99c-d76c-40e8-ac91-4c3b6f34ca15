import React, { useEffect, useState } from 'react';
import collectionToObjectByKey from '@shared/utils/toolkit/collectionToObjectByKey';
import { db } from '@shared/utils/constants/enums';
import useTranslation from '@shared/utils/hooks/useTranslation';
import RightSearchResultSkeleton from '@app/search/partials/Skeletons/RightSearchResultSkeleton';
import Tabs from '@shared/components/Organism/Tabs';
import CandidateCard from '@shared/components/molecules/CandidateCard';
import { CompanyType, ECompanyRole } from '@shared/types/company';
import { CompanyTab } from '@shared/types/company';
import dynamic from 'next/dynamic';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import QueryKeys from '@shared/utils/constants/queryKeys';
import { getCompanyDetailInVendorClientByPageId } from '@shared/utils/api/company';
import EmptyState from '@shared/components/Organism/EmptyState/EmptyState';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import EmptySearchSvg from '@shared/svg/EmptySearchSvg';
import noCacheQueryClientOptions from '@shared/utils/constants/noCacheQueryClientOptions';
import { ClientVendorPendingBadge } from './partials/components/ClientVendorPendingBadge';
import CompanyDetailsTabSkeleton from './partials/components/CompanyDetailsTab/CompanyDetailsTab.skeleton';
import CompanyActionButton from './partials/components/CompanyActionButton';
import CompanyCardBadges from './partials/components/CompanyCardBadges';
import classes from './SearchPages.details.module.scss';

const CompanyDetailsTab = dynamic(
  () => import('./partials/components/CompanyDetailsTab'),
  { loading: () => <CompanyDetailsTabSkeleton /> }
);

const pagesCategories = collectionToObjectByKey(db.CATEGORY_TYPES);

const tabs = [
  {
    path: 'details',
    title: 'details',
  },
  // {
  //   path: 'jobs',
  //   title: 'jobs',
  // },
  // {
  //   path: 'candidates',
  //   title: 'candidates',
  // },
  // {
  //   path: 'collaborators',
  //   title: 'collaborators',
  // },
  // {
  //   path: 'activities',
  //   title: 'activities',
  // },
  // {
  //   path: 'insights',
  //   title: 'insights',
  // },
];

interface SearchPagesDetailsProps {
  isLoading?: boolean;
  activeTab: CompanyTab;
  currentEntityId: string;
  params: Record<string, string>;
  myCompanyRole: ECompanyRole;
}

const SearchPagesDetails: React.FC<SearchPagesDetailsProps> = ({
  isLoading,
  activeTab,
  params,
  currentEntityId,
  myCompanyRole,
}) => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState('details');

  useEffect(() => {
    setSelectedTab('details');
  }, [currentEntityId]);

  const {
    isFetching,
    data: company,
    refetch,
  } = useReactQuery<CompanyType>({
    action: {
      key: [QueryKeys.companyDetail, params],
      apiFunc: getCompanyDetailInVendorClientByPageId,
      // spreadParams: true,
      params,
    },
    config: {
      ...noCacheQueryClientOptions,
      enabled: !!currentEntityId,
    },
  });

  if (isFetching || isLoading) {
    return <RightSearchResultSkeleton page />;
  }
  if (!company) {
    return (
      <EmptyState
        image={<EmptySearchSvg />}
        captionProps={{ color: 'smoke_coal', size: 20, font: '700' }}
        caption={translateReplacer(t('no_name_found'), [
          myCompanyRole.toLowerCase(),
        ])}
        message={translateReplacer(t('there_are_no_name_to_show'), [
          myCompanyRole.toLowerCase(),
        ])}
        className="h-full flex justify-center items-center"
      />
    );
  }

  return (
    <>
      <CandidateCard
        avatar={company.croppedImageUrl}
        firstText={company.title}
        secondText={company.usernameAtSign}
        thirdText={t(pagesCategories[company.category?.value]?.label)}
        fourthText={company.location?.label}
        isPage
        footer={
          <Tabs
            activePath={selectedTab}
            onChangeTab={setSelectedTab}
            styles={{
              tabsRoot: classes.tabsRoot,
            }}
            tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
          />
        }
        topRightActionComponent={
          activeTab === CompanyTab.REQUESTS ? (
            <ClientVendorPendingBadge
              isVendorPending={company.vendorStatus === 'REQUESTED'}
            />
          ) : undefined
        }
      >
        <CompanyActionButton
          company={company}
          role={myCompanyRole}
          activeTab={activeTab}
          refetchDetails={refetch}
          badges={
            activeTab === CompanyTab.CLIENTS ||
            activeTab === CompanyTab.VENDORS ? (
              <CompanyCardBadges
                jobCount={company.jobCount}
                candidateCount={company.candidateCount}
                collaboratorCount={company.collaboratorCount}
              />
            ) : undefined
          }
          currentEntityId={currentEntityId}
        />
      </CandidateCard>
      <Panels company={company} tab={selectedTab} />
    </>
  );
};

export default SearchPagesDetails;

const Panels = ({ tab, company }) => {
  switch (tab) {
    case 'jobs': {
      return 'jobs';
    }
    default: {
      return <CompanyDetailsTab company={company} />;
    }
  }
};
