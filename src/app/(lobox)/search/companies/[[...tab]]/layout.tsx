'use client';

import type { PropsWithChildren } from 'react';
import React from 'react';
import dynamic from 'next/dynamic';
import { useSearchState } from '@shared/contexts/search/search.provider';

const AddCompanyModal = dynamic(
  () => import('./partials/components/AddCompanyModal'),
  { ssr: false }
);

export default function Layout({ children }: PropsWithChildren) {
  const isAddCompanyModalOpen = useSearchState('addCompanyModalData')?.isOpen;

  return (
    <>
      {children}
      {isAddCompanyModalOpen && <AddCompanyModal />}
    </>
  );
}
