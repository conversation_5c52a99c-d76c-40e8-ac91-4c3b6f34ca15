import CardBadge from '@shared/components/molecules/CardBadge';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Flex from '@shared/uikit/Flex';

const CompanyCardBadges = ({ jobCount, candidateCount, collaboratorCount }) => {
  const { t } = useTranslation();

  return (
    <Flex flexDir="row" className="gap-8">
      <CardBadge
        value={jobCount ?? '0'}
        iconsDetails={{ iconName: 'briefcase' }}
        tooltipProps={{
          children: t('jobs'),
        }}
      />
      <CardBadge
        value={collaboratorCount ?? '0'}
        iconsDetails={{ iconName: 'new-person' }}
        tooltipProps={{
          children: t('collaborators'),
        }}
      />
      <CardBadge
        value={candidateCount ?? '0'}
        iconsDetails={{ iconName: 'meeting' }}
        tooltipProps={{
          children: t('candidates'),
        }}
      />
    </Flex>
  );
};

export default CompanyCardBadges;
