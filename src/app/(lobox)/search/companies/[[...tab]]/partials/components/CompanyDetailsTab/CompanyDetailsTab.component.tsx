import React from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Flex from '@shared/uikit/Flex';
import CopyEntityId from 'shared/components/molecules/CopyEntityId';
import OverView from './Overview/Overview.component';
import ContactInfo from './ContactInfo/ContactInfo.component';
import Location from './Location/Location.component';

interface Props {
  company: string;
}

const CompanyDetailsTab: React.FC<Props> = ({ company }) => {
  const { t } = useTranslation();

  return (
    <Flex className="pb-32">
      <OverView company={company} />
      <ContactInfo company={company} />
      <Location company={company} />
      <CopyEntityId
        title={t('company_id')}
        entityId={company.id}
        tooltip={t('y_c_search_th_company_id')}
      />
    </Flex>
  );
};

export default CompanyDetailsTab;
