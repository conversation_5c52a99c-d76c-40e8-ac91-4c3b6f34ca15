import React from 'react';
import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import Skeleton from '@shared/uikit/Skeleton';

const CompanyDetailsTabSkeleton: React.FC = () => (
  <SectionLayout
    title={<Skeleton className="!w-[137px] !h-[24px]" />}
    classNames={{ childrenWrap: '!p-0 !rounded-md' }}
  >
    <Skeleton className="h-[300px] w-full rounded-md" />
  </SectionLayout>
);

export default CompanyDetailsTabSkeleton;
