@import '/src/shared/theme/theme.scss';

@layer organism {
  .childrenWrap {
    padding: 0;
    background-color: colors(background);
    overflow: hidden;
  }
  .zoomWrapper {
    position: absolute;
    right: 24px;
    top: variables(largeGutter);
  }
  .plusIcon {
    margin-right: variables(largeGutter);
  }

  .addressWrapper {
    padding: 12px 0;
    gap: 12px;
  }

  @media (min-width: breakpoints(tablet)) {
    .childrenWrap {
      border-radius: 12px;
      overflow: hidden;
    }
  }
}
