import React from 'react';
import type { Metadata } from 'next/types';
import SearchJobs from './partials/components/SearchJobs.component';
import fetchJobMetadata from './partials/utils/fetchMetadata';

interface JobsSearchParams {
  currentEntityId: string;
  searchEntity: 'jobs' | 'posts';
}

interface SearchJobsProps {
  searchParams: Promise<JobsSearchParams>;
}

export async function generateMetadata({
  searchParams,
}: SearchJobsProps): Promise<Metadata> {
  try {
    const { currentEntityId } = await searchParams;
    return await fetchJobMetadata(currentEntityId);
  } catch {
    return {};
  }
}

export default function SearchJobPage({ searchParams }: SearchJobsProps) {
  return <SearchJobs />;
}
