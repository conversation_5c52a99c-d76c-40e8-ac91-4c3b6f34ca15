import React, { type FC } from 'react';
import JobElementHeaderSkeleton from '@app/jobs/partials/components/JobElement/JobElementHeader/JobElementHeader.skeleton';
import JobsDetails from '@app/jobs/partials/components/JobsDetails';
import useJobElement from '@app/jobs/partials/hooks/useJobElement';
import Flex from '@shared/uikit/Flex/index';
import SectionSkeleton from '@shared/components/molecules/Section/Section.skeleton';
import Skeleton from '@shared/uikit/Skeleton';

type Props = { isFetching: boolean; currentEntityId?: string };

const SearchJobsDetails: FC<Props> = ({ isFetching, currentEntityId }) => {
  const { queryResult } = useJobElement();
  if (queryResult.isError) return null;

  return isFetching || queryResult.isFetching || !currentEntityId ? (
    <Flex className="h-full">
      <JobElementHeaderSkeleton isJob />
      <SectionSkeleton hasTitle>
        <Skeleton className="h-[300px] w-full rounded-md" />
      </SectionSkeleton>
      <SectionSkeleton hasTitle>
        <Skeleton className="h-[300px] w-full rounded-md" />
      </SectionSkeleton>
    </Flex>
  ) : (
    <JobsDetails isLoggedIn />
  );
};

export default SearchJobsDetails;
