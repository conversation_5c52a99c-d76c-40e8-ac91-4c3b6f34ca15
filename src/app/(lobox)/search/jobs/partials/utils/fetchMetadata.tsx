import appEnvironment from 'shared/utils/constants/env';
import textTruncate from 'shared/uikit/utils/textTruncate';
import { getJobDetails } from 'shared/utils/api/jobs';
import { routeNames } from 'shared/utils/constants/routeNames';
import applyResolutionToImageSrc from 'shared/utils/toolkit/applyResolutionToImageSrc';
import getAccessTokenFromCookies from 'shared/utils/getAccessTokenFromCookies';
import type { Metadata } from 'next';

export default async function fetchJobMetadata(
  jobId?: string
): Promise<Metadata> {
  if (!jobId)
    return {
      title: {
        default: 'Search Jobs',
        template: '%s | Lobox',
      },
      robots: {
        index: true,
        follow: true,
        nocache: true,
      },
    };

  const accessToken = await getAccessTokenFromCookies();
  const job = await getJobDetails({
    params: { id: jobId },
    headers: { accessToken },
  });

  const { id, title, description, category, pageCroppedImageUrl } = job;
  const safeTitle = title?.label ?? '';
  const safeDescription =
    (description || category?.label || '')?.replace(/<[^>]+>/g, '') ?? '';
  const metaDescription = textTruncate(safeDescription, 35);
  const metaTitle = textTruncate(safeTitle, 70);

  const image = pageCroppedImageUrl
    ? applyResolutionToImageSrc(pageCroppedImageUrl, 'medium')
    : undefined;

  const pathname = appEnvironment.baseUrl + routeNames.jobDetails.makeRoute(id);

  return {
    title: {
      default: metaTitle ?? 'Search Jobs',
      template: '%s | Lobox',
    },
    description: metaDescription,
    alternates: {
      canonical: pathname,
    },
    openGraph: {
      title: title?.label ?? '',
      description,
      images: image,
      url: pathname,
      type: 'website',
    },
    robots: {
      index: true,
      follow: true,
      nocache: true,
    },
    twitter: {
      card: 'summary_large_image',
      site: pathname,
      title: title?.label ?? '',
      description,
      images: image,
    },
  };
}
