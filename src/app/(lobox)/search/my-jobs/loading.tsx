'use client';

import React from 'react';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import Skeleton from 'shared/uikit/Skeleton';
import cnj from 'shared/uikit/utils/cnj';
import SearchListItemSkeleton from 'shared/components/Organism/SearchList/SearchListItem.skeleton';
import RightSearchResultSkeleton from '../partials/Skeletons/RightSearchResultSkeleton';
import SearchListHeaderSkeleton from '../partials/Skeletons/SearchListHeaderSkeleton';
import classes from './loading.module.scss';

const list = Array(6).fill(0);

export default function loading() {
  return (
    <>
      <Flex className={classes.linksRootShrink}>
        <Flex className={classes.searchFilterContent}>
          <IconButton
            name="chevron-left"
            type="far"
            className={classes.backBtn}
          />
          {list.map((_, i) => (
            <Skeleton key={`jobs_empty_${i}`} className={classes.skeleton} />
          ))}
          <Skeleton className={classes.modalButtonSkeleton} />
        </Flex>
      </Flex>
      <Flex className={cnj(classes.contentRoot, classes.maxWidth)}>
        <Flex className={classes.jobsListWithDetails}>
          <Flex className={classes.list}>
            <Flex className={classes.wrapper}>
              <SearchListHeaderSkeleton />
              {Array(6)
                .fill(0)
                ?.map((_, i) => (
                  <SearchListItemSkeleton type="people" key={`item_${i}`} />
                ))}
            </Flex>
          </Flex>
          <Flex className={classes.details}>
            <RightSearchResultSkeleton />
          </Flex>
        </Flex>
      </Flex>
    </>
  );
}
