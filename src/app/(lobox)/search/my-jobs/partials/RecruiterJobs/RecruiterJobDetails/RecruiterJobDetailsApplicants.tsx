// TODO refactor this file to use a normal List component instead of a SearchList;

import { useCallback, type FC } from 'react';
import JobCandidateCard from '@shared/components/molecules/JobCandidateCard/JobCandidateCard';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import { getJobApplications } from '@shared/utils/api/jobs';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import SearchList from 'shared/components/Organism/SearchList';
import useChangePipeline from 'shared/hooks/useChangePipeline';
import { QueryKeys } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import RecruiterJobDetailsSkeleton from './RecruiterJobDetailsSkeleton';
import classes from './RecruiterJobDetailsStyles.module.scss';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import type {
  ApplicationProps,
  SingleJobAPIProps,
} from 'shared/types/jobsProps';

interface RecruiterJobDetailsApplicantsProps {
  job: SingleJobAPIProps;
}

const RecruiterJobDetailsApplicants: FC<RecruiterJobDetailsApplicantsProps> = ({
  job,
}) => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();

  const { data, isLoading, totalElements, refetch, totalPages } =
    useReactInfiniteQuery<ApplicationProps>(
      [QueryKeys.jobApplications, job.id],
      {
        func: getJobApplications,
        size: 10,
        extraProps: {
          id: job.id,
        },
      }
    );

  const { onChangePipeline } = useChangePipeline({
    onSuccess: refetch,
    variant: 'applicant',
  });

  const handleOpenManagerModal = useCallback(
    (candidateId: string, tab: CandidateManagerTabkeys) => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          tab,
          id: candidateId,
          enableNavigate: false,
        },
      });
    },
    [appDispatch]
  );

  if (isLoading) return <RecruiterJobDetailsSkeleton />;

  return (
    <SearchList
      entity="recruiterJobs"
      isLoading={isLoading}
      totalElements={Number(totalElements)}
      data={data ?? []}
      onPageChange={() => {}}
      totalPages={Number(totalPages)}
      className={{ root: classes.applicantsRoot }}
      renderItem={(application) => (
        <JobCandidateCard
          data={application}
          showActions
          variant="applicant"
          showTags
          onChangePipeline={(pipeline) =>
            onChangePipeline({
              userId: application.id,
              pipelineId: pipeline.id as string,
            })
          }
          key={`application_${application.id}`}
          onPrimaryButtonClick={() => {
            handleOpenManagerModal(application.applicant.id, 'notes');
          }}
        />
      )}
      emptyList={
        <EmptySearchResult
          title={t('no_applicants_found')}
          sectionMessage={t('no_applicants_found_desc')}
          className={classes.emptyResult}
          classNames={{ description: '!mt-12' }}
        />
      }
      // parentPage={0}
      noItemButtonAction
      innerList
    />
  );
};

export default RecruiterJobDetailsApplicants;
