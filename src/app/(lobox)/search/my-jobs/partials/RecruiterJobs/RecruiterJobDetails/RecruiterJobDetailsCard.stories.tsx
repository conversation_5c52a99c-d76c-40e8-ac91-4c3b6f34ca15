import type { ArgTypes, Meta, StoryObj } from '@storybook/react';
import type { ComponentProps } from 'react';
import { BusinessJobCardSkeleton } from '@shared/components/molecules/BusinessJobCard';
import RecruiterJobDetailsCard from './RecruiterJobDetailsCard';

type RecruiterJobDetailsCardProps = ComponentProps<
  typeof RecruiterJobDetailsCard
>;

const meta: Meta<RecruiterJobDetailsCardProps> = {
  title: 'Portals/Lobox Recruiter/Jobs/Card',
  component: RecruiterJobDetailsCard,
  subcomponents: { BusinessJobCardSkeleton },
} satisfies Meta<RecruiterJobDetailsCardProps>;

export default meta;
type Story = StoryObj<typeof meta>;

const defaultProps: Partial<RecruiterJobDetailsCardProps> = {
  job: {
    id: '5',
    title: 'Senior Frontend Developer',
    pageTitle: 'varcel',
    categoryName: 'IT Developement Management',
    projects: [
      { title: 'Lobox' },
      { title: 'Recruitment Team' },
      { title: 'User Management' },
    ] as any[],
    createdDate: '2024-01-25T15:17',
    creatorUser: {
      name: '<PERSON><PERSON><PERSON>',
      username: 'mohsen_lotfi',
    } as any,
    status: 'OPEN',
    priority: 'HIGH',
    tags: ['React', 'Storybook'],
    location: { title: 'Ankara, Turkey' },
    applicantsCount: '35',
    collaborators: [{}, {}, {}] as any,
    candidatesCount: '16',
    lastModifiedDate: '2024-12-25T15:17',
    pageCroppedImageUrl:
      'https://images.unsplash.com/photo-1516880711640-ef7db81be3e1?w=496&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dGVhbXxlbnwwfHwwfHx8Mg%3D%3D',
  } as any,
  cardProps: { classNames: { root: '!w-[726px]' } },
  editableTags: false,
};

const defaultArgTypes: Partial<ArgTypes<RecruiterJobDetailsCardProps>> = {
  job: {
    control: false,
  },
  cardProps: {
    control: false,
  },
  selectedTab: {
    control: false,
  },
};

export const MainCard: Story = {
  args: defaultProps,
  argTypes: defaultArgTypes,
};

export const SkeletonMainCard: Story = {
  render: () => (
    <BusinessJobCardSkeleton showTags className="!w-[726px]" isMain />
  ),
  argTypes: defaultArgTypes,
};
