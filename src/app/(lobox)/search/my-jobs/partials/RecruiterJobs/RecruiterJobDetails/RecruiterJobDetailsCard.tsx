import BaseBusinessJobCard from 'shared/components/molecules/BusinessJobCard/BaseBusinessJobCard';
import type { SingleJobAPIProps } from 'shared/types/jobsProps';
import type { FC } from 'react';
import Tabs from 'shared/components/Organism/Tabs';
import useTranslation from 'shared/utils/hooks/useTranslation';
import BusinessJobCardActions from '@shared/components/molecules/BusinessJobCard/BusinessJobCardActions';
import type { CardWrapperProps } from '@shared/components/molecules/CardItem/CardWrapper';
import cnj from '@shared/uikit/utils/cnj';
import HorizontalTagList from '@shared/components/molecules/HorizontalTagList';
import { updateJobTags } from '@shared/utils/api/jobs';
import useAccessibility from '@shared/hooks/useAccessibility';
import classes from './RecruiterJobDetailsStyles.module.scss';
import RecruiterJobDetailsCardMoreOptions from './RecruiterJobDetailsCard/RecruiterJobDetailsCardMoreOptions';

interface RecruiterJobDetailsCardProps {
  job: SingleJobAPIProps;
  onChangeTab: (tab: string) => void;
  selectedTab: string;
  cardProps?: CardWrapperProps;
  refetchJobData: VoidFunction;
  editableTags?: boolean;
}

const RecruiterJobDetailsCard: FC<RecruiterJobDetailsCardProps> = (props) => {
  const {
    job,
    onChangeTab,
    selectedTab,
    cardProps,
    refetchJobData,
    editableTags = true,
  } = props;
  const { t } = useTranslation();
  const { accessName } = useAccessibility();

  const projectsName = job.projects?.map((item) => item.title).join(', ');

  return (
    <BaseBusinessJobCard
      id={job.id}
      image={job.pageCroppedImageUrl}
      title={job.title}
      username={job.pageTitle}
      category={job.categoryName}
      location={job.location?.title ?? ''}
      creator={{
        name: job.ownerFullName,
        username: job.ownerUsername,
        role: accessName({
          portalAccesses: job.ownerPortalAccesses,
        } as any),
      }}
      projects={projectsName}
      createdAt={job.createdDate}
      status={job.status}
      priority={job.priority}
      collaboratorsCount={job.collaborators.length}
      applicantsCount={Number(job.applicantsCount)}
      candidatesCount={Number(job.candidatesCount)}
      tags={
        <HorizontalTagList
          tags={job.tags}
          title={t('job_tags')}
          editable={editableTags}
          onSuccess={refetchJobData}
          apiFunc={(body) => updateJobTags(job.id, body.tags)}
        />
      }
      cardProps={{
        ...cardProps,
        classNames: {
          ...cardProps?.classNames,
          root: cnj(classes.root, cardProps?.classNames?.root),
        },
      }}
      actions={<BusinessJobCardActions job={job} />}
      moreOptions={<RecruiterJobDetailsCardMoreOptions job={job} />}
      lastUpdate={job.lastModifiedDate}
      badgeActions={{
        onApplicantsClick: () => onChangeTab('applicants'),
        onCandidatesClick: () => onChangeTab('candidates'),
        onAssigneesClick: () => onChangeTab('assignees'),
      }}
    >
      <Tabs
        activePath={selectedTab}
        onChangeTab={onChangeTab}
        styles={{
          tabsRoot: classes.tabsRoot,
          linksRoot: classes.linksRoot,
        }}
        tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
      />
    </BaseBusinessJobCard>
  );
};

export default RecruiterJobDetailsCard;

const tabs = [
  {
    path: 'about',
    title: 'details',
  },
  {
    path: 'applicants',
    title: 'applicants',
  },
  {
    path: 'candidates',
    title: 'candidates',
  },
  {
    path: 'assignees',
    title: 'assignees',
  },
  {
    path: 'reviews',
    title: 'reviews',
  },
  {
    path: 'activities',
    title: 'activities',
  },
  {
    path: 'insights',
    title: 'insights',
  },
] as const;
