import SectionLayout from 'shared/components/Organism/Objects/Common/Section.layout';
import SearchCard from 'shared/components/Organism/SearchCard';
import type { JobCollaboratorsProps } from 'shared/types/jobsProps';
import Flex from 'shared/uikit/Flex';
import { useQuery } from '@tanstack/react-query';
import { useCallback, type FC } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { QueryKeys } from 'shared/utils/constants';
import Grid from 'shared/uikit/Grid';
import { getCollaborators } from '@shared/utils/api/jobs';
import AssigneeNetworkButtons from '@shared/components/molecules/AssigneeNetworkButtons';
import useAccessibility from '@shared/hooks/useAccessibility';
import RecruiterJobDetailsCollaboratorsSkeleton from './RecruiterJobDetailsCollaborators/RecruiterJobDetailsCollaboratorsSkeleton';
import classes from './RecruiterJobDetailsStyles.module.scss';
import AssigneeCard from './RecruiterJobDetailsCollaborators/AssigneeCard';

interface RecruiterJobDetailsCollaboratorsProps {
  jobId: string;
}

const RecruiterJobDetailsCollaborators: FC<
  RecruiterJobDetailsCollaboratorsProps
> = ({ jobId }) => {
  const { t } = useTranslation();
  const { accessName } = useAccessibility();
  const { data, isLoading } = useQuery<JobCollaboratorsProps>({
    queryKey: [QueryKeys.jobCollaborators, jobId],
    queryFn: (props) => getCollaborators(props.queryKey[1] as string),
  });

  const NetworkButtons = useCallback(
    (collab: any) => <AssigneeNetworkButtons user={collab} />,
    []
  );

  if (isLoading) return <RecruiterJobDetailsCollaboratorsSkeleton />;

  return (
    <Flex>
      <SectionLayout
        title={t('point_of_contact')}
        classNames={{ childrenWrap: classes.sectionLayout }}
      >
        {data?.pointOfContact && (
          <AssigneeCard assignee={data.pointOfContact} />
        )}
      </SectionLayout>
      {!!data?.collaborators.length && (
        <SectionLayout
          title={t('recruitment_team')}
          classNames={{ childrenWrap: classes.sectionLayout }}
        >
          <Grid container spacing={2}>
            {data?.collaborators.map((collab) => (
              <Grid size={6} key={`collaborator_${collab.id}`}>
                <AssigneeCard
                  assignee={collab}
                  assigneesList={[...data.collaborators, data.pointOfContact]}
                />
              </Grid>
            ))}
          </Grid>
        </SectionLayout>
      )}
    </Flex>
  );
};

export default RecruiterJobDetailsCollaborators;
