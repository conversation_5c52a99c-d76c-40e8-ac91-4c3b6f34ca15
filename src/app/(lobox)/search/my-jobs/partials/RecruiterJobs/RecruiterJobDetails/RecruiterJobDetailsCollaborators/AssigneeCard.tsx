import SearchCard from '@shared/components/Organism/SearchCard/SearchCard.component';
import type { FC } from 'react';
import { useCallback, useState } from 'react';
import type { JobDetailsCollaboratorProps } from '@shared/types/jobsProps';
import AssigneeNetworkButtons from '@shared/components/molecules/AssigneeNetworkButtons';
import useAccessibility from '@shared/hooks/useAccessibility';
import AssigneesMoreOptions from './AssigneesMoreOptions';
import classes from '../RecruiterJobDetailsStyles.module.scss';

interface AssigneeCardProps {
  assigneesList?: JobDetailsCollaboratorProps[];
  assignee: JobDetailsCollaboratorProps;
}

const AssigneeCard: FC<AssigneeCardProps> = (props) => {
  const { assignee, assigneesList } = props;
  const [isHovering, setIsHovering] = useState(false);
  const { accessName } = useAccessibility();
  const NetworkButtons = useCallback(
    (collab: any) => <AssigneeNetworkButtons user={collab} />,
    []
  );
  const onMouseEnter = () => {
    setIsHovering(true);
  };
  const onMouseLeave = () => {
    setIsHovering(false);
  };
  return (
    <SearchCard
      imgSrc={assignee?.croppedImageUrl as string}
      firstText={assignee?.fullName as string}
      secondText={accessName(assignee as any)}
      thirdText={assignee?.occupation?.label as string}
      fourthText={assignee?.location.title as string}
      isHoverAble={false}
      bottomComponent={() => NetworkButtons(assignee)}
      id={assignee?.id}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      topRightActionComponent={
        isHovering ? (
          <AssigneesMoreOptions
            id={assignee?.id}
            assignee={assignee}
            assigneesList={assigneesList}
          />
        ) : undefined
      }
      classNames={{
        bottomWrapper: classes.collaboratorBtnsWrapper,
        wrapper: '!pr-0',
      }}
    />
  );
};

export default AssigneeCard;
