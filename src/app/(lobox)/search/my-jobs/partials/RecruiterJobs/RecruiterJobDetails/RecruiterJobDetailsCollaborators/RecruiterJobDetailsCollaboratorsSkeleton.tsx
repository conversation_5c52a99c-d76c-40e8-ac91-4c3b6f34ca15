import classes from './RecruiterJobDetailsCollaboratorsSkeleton.module.scss';
import SearchListItemSkeleton from 'shared/components/Organism/SearchList/SearchListItem.skeleton';
import Flex from 'shared/uikit/Flex';
import Grid from 'shared/uikit/Grid';
import Skeleton from 'shared/uikit/Skeleton';

const skeletons = Array.from({ length: 10 }, (_, i) => i);

const RecruiterJobDetailsCollaboratorsSkeleton = () => {
  return (
    <Flex>
      <Skeleton className={classes.title} />
      <SearchListItemSkeleton type={'people'} />
      <Skeleton className={classes.title} />
      <Grid container spacing={2}>
        {skeletons?.map((item: number) => (
          <Grid size={6} key={`collaborator_skeleton_${item}`}>
            <SearchListItemSkeleton key={`collab_${item}`} type={'people'} />
          </Grid>
        ))}
      </Grid>
    </Flex>
  );
};

export default RecruiterJobDetailsCollaboratorsSkeleton;
