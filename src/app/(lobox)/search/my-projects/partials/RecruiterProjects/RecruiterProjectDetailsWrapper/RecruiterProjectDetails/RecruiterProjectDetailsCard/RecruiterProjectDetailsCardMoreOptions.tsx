import useRecruiterProjectMoreActions from '@shared/hooks/useRecruiterProjectMoreActions';
import type { ProjectProps } from '@shared/types/project';
import IconButton from '@shared/uikit/Button/IconButton';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FC } from 'react';

interface RecruiterProjectDetailsCardMoreOptionsProps {
  project: ProjectProps;
}

const RecruiterProjectDetailsCardMoreOptions: FC<
  RecruiterProjectDetailsCardMoreOptionsProps
> = ({ project }) => {
  const { t } = useTranslation();
  const { actions, onAction } = useRecruiterProjectMoreActions();
  return (
    <PopperMenu
      placement="bottom-end"
      closeOnScroll
      buttonComponent={<IconButton type="fas" name="ellipsis-h" size="md" />}
    >
      {actions.map((item) => (
        <PopperItem
          key={`job_option_${item.label}`}
          onClick={() => onAction(item.label, project)}
          iconName={item.icon}
          iconType="far"
          label={t(item.label)}
          iconSize={20}
        />
      ))}
    </PopperMenu>
  );
};

export default RecruiterProjectDetailsCardMoreOptions;
