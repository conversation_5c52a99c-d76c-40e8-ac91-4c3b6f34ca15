import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import ComingSoon from '@shared/svg/ComingSoon';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FC } from 'react';
import classes from './RecruiterProjectDetailsStyles.module.scss';

interface RecruiterProjectDetailsInsightsProps {}

const RecruiterProjectDetailsInsights: FC<
  RecruiterProjectDetailsInsightsProps
> = () => {
  const { t } = useTranslation();
  return (
    <EmptySectionInModules
      title={t('coming_3dot')}
      text={t('w_r_w_o_it')}
      image={<ComingSoon />}
      classNames={{ container: classes.sectionRoot }}
    />
  );
};

export default RecruiterProjectDetailsInsights;
